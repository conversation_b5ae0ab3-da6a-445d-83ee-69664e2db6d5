import os
from duffle_path import (
    get_dao_provider_file,
    get_module_dir,
    get_template_dir,
)


DAO_FILE = ["TempCategoryDao.kt", "TempDao.kt"]
REPO_PACKAGE = "com.pixocial.business.duffle.repo"


def process_dao_provider(m_import, m_function):
    provider_file = get_dao_provider_file()
    with open(provider_file, "r") as f:
        content = f.read()

    # 提取 package 声明
    package_line = ""
    lines = content.splitlines()
    if lines[0].startswith("package "):
        package_line = lines[0]
        content = "\n".join(lines[1:])

    # 提取并清理 import 语句
    import_lines = [line for line in content.splitlines() if line.startswith("import ")]
    import_lines.append(m_import)
    import_lines = sorted(set(import_lines))

    # 提取其他行
    other_lines = [
        line for line in content.splitlines() if not line.startswith("import ")
    ]
    other_lines = other_lines[
        next((i for i, line in enumerate(other_lines) if line.strip()), 0) :
    ]

    # 重新组合内容
    content = (
        f"{package_line}\n\n"
        + "\n".join(import_lines)
        + "\n\n"
        + "\n".join(other_lines)
    )

    # 加入函数
    if m_function not in content:
        last_brace_index = content.rfind("}")
        if last_brace_index != -1:
            content = (
                content[:last_brace_index]
                + f"\n    {m_function}\n"
                + content[last_brace_index:]
            )

    with open(provider_file, "w") as f:
        f.write(content)


def process_db(module_name):
    package_name = module_name.lower()

    for file in DAO_FILE:
        dao_file = os.path.join(get_template_dir(), file)
        with open(dao_file, "r") as f:
            content = f.read()
            content = (
                content.replace(r"{package_name}", package_name)
                .replace(r"{module_name}", module_name)
                .replace(r"{table}", package_name)
            )
            with open(
                f"{get_module_dir(package_name)}/{file.replace('Temp', module_name)}",
                "w",
            ) as w:
                w.write(content)

    m_import = f"import {REPO_PACKAGE}.{package_name}.{module_name}Dao"
    m_function = f"fun get{module_name}Dao(): {module_name}Dao"
    c_import = f"import {REPO_PACKAGE}.{package_name}.{module_name}CategoryDao"
    c_function = f"fun get{module_name}CategoryDao(): {module_name}CategoryDao"

    process_dao_provider(m_import, m_function)
    process_dao_provider(c_import, c_function)


def process_repo(module_name, values):
    package_name = module_name.lower()

    repo_file = os.path.join(get_template_dir(), "TempRepo.kt")

    cate_id = values["category"]["id"]
    materail_id = values["material"]["id"]

    with open(repo_file, "r") as f:
        content = f.read()
        content = (
            content.replace(r"{package_name}", package_name)
            .replace(r"{module_name}", module_name)
            .replace(r"{category_id}", f"{cate_id}")
            .replace(r"{material_id}", f"{materail_id}")
        )
        with open(f"{get_module_dir(package_name)}/{module_name}Repo.kt", "w") as w:
            w.write(content)
