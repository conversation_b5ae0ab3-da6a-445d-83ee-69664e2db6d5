package com.pixocial.framework.aws.data

import androidx.room.Entity

/**
 *  AWS下载记录。
 */
@Entity(primaryKeys = ["downloadUrl", "saveName"])
class AwsDownwardRecord {

    /**
     * 下载的URl
     */
    var downloadUrl: String = ""

    /**
     * 本地保存的路径。
     */
    var saveName: String = ""

    /**
     * 全部的文件大小
     */
    var totalContentLength: Long = 0

    /**
     * 断点续传的临时本地路径。
     */
    var tempKeepPath: String? = null
}