package com.commsource.widget

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.commsource.util.setRTL

class SpaceVerticalItemDecoration(
    private val firstItemSpace: Int,
    private val space: Int,
    private val lastItemSpace: Int = 0,
    private val isLoop: Boolean = false
) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        var start = space / 2
        var end = space / 2
        if (!isLoop && parent.getChildAdapterPosition(view) == 0) {
            start = firstItemSpace
        }
        if (parent.getChildAdapterPosition(view) == (parent.adapter?.itemCount ?: 0) - 1) {
            end = lastItemSpace
        }
        outRect.setRTL(0, start, 0, end)
    }

}