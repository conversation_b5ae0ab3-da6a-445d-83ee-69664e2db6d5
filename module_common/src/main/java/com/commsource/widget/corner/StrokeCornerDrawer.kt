package com.commsource.widget.corner

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.graphics.Shader
import android.view.View
import androidx.annotation.IntDef
import androidx.core.view.setPadding
import com.commsource.util.dpf
import com.commsource.util.resColor
import com.meitu.lib_common.R

/**
 * @Description : 描边圆角绘制类
 * <AUTHOR> bear
 * @Date : 2022/1/13
 */
class StrokeCornerDrawer(val view: View) {

    /**
     * 描边分布
     *
     * Android所有的描边都是中描边 所以对于外、中、内描边 都是通过Size的调整 + 绘制中描边 得到的目标描边效果
     */
    @IntDef(value = [StrokeAlign.OUTSIDE, StrokeAlign.MID, StrokeAlign.INSIDE])
    annotation class StrokeAlign {
        companion object {
            const val OUTSIDE = 1
            const val MID = 0
            const val INSIDE = -1
        }
    }

    /**
     * 裁剪笔
     */
    private val clipPaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.WHITE
            style = Paint.Style.FILL_AND_STROKE
            strokeWidth = 0f
        }
    }

    /**
     * 描边笔
     */
    private val strokePaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = R.color.black10.resColor()
            style = Paint.Style.STROKE
        }
    }

    /**
     * 是否圆形
     */
    var isCircle = false
        set(value) {
            field = value
            if (fullRectF.width() > 0 && fullRectF.height() > 0) {
                updateCorner(fullRectF.width() / 2f)
                updateParam()
                view.invalidate()
            }
        }

    /**
     * 是否支持阴影
     */
    var isShadowEnable = false
        set(value) {
            field = value
            updateParam()
            view.invalidate()
        }

    /**
     * 描边宽度
     */
    var strokeWidth = 0f.dpf
        set(value) {
            field = value
            updateParam()
            view.invalidate()
        }

    /**
     * 描边颜色
     */
    var strokeColor = R.color.black10.resColor()
        set(value) {
            field = value
            updateParam()
            view.invalidate()
        }

    /**
     * 描边权重
     */
    @StrokeAlign
    var strokeAlign: Int = StrokeAlign.INSIDE
        set(value) {
            field = value
            updateParam()
            view.invalidate()
        }

    var shadowColor: Int = 0x1a000000
        set(value) {
            field = value
            updateParam()
            view.invalidate()
        }

    /**
     * 渐变色
     */
    var gradient: IntArray? = null
        set(value) {
            field = value
            updateParam()
            view.invalidate()
        }

    /**
     * 阴影
     */
    private val shadowPaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = strokeColor
            setShadowLayer(20f, 0f, 2f, shadowColor)
        }
    }

    private val shadowRect = RectF()
    private val shadowDiffuse = 20.dpf

    /**
     * 总尺寸 同View尺寸一致的尺寸
     */
    val fullRectF = RectF()
    private val clipRectF = RectF()
    private val outStrokeRectF = RectF()
    private val inStrokeRectF = RectF()

    /**
     * 圆角组 UI层希望的圆角
     */
    private val cornerArray = FloatArray(8)

    /**
     * 绘制使用的中描边对应的圆角
     */
    private val midCornerArray = FloatArray(8)
    private val clipCornerArray = FloatArray(8)

    /**
     * 圆角path op操作
     */
    private val cornerPath: Path = Path()
    private val clipPath: Path = Path()
    private val outStrokePath = Path()
    private val inStrokePath = Path()

    /**
     * 混合模式抠图
     */
    private val xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_OUT)

    /**
     * 更新参数
     */
    private fun updateParam() {
        if (fullRectF.width() <= 0f || fullRectF.height() <= 0f) {
            return
        }
        //正确的描边绘制
        //居中描边需要内外绘制两次
        val strokeWidth = when {
            strokeAlign == StrokeAlign.MID -> this.strokeWidth / 2f
            else -> this.strokeWidth
        }
        //更新使用绘制的中描边的圆角信息
        updateCornerArray(cornerArray, midCornerArray, strokeWidth / 2f)
        //设置描边笔设置
        shadowPaint.color = strokeColor
        strokePaint.color = strokeColor
        strokePaint.strokeWidth = strokeWidth
        outStrokePath.reset()
        outStrokeRectF.set(fullRectF)
        outStrokeRectF.inset(strokeWidth / 2, strokeWidth / 2)
        outStrokePath.addRoundRect(outStrokeRectF, midCornerArray, Path.Direction.CW)
        if (strokeAlign == StrokeAlign.MID) {
            inStrokePath.reset()
            inStrokeRectF.set(fullRectF)
            //相当于总尺寸内缩
            inStrokeRectF.inset(strokeWidth, strokeWidth)
            //在到绘制尺寸
            inStrokeRectF.inset(strokeWidth / 2, strokeWidth / 2)
            //再次利用计算在总尺寸内缩情况下 绘制一次内描边的圆角信息
            updateCornerArray(cornerArray, midCornerArray, strokeWidth * 3 / 2f)
            inStrokePath.addRoundRect(inStrokeRectF, midCornerArray, Path.Direction.CW)
        }
        //1.下面外内中描边使用padding控制总体尺寸问题，外层不能使用Container padding处理自己封面内尺寸
        //2.内缩尺寸作为裁剪clip使用
        when (strokeAlign) {
            StrokeAlign.INSIDE -> {
                view.setPadding(0)
                clipRectF.set(fullRectF)
                updateCornerArray(cornerArray, clipCornerArray)
            }
            //中描边是绘制两次 所以中描边的外层描边就是和outSide描边的裁剪保持一致做法
            StrokeAlign.MID, StrokeAlign.OUTSIDE -> {
                view.setPadding(strokeWidth.toInt())
                //外描边有很多中做法 这种是保证外尺寸和UI一致的做法，另一种是保证内尺寸扩大外边的做法 两种效果不一致 暂定第一种
                clipRectF.set(fullRectF)
                //外描边是直接用fullRectF往内裁剪的做法
                clipRectF.inset(strokeWidth, strokeWidth)
                //这里3/4描边宽度 其实是有原因的 因为Android都是 中描边绘制 所以在内缩strokeWidth/2绘制后 还要再抬升因为中描边导致的偏差尺寸1/4
                updateCornerArray(cornerArray, clipCornerArray, strokeWidth)
            }
        }
        //扣除裁剪区域path
        //修复8.0以后 混合模式对于范围的问题
        cornerPath.reset()
        cornerPath.addRoundRect(clipRectF, clipCornerArray, Path.Direction.CW)
        clipPath.reset()
        fixFullRectF.set(fullRectF)
        fixFullRectF.inset(-.5f, -.5f)
        clipPath.addRect(fixFullRectF, Path.Direction.CW)
        clipPath.op(cornerPath, Path.Op.DIFFERENCE)
        shadowRect.set(fullRectF)
        shadowRect.inset(-shadowDiffuse, -shadowDiffuse)

        shadowPaint.setShadowLayer(20f, 0f, 2f, shadowColor)
        gradient?.takeIf { it.size > 1 }?.let {
            val size = it.size
            val space = 1f / (size - 1)
            val positions = FloatArray(size).apply {
                var position = 0f
                it.forEachIndexed { index, i ->
                    this[index] = position + index * space
                }
            }
            strokePaint.alpha = 255
            strokePaint.shader = LinearGradient(
                fullRectF.left,
                fullRectF.top,
                fullRectF.right,
                fullRectF.bottom,
                it,
                positions,
                Shader.TileMode.REPEAT
            )
        }
    }

    val fixFullRectF = RectF()

    /**
     * 更新裁剪区域圆角
     */
    private fun updateCornerArray(oriArray: FloatArray, tarArray: FloatArray, inset: Float = 0f) {
        oriArray.forEachIndexed { index, fl ->
            tarArray[index] = fl - inset
        }
    }

    val tempPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = 0x00ffffff
        xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)
    }

    /**
     * 绘制阴影
     */
    fun drawShadow(canvas: Canvas?) {
        if (isShadowEnable) {
            canvas?.let {
                val count = it.saveLayer(shadowRect, shadowPaint)
                it.drawPath(cornerPath, shadowPaint)
                it.drawPath(cornerPath, tempPaint)
                it.restoreToCount(count)
            }
        }
    }

    /**
     * 裁剪圆角
     */
    fun clipCorner(canvas: Canvas?) {
        clipPaint.xfermode = xfermode
        canvas?.drawPath(clipPath, clipPaint)
        clipPaint.xfermode = null
    }

    /**
     * 绘制内尺寸描边
     */
    fun drawInsideStroke(canvas: Canvas?) {
        if (strokeWidth > 0) {
            if (strokeAlign == StrokeAlign.INSIDE) {
                //这个其实和outside描边用同一个尺寸path
                canvas?.drawPath(outStrokePath, strokePaint)
            } else if (strokeAlign == StrokeAlign.MID) {
                canvas?.drawPath(inStrokePath, strokePaint)
            }
        }
    }

    /**
     * 绘制外尺寸描边
     */
    fun drawOutsideStroke(canvas: Canvas?) {
        if (strokeWidth > 0) {
            if (strokeAlign == StrokeAlign.MID || strokeAlign == StrokeAlign.OUTSIDE) {
                canvas?.drawPath(outStrokePath, strokePaint)
            }
        }
    }

    /**
     * 整体圆角
     */
    fun onSizeChanged(w: Int, h: Int) {
        fullRectF.set(0f, 0f, w.toFloat(), h.toFloat())
        if (isCircle) {
            updateCorner(fullRectF.width() / 2f)
        }
        updateParam()
    }

    var corner: Float = 0f

    /**
     * 更新圆角
     */
    fun updateCorner(corner: Float) {
        this.corner = corner
        updateCorner(corner, corner, corner, corner)
    }

    /**
     * 更新圆角
     */
    fun updateCorner(
        TL: Float = cornerArray[0],
        TR: Float = cornerArray[2],
        BR: Float = cornerArray[4],
        BL: Float = cornerArray[6]
    ) {
        cornerArray[0] = TL
        cornerArray[1] = TL

        cornerArray[2] = TR
        cornerArray[3] = TR

        cornerArray[4] = BR
        cornerArray[5] = BR

        cornerArray[6] = BL
        cornerArray[7] = BL
        updateParam()
        view.invalidate()
    }

    fun cornerTL(TL: Float) {
        cornerArray[0] = TL
        cornerArray[1] = TL
        updateParam()
        view.invalidate()
    }

    fun cornerTR(TR: Float) {
        cornerArray[2] = TR
        cornerArray[3] = TR
        updateParam()
        view.invalidate()
    }

    fun cornerBL(BL: Float) {
        cornerArray[6] = BL
        cornerArray[7] = BL
        updateParam()
        view.invalidate()
    }

    fun cornerBR(BR: Float) {
        cornerArray[4] = BR
        cornerArray[5] = BR
        updateParam()
        view.invalidate()
    }

}