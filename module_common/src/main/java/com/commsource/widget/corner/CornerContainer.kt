package com.commsource.widget.corner

import android.content.Context
import android.graphics.Canvas
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.widget.FrameLayout
import com.commsource.util.dpf
import com.commsource.util.resColor
import com.commsource.widget.PressDelegate
import com.meitu.lib_common.R

/**
 * @Description : 边框容器
 * <AUTHOR> bear
 * @Date : 2022/1/13
 *
 * 1.支持内容动态裁剪
 * 2.单一圆角和自定义圆角
 * 3.支持外、中、内描边完整定义
 * 4.支持按压模式
 * 5.支持阴影
 * 6.支持描边线性渐变色(TODO 添加渐变方向等)
 */
class CornerContainer @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {

    /**
     * 按压代理
     *
     * 默认无处理任何按压代理
     */
    val pressDelegate by lazy { PressDelegate(this).apply { isAlpha = false } }

    override fun setPressed(pressed: Boolean) {
        super.setPressed(pressed)
        pressDelegate.pressed = pressed
    }

    override fun dispatchTouchEvent(event: MotionEvent): Boolean {
        if (event.action == MotionEvent.ACTION_DOWN) {
            pressDelegate.pressed = true
        } else if (event.action == MotionEvent.ACTION_CANCEL || event.action == MotionEvent.ACTION_UP) {
            pressDelegate.pressed = false
        }
        return super.dispatchTouchEvent(event)
    }

    /**
     * 内描边目标
     */
    var insideStrokeTarget: View? = null

    /**
     * 描边圆角代理
     */
    val cornerDelegate by lazy { StrokeCornerDrawer(this) }

    /**
     * 描边宽度
     */
    var strokeWidth = 0f.dpf
        set(value) {
            field = value
            if (isStrokeEnable) {
                cornerDelegate.strokeWidth = value
            }
        }

    /**
     * 是否有描边
     */
    var isStrokeEnable = false
        set(value) {
            field = value
            if (value) {
                cornerDelegate.strokeWidth = strokeWidth
            } else {
                cornerDelegate.strokeWidth = 0f
            }
        }

    var isShadowEnable = false
        set(value) {
            field = value
            cornerDelegate.isShadowEnable = value
        }

    /**
     * 权重
     */
    var strokeAlign = StrokeCornerDrawer.StrokeAlign.INSIDE
        set(value) {
            field = value
            cornerDelegate.strokeAlign = value
        }

    /**
     * 是否是圆形
     */
    var isCircle = false
        set(value) {
            field = value
            cornerDelegate.isCircle = value
        }

    /**
     * 描边颜色
     */
    var strokeColor = R.color.black10.resColor()
        set(value) {
            field = value
            cornerDelegate.strokeColor = value
        }

    fun cornerTL(TL: Float) {
        cornerDelegate.updateCorner(TL = TL)
    }

    fun cornerTR(TR: Float) {
        cornerDelegate.updateCorner(TR = TR)
    }

    fun cornerBL(BL: Float) {
        cornerDelegate.updateCorner(BL = BL)
    }

    fun cornerBR(BR: Float) {
        cornerDelegate.updateCorner(BR = BR)
    }

    fun corner(corner: Float) {
        cornerDelegate.updateCorner(corner)
    }

    fun updateCorner(TL: Float, TR: Float, BR: Float, BL: Float) {
        cornerDelegate.updateCorner(TL, TR, BR, BL)
    }

    init {
        setWillNotDraw(false)
    }

    /**
     * 拦截绘制
     */
    override fun drawChild(canvas: Canvas, child: View?, drawingTime: Long): Boolean {
        val boolean = super.drawChild(canvas, child, drawingTime)
        //拦截绘制内描边目标
        val index = indexOfChild(child)
        if (child == insideStrokeTarget || index == 0) {
            cornerDelegate.drawInsideStroke(canvas)
        }
        return boolean
    }

    override fun draw(canvas: Canvas) {
        cornerDelegate.drawShadow(canvas)
        val id = canvas.saveLayer(cornerDelegate.fullRectF, null, Canvas.ALL_SAVE_FLAG)
        super.draw(canvas)
        cornerDelegate.clipCorner(canvas)
        canvas.restoreToCount(id)
        cornerDelegate.drawOutsideStroke(canvas)
    }

    /**
     * 总体拦截裁剪
     */
    override fun dispatchDraw(canvas: Canvas) {
        super.dispatchDraw(canvas)
    }

    /**
     * 尺寸采集
     */
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        cornerDelegate.onSizeChanged(w, h)
    }
}