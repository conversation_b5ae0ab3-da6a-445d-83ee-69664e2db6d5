package com.commsource.share.action

import com.commsource.share.NextAction
import com.commsource.util.ResourcesUtils
import com.meitu.lib_common.R

/**
 * @Description: 再修一张
 *
 * @Author: vinvince, @Time: 2023/7/14 15:10
 */
class EditNewAction : NextAction() {

    override var drawableId: Int = R.string.edit_share_icon_new

    override var actionName: String? = EditNew

    override var showName: String? = ResourcesUtils.getString(R.string.t_continue_edit)
}