package com.commsource.util.coroutine

import kotlinx.coroutines.isActive
import java.util.concurrent.atomic.AtomicInteger
import kotlin.coroutines.Continuation
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * [suspendCoroutine] 提供挂起同步点
 * [resume] 恢复到同步位置
 * "Subsequent invocation of any resume function on the resulting continuation will produce an[IllegalStateException]"
 * Fix:suspendCoroutine#java.lang.IllegalStateException: Already resumed, but proposed with update true
 *
 * 提供安全resume的挂起同步点
 * Note:问题本质还是因为挂起点 内部函数/代码块callback的异常回调，但是还有有必要这么处理，这种直接给崩溃不合适
 */
public suspend inline fun <T> safeSuspendCoroutine(crossinline block: (SafeContinuation<T>) -> Unit) =
    suspendCoroutine<T> {
        block.invoke(SafeContinuation(it))
    }


/**
 * 安全resume
 *
 * 问题本质还是因为挂起点 内部函数/代码块callback的异常回调
 * 比如：
 * 1.onError两次回调
 *
 * 2.onSuccess完之后还响应onError事件
 */
class SafeContinuation<T>(val continuation: Continuation<T>) {

    companion object {
        //限制数据版本
        private const val LimitGeneration = 1
    }

    /**
     * 数据版本
     */
    private val generation = AtomicInteger(0)

    /**
     * 提供resume
     */
    fun resume(resume: T) {

        if (generation.incrementAndGet() <= LimitGeneration) {
            continuation.takeIf { continuation.context.isActive }?.resume(resume)
        }
    }
}
