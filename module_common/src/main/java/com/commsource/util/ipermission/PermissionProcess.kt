package com.commsource.util.ipermission

import android.content.Intent
import android.content.pm.PackageManager
import androidx.annotation.Nullable
import com.commsource.util.delegate.DelegateFragment
import com.commsource.util.delegate.DelegateProcess

/**
 * @Desc : 权限处理
 * <AUTHOR> meitu - 4/1/21
 */
abstract class PermissionProcess(
    val permissions: List<String>,
    val isNeedRequestOneByOne: Boolean = false
) : DelegateProcess() {

    /**
     * request code for permission
     */
    private val PERMISSION_REQUEST_CODE = 200

    /**
     * arrays of permission
     */
    private val requestPermissions = ArrayList<String>()

    /**
     * arrays of permission successful
     */
    private val successPermissions = ArrayList<String>()

    /**
     * 代理处理Fragment
     */
    private var delegateFragment: DelegateFragment? = null

    /**
     * 执行方法 直接准备permission
     */
    override fun onExecute(delegateFragment: DelegateFragment) {
        this.delegateFragment = delegateFragment
        for (permissionStr in permissions) {
            preparePermission(permissionStr)
        }
        requestPermission()
    }

    /**
     * prepare permission
     *
     * @param permission the target permission
     */
    protected fun preparePermission(permission: String?) {
        Preconditions.checkNotNull(permission)
        if (successPermissions.contains(permission) || requestPermissions.contains(permission)) {
            return
        }
        if (!IPermission.isGanted(permission)) {
            requestPermissions.add(permission!!)
            return
        }
        successPermissions.add(permission!!)
    }

    /**
     * request
     */
    protected fun requestPermission() {
        if (requestPermissions.isEmpty()) {
            if (!successPermissions.isEmpty()) {
                val results = java.util.ArrayList<PermissionResult>()
                for (permission in successPermissions) {
                    results.add(PermissionResult(permission, true))
                }
                onPermissionResult(results,false)
            }
            return
        }
        if (isNeedRequestOneByOne) {
            excuteSuccessPermission()
            requestPermission(requestPermissions.removeAt(0))
        } else {
            requestPermission(*requestPermissions.toTypedArray())
            requestPermissions.clear()
        }
    }

    /**
     * send success permission
     */
    private fun excuteSuccessPermission() {
        val permissionResults = ArrayList<PermissionResult>()
        for (permission in successPermissions) {
            if (isNeedRequestOneByOne) {
                permissionResults.clear()
                permissionResults.add(PermissionResult(permission, true))
                onPermissionResult(permissionResults,true)
            } else {
                permissionResults.add(PermissionResult(permission, true))
            }
        }
        if (!isNeedRequestOneByOne) {
            onPermissionResult(permissionResults,true)
        }
        successPermissions.clear()
    }

    private fun requestPermission(vararg permissions: String) {
        delegateFragment?.requestPermissions(permissions, PERMISSION_REQUEST_CODE)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        return true
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ): Boolean {
        // receive permission result after policy selected
        // to be safe
        if (requestCode == PERMISSION_REQUEST_CODE) {
            val permissionResults = ArrayList<PermissionResult>()

            // 1.remove deal permission and remove it on all permissions
            for (i in permissions.indices) {
                val requestedPermission = permissions[i]
                val grantedCode = grantResults[i]
                permissionResults.add(
                    PermissionResult(
                        requestedPermission,
                        grantedCode == PackageManager.PERMISSION_GRANTED
                    )
                )
            }

            // 2.diff strategy diff next
            if (isNeedRequestOneByOne) {
                onPermissionResult(permissionResults,true)
                if (hasNext()) {
                    requestPermission()
                    return false
                }
            } else {
                // 3.rest permissions is successful at first
                for (successPermission in successPermissions) {
                    permissionResults.add(PermissionResult(successPermission, true))
                }
                onPermissionResult(permissionResults,true)
            }
            clear()
        }
        return true
    }

    private operator fun hasNext(): Boolean {
        return !requestPermissions.isEmpty()
    }

    fun clear() {
        successPermissions.clear()
        requestPermissions.clear()
    }

    /**
     * 权限回调
     */
    abstract fun onPermissionResult(@Nullable results: List<PermissionResult>?, isRequestResult: Boolean)
}