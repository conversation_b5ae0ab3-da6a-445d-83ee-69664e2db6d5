package com.commsource.share.common;

import android.content.ActivityNotFoundException;
import android.content.Context;

import com.commsource.beautyplus.R;
import com.meitu.common.utils.ToastUtils;


public class ShareUtil {

    public static void sharePlatformClick(Context context, String shareUrl,
        String sharePlatform) {
        switch (sharePlatform) {
            case SnsUtil.PLATFORM_INSTAGRAM:
                doJump2SharePlatform(context, shareUrl, SnsUtil.PACKAGE_INSTAGRAM, SnsUtil.PLATFORM_INSTAGRAM);
                break;
            case SnsUtil.PLATFORM_FACEBOOK:
                doJump2SharePlatform(context, shareUrl, SnsUtil.PACKAGE_FACEBOOK, SnsUtil.PLATFORM_FACEBOOK);
                break;
            case SnsUtil.PLATFORM_TWITTER:
                doJump2SharePlatform(context, shareUrl, SnsUtil.PACKAGE_TWITTER, SnsUtil.PLATFORM_TWITTER);
                break;
            case SnsUtil.PLATFORM_WHATSAPP:
                doJump2SharePlatform(context, shareUrl, SnsUtil.PACKAGE_WHATSAPP, SnsUtil.PLATFORM_WHATSAPP);
                break;
            case SnsUtil.PLATFORM_WECHAT:
                doJump2SharePlatform(context, shareUrl, SnsUtil.PACKAGE_WECHAT, SnsUtil.PLATFORM_WECHAT);
                break;
            case SnsUtil.PLATFORM_WECHAT_MOMENTS:
                doJump2SharePlatform(context, shareUrl, SnsUtil.PACKAGE_WECHAT, SnsUtil.PLATFORM_WECHAT_MOMENTS);
                break;
            case SnsUtil.PLATFORM_KAKAOTALK:
                doJump2SharePlatform(context, shareUrl, SnsUtil.PACKAGE_KAKAOTALK, SnsUtil.PLATFORM_KAKAOTALK);
                break;
            case SnsUtil.PLATFORM_LINE:
                doJump2SharePlatform(context, shareUrl, SnsUtil.PACKAGE_LINE, SnsUtil.PLATFORM_LINE);
                break;
            case SnsUtil.PLATFORM_WEIBO:
                doJump2SharePlatform(context, shareUrl, SnsUtil.PACKAGE_WEIBO,
                    context.getString(R.string.save_and_share_weibo));
                break;
            case SnsUtil.PLATFORM_MIGME:
                doJump2SharePlatform(context, shareUrl, SnsUtil.PACKAGE_MIGME, SnsUtil.PLATFORM_MIGME);
                break;
            default:
                break;
        }
    }

    public static void doJump2SharePlatform(Context context, String shareUrl, String packageName, String platform) {
        try {
            SnsUtil.shareLinkByPlatform(context, packageName, shareUrl);
        } catch (ActivityNotFoundException e) {
            ToastUtils.showShortToastSafe(
                String.format(context.getString(R.string.t_share_install_tips), platform));
        }
    }

}
