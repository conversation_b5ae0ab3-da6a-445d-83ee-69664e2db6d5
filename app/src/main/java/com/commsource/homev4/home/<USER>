package com.commsource.homev4.home

import android.graphics.Rect
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.commsource.airepair.imagequality.engine.ImageProcessStatus
import com.commsource.airepair.imagequality.repo.ImageRepairRecord
import com.commsource.airepair.imagequality.repo.ImageRepairRecordRepo
import com.commsource.applanuch.HWBusinessLaunchTask
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.beautyplus.BaseActivity
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentHomeV4Binding
import com.commsource.beautyplus.setting.abtest.ABTestDataEnum
import com.commsource.billing.SubSource
import com.commsource.billing.activity.SubscribeActivity
import com.commsource.billing.activity.SubscribeViewModel
import com.commsource.billing.pro.GmsManager
import com.commsource.camera.xcamera.cover.bottomFunction.BaseBottomSubFragment
import com.commsource.camera.xcamera.cover.bottomFunction.effect.filter.SubscribeInfo
import com.commsource.config.SubscribeConfig
import com.commsource.home.NewHomeViewModel
import com.commsource.home.create.FunctionViewModel
import com.commsource.home.work.AiRecordManager
import com.commsource.homev2.HomeContentViewModel
import com.commsource.homev3.BaseHomeStateHolder
import com.commsource.homev3.DailyRewardUIHolder
import com.commsource.homev3.DailyRewardViewHolder
import com.commsource.homev3.HomeRouter
import com.commsource.homev4.HomeUITransitionHelper
import com.commsource.homev4.vh.EditingAreaViewHolder
import com.commsource.homev4.vh.KingKongViewHolder
import com.commsource.homev4.vh.OperationViewHolder
import com.commsource.search_common.view.SearchEditTextView
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.Meepo
import com.commsource.statistics.SpmParamConstant
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.statistics.constant.MTAnalyticsConstant.topbar_clk
import com.commsource.util.BitmapUtils
import com.commsource.util.ErrorNotifier
import com.commsource.util.LOGV_Pro
import com.commsource.util.common.BaseCallback2
import com.commsource.util.dp
import com.commsource.util.dpf
import com.commsource.util.gone
import com.commsource.util.resColor
import com.commsource.util.setMarginStart
import com.commsource.util.string
import com.commsource.util.traverseShowViewHolder
import com.commsource.util.visible
import com.commsource.widget.mask.MaskType
import com.commsource.widget.recyclerview.AdapterDataBuilder
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.meitu.common.AppContext
import com.meitu.common.utils.GradientDrawableFactory
import com.meitu.hwbusinesskit.core.HWBusinessSDK
import com.meitu.library.hwanalytics.spm.SPMManager
import com.meitu.library.hwanalytics.spm.SPMShare
import com.meitu.library.util.device.DeviceUtils
import com.meitu.library.util.net.NetUtils
import com.meitu.media.tools.editor.MVEditorTool
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

class HomeV4Fragment : BaseBottomSubFragment() {

    companion object {
        fun newInstance() = HomeV4Fragment()
    }

    private val binding by lazy { FragmentHomeV4Binding.inflate(layoutInflater) }

    private val viewModel by lazy { ViewModelProvider(ownerActivity)[HomeV4ViewModel::class.java] }
    private val functionViewModel by lazy { ViewModelProvider(ownerActivity)[FunctionViewModel::class.java] }

    private val topBannerHolder by lazy {
        TopBannerV4Holder(ownerActivity as BaseActivity, binding)
    }

    private val operationAdapter by lazy { BaseRecyclerViewAdapter(context) }

    private val homeViewModel by lazy {
        ViewModelProvider(ownerActivity)[NewHomeViewModel::class.java]
    }

    private val contentViewModel by lazy {
        ViewModelProvider(ownerActivity)[HomeContentViewModel::class.java]
    }

    private val subscribeViewModel by lazy {
        ViewModelProvider(ownerActivity)[SubscribeViewModel::class.java]
    }

    private var transitionHelper: HomeUITransitionHelper? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        lifecycle.addObserver(homeViewModel)
        viewModel.load()
        viewModel.requestHotSearchWord(this@HomeV4Fragment)
        initialContentRV()
        initialSearchBar()
        initialObserver()
        tryEnterAdMobTest()

        if (!SubscribeConfig.isSubValid()) {
            Meepo.enterTest(ABTestDataEnum.HOME_PRO_VIEW_REF, ABTestDataEnum.HOME_PRO_VIEW_TEST)
        }

        if (Meepo.isInHomeProViewTest()) {
            binding.proView.setIconGravity(Gravity.START)
            binding.proView.setMarginStart(16.dp)
        }
    }

    private fun initialObserver() {
        functionViewModel.showMainFunctionEvent.observe(viewLifecycleOwner) {
            if (it?.fgClazz == HomeV4Fragment::class.java) {
                topBannerHolder.setAutoScrollState(false)
            } else {
                topBannerHolder.setAutoScrollState(true)
            }
        }

        viewModel.uiState.onEach {
            when (it) {
                is HomeUiState.Success -> {
                    binding.maskContainer.hideAll()
                    topBannerHolder.updateTopVPData(it.topBanner)
                    updateRvOperation(it)
                    // 更数据的时候先屏蔽操作
                    binding.optionBarrier.visible()
                    binding.rvOperation.post {
                        binding.appBarLayout.setExpanded(true, false)
                        binding.rvOperation.scrollToPosition(0)
                        transitionHelper?.release()
                        transitionHelper = HomeUITransitionHelper(binding, it)
                        binding.optionBarrier.gone()
                    }
                }

                is HomeUiState.Loading -> {
                    binding.maskContainer.showMask(MaskType.Loading)
                }

                is HomeUiState.Error -> {
                    binding.maskContainer.showMask(it.maskType)
                }
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)

        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.eventFlow.collect { event ->
                    when (event) {
                        is HomeEvent.ShowFuncHalfDialog -> {
                            showFuncHalfDialog()
                        }
                    }
                }
            }
        }

        binding.personalStudio.setOnClickListener {
            MTAnalyticsAgent.logEvent(topbar_clk, "type", "me")
            HomeRouter.toPersonalWork(requireActivity())
        }

        lifecycleScope.launch {
            subscribeViewModel.innerViewModel.homeUiStateFlow.collect {
                binding.proView.homeSubscribeUiState = it
            }
        }
        subscribeViewModel.innerViewModel.countDownTextEvent.observe(viewLifecycleOwner) {
            binding.proView.setDiscount(it)
        }

        binding.proView.setOnClickListener {
            MTAnalyticsAgent.logEvent(topbar_clk, "type", "vip")
            SPMShare.put(SpmParamConstant.KEY_SOURCE_CLICK_POSITION, "默认入口")
            SubscribeActivity.startActivity(ownerActivity, SubSource.FROM_HOME)
        }
        // 订阅用户类型
        SubscribeInfo.instance.notifyUseTypeChanged.observe(viewLifecycleOwner,
            object : NoStickLiveData.CustomObserver<Boolean?>() {
                override fun onAccept(result: Boolean?) {
                    if (result == true) {
                        subscribeViewModel.initUserType()
                    }
                }
            })

        // 订阅通知
        GmsManager.instance.subscribeResultEvent.observe(viewLifecycleOwner) {
            operationAdapter.notifyAllItemChange()
        }

        viewModel.recordStateChangeListener.observe(viewLifecycleOwner) {
            // todo 更新金刚区
//            viewModel.dataEvent.value?.kingKong?.elementAtOrNull(0)?.let {
//                operationAdapter.notifyItemChanged(it)
//            }
        }

        lifecycleScope.launchWhenCreated {
            combine(viewModel.personalWorkHasNew, viewModel.isShowSearch) { hasNew, showSearch ->
                hasNew && !showSearch
            }.collect {
                binding.vPersonalStudioRed.visibility = if (it) View.VISIBLE else View.GONE

            }

        }

        //定位到顶部
        contentViewModel.focusHomeTopEvent.observe(viewLifecycleOwner) {
            binding.rvOperation.scrollToPosition(0)
            binding.appBarLayout.setExpanded(true, true)
        }


        subscribeViewModel.subSuccessEvent.observe(requireActivity()) {
            //刷新功能item
            subscribeViewModel.innerViewModel.checkSubscribed()
            "通知功能区刷新 ${SubscribeConfig.isSubValid()}".LOGV_Pro()
            GmsManager.instance.postSubscribeResult(SubscribeConfig.isSubValid())
            subscribeViewModel.setNeedLogSubSuccess(false)
        }

        homeViewModel.analyzeEvent.observe(viewLifecycleOwner) {
            viewModel.clearContentShowSet()
            if (it) {
                notifyRvTraverseState()
            }
        }

        homeViewModel.applyAllSearchEvent.observe(viewLifecycleOwner) {
            viewModel.isShowSearch.value = it
            if (!it) {
                transitionHelper?.resumeSearch2LastState()
                // 取消焦点
                binding.searchBg.gone()
                binding.searchEdit.setText("")
                binding.searchEdit.requestEditFocus(false, "")
                topBannerHolder.setAutoScrollState(false)
            }
        }

        homeViewModel.setSearchWord.observe(viewLifecycleOwner) {
            it?.run {
                binding.searchEdit.setText(this)
                binding.searchEdit.requestEditFocus(false, "")
            }
        }

        functionViewModel.showMainFunctionEvent.observe(viewLifecycleOwner) {
            if (it?.fgClazz == HomeV4Fragment::class.java) {
                topBannerHolder.setAutoScrollState(false)
            } else {
                topBannerHolder.setAutoScrollState(true)
            }
        }

        homeViewModel.closeInputEvent.observe(viewLifecycleOwner) {
            binding.searchEdit.requestEditFocus(false)
        }

        homeViewModel.searchApplyEvent.observe(viewLifecycleOwner) {
            it?.run {
                SPMManager.instance.getTopModel()?.let {
                    first?.run {
                        it.addExtraInfo(SpmParamConstant.KEY_MIDS_MATERIAL, this)
                    }

                    second?.run {
                        it.addExtraInfo(SpmParamConstant.KEY_MIDS_MATERIAL_TAG, this)
                    }
                    it.content = third
                }
            }
        }

        viewModel.requestStateEvent.observe(viewLifecycleOwner) {
            when (it) {
                null -> {
                    binding.maskContainer.gone()
                    binding.maskContainer.hideAll()
                }

                true -> {
                    binding.maskContainer.visible()
                    binding.maskContainer.showMask(MaskType.Loading)
                    binding.maskContainer.getMask(MaskType.Loading)?.view
                        ?.setBackgroundColor(R.color.Gray_O.resColor())
                }

                else -> {
                    binding.maskContainer.visible()
                    binding.maskContainer.showMask(MaskType.NetError)
                    binding.maskContainer.getMask(MaskType.NetError)?.view
                        ?.setBackgroundColor(R.color.Gray_O.resColor())
                }
            }
        }

        homeViewModel.clickBlankEvent.observe(viewLifecycleOwner) {
            binding.searchEdit.requestEditFocus(false, "")
        }

        homeViewModel.userApproveProtocolEvent.observe(viewLifecycleOwner) {
            // 用户同意协议后重新拉取首页数据
            // todo
        }

        viewModel.keyWordResultEvent.observe(viewLifecycleOwner) {
            if (!it.isNullOrEmpty()) {
                homeViewModel.loadHotEvent.value = true
            }
        }

        // 监听显示搜索页事件
        homeViewModel.displayHomeAllSearchEvent.observe(viewLifecycleOwner) {
            onSearchClick()
        }

        // 画质修复相关
        binding.llImageQualityResult.background =
            GradientDrawableFactory.createDrawable(R.color.color_80000000.resColor(), 12.dpf)
        binding.tvGoToTaskManager.background =
            GradientDrawableFactory.createDrawable(R.color.white.resColor(), 12.dpf)
        binding.tvGoToTaskManager.setOnClickListener {
            HomeRouter.toPersonalWork(ownerActivity, true)
        }
        binding.llImageQualityResult.setOnClickListener {
            HomeRouter.toPersonalWork(ownerActivity, true)
        }
        binding.ivCloseImageQualityNotifier.setOnClickListener {
            binding.llImageQualityResult.gone()
        }

        ImageRepairRecordRepo.latestNotifiedImageRepairRecordEvent.observe(viewLifecycleOwner) { record ->
            if (record is ImageRepairRecord) {
                if (record.status != ImageProcessStatus.PROCESSED
                    && record.status != ImageProcessStatus.UPLOAD_FAILED
                    && record.status != ImageProcessStatus.PROCESS_FAILED
                ) {
                    return@observe
                }

                if (record.notifiedInApp) {
                    return@observe
                }

                lifecycleScope.launch {
                    record.notifiedInApp = true
                    ImageRepairRecordRepo.updateRecordToDb(record)
                }

                binding.llImageQualityResult.visible()
                binding.ivImageQualityPictureContainer.visible()

                val bitmap = if (record.isVideoType()) {
                    if (record.coverPath == null) {
                        MVEditorTool.getFirstFrame(ownerActivity, record.localFilePath)
                    } else {
                        record.coverPath?.let {
                            BitmapUtils.loadBitmapFromSDcard(it)
                        }
                    }
                } else {
                    record.coverPath?.let {
                        BitmapUtils.loadBitmapFromSDcard(it)
                    }
                }
                when (record.status) {
                    ImageProcessStatus.PROCESSED -> {
                        if (record.isVideoType()) {
                            binding.tvImageQualityResult.text = R.string.v7120_B_27.string()
                        } else {
                            binding.tvImageQualityResult.text = R.string.v7120_B_26.string()
                        }

                        if (bitmap != null) {
                            binding.ivImageQualityResultPic.setImageBitmap(bitmap)
                            binding.ivImageQualityResultPicMask.gone()
                        } else {
                            if (record.localFilePath == null) {
                                // 卸载重装的情况
                                if (record.uploadedUrl != null) {
                                    Glide.with(this)
                                        .load(record.uploadedUrl)
                                        .frame(0)
                                        .into(binding.ivImageQualityResultPic)
                                } else {
                                    binding.ivImageQualityPictureContainer.gone()
                                }
                            } else {
                                binding.ivImageQualityResultPic.setImageBitmap(null)
                                binding.ivImageQualityResultPicMask.visible()
                            }
                        }
                        binding.ivImageQualityResultWrongIcon.gone()
                    }

                    ImageProcessStatus.UPLOAD_FAILED,
                    ImageProcessStatus.PROCESS_FAILED -> {
                        if (record.isVideoType()) {
                            binding.tvImageQualityResult.text = R.string.v7120_B_29.string()
                        } else {
                            binding.tvImageQualityResult.text = R.string.v7120_B_28.string()
                        }

                        binding.ivImageQualityResultWrongIcon.visible()
                        if (bitmap != null) {
                            binding.ivImageQualityResultPic.setImageBitmap(bitmap)
                            binding.ivImageQualityResultPicMask.visible()
                        } else {
                            if (record.localFilePath == null) {
                                // 卸载重装的情况
                                binding.ivImageQualityPictureContainer.gone()
                            } else {
                                binding.ivImageQualityResultPic.setImageBitmap(null)
                                binding.ivImageQualityResultPicMask.gone()
                            }
                        }
                    }
                }
            }
        }

        ImageRepairRecordRepo.linkToRepairWorkPageEvent.observe(viewLifecycleOwner) {
            binding.llImageQualityResult.gone()
        }
    }

    override fun onStop() {
        super.onStop()
        binding.llImageQualityResult.gone()
    }

    /**
     * Admob聚合实验
     */
    private fun tryEnterAdMobTest() {
        if (!SubscribeConfig.isSubValid()) {
            Meepo.enterTest(
                ABTestDataEnum.ADMOB_MEDIATIONS_REF,
                ABTestDataEnum.ADMOB_MEDIATIONS_TEST
            )
        }
        HWBusinessSDK.filterBusinessExtra(HWBusinessLaunchTask.adSlotPlatformFilterForVersion712)
    }

    private fun initialSearchBar() {
        binding.searchEdit.setSearchHintVisible(false)
        binding.searchEdit.hideHotLoopTextView()
        binding.searchEdit.setSearchIconStartMargin(1.dp)
        binding.searchEdit.viewBinding.searchIcon.setTextColor(R.color.Gray_A.resColor())
        // 取消搜索
        binding.searchEdit.switchWhiteBg()
        binding.tvCancelSearch.setOnClickListener {
            homeViewModel.applyAllSearchEvent.value = false
            homeViewModel.cancelSearchEvent.value = true
        }

        // 点击搜索。
        binding.searchEdit.setOnClickSearchLayoutListener(object :
            SearchEditTextView.OnClickSearchLayoutListener {

            override fun onClearClick() {
                //如果当前在搜索loading状态，取消loading
                homeViewModel.delWord.value = true
            }

            override fun onCancelClick() {}


            override fun onSearchClick(hotWord: String?) {
                onSearchClick()
            }

            override fun onInvokeSearch(searchWord: String?) {}

            override fun onInputting(searchWord: String) {
                homeViewModel.inputEvent.value = searchWord
            }
        })
    }

    private fun onSearchClick() {
        // 还在动画之中
        if (transitionHelper?.isInUITransition() == true) {
            return
        }
        // 已经进入搜索界面
        if (homeViewModel.applyAllSearchEvent.value == true) {
            binding.searchEdit.requestEditFocus(true)
            homeViewModel.inputEvent.value = binding.searchEdit.getText()
            return
        }
        MTAnalyticsAgent.logEvent(
            MTAnalyticsConstant.material_search_button_clk,
            HashMap<String, String>(4).apply {
                put(MTAnalyticsConstant.source, MTAnalyticsConstant.home_page_search)
                put(MTAnalyticsConstant.material_type, MTAnalyticsConstant.all)
            })
        viewModel.requestHotSearchWord(this@HomeV4Fragment)
        viewModel.clearContentShowSet()
        //停止轮播热搜词
        binding.searchBg.visible()
        binding.searchEdit.stopScroll()
        transitionHelper?.expandSearchBar()
        homeViewModel.applyAllSearchEvent.value = true
        binding.searchEdit.requestEditFocus(true, "")
        topBannerHolder.setAutoScrollState(true)
    }

    private fun initialContentRV() {
        topBannerHolder.initialTopBanner()
        binding.rvOperation.apply {
            layoutManager = LinearLayoutManager(ownerActivity)
            adapter = operationAdapter
        }
        // 计算屏幕高度是否足够
        binding.root.post {
            val tempHeight = DeviceUtils.getScreenWidth() / 390f * 270f + 477.dp
            if (binding.root.height - tempHeight < 60.dp) {
                operationAdapter.tag = true
            }
        }

        binding.appBarLayout.addOnOffsetChangedListener { _, _ ->
            notifyRvTraverseState()
        }

        binding.rvOperation.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            var scrolledDy = 0
            var hasReported = false

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                scrolledDy += dy
                if (scrolledDy >= 150.dp && !hasReported) {
                    hasReported = true
                    MTAnalyticsAgent.logEvent(
                        MTAnalyticsConstant.home_second_page_appr,
                        "type",
                        "home"
                    )
                }
                notifyRvTraverseState()
            }
        })

        //添加数据mask的基础监听回调
        binding.maskContainer.maskContainerHelper.newBuilder()
            .bindView(MaskType.NetError, R.id.tv_action) {
                if (NetUtils.canNetworking(AppContext.context)) {
                    // todo check 是否直接load
                    viewModel.load()
                    subscribeViewModel.initUserType()
                } else {
                    ErrorNotifier.showNetworkErrorToast()
                }
            }.build()
    }

    private val callback = BaseCallback2<Int, RecyclerView.ViewHolder> { state, holder ->
        (holder as? BaseHomeStateHolder<*>)?.holderState = state
    }

    private fun notifyRvTraverseState() {
        topBannerHolder.analyzeIfShowComplete()
        binding.rvOperation.traverseShowViewHolder(
            false,
            excludeBottom = 59.dp,
            callback2 = callback
        )
        binding.rvOperation.adapter?.let {
            for (index in 0 until it.itemCount) {
                (binding.rvOperation.findViewHolderForAdapterPosition(index)
                        as? BaseHomeStateHolder<*>)?.onNotifyScrollY()
            }
        }
    }

    private fun updateRvOperation(uiState: HomeUiState.Success) {
        if (binding.rvOperation.itemDecorationCount > 0) {
            binding.rvOperation.removeItemDecorationAt(0)
        }
        val hasRVData = !uiState.editingAreaConfig.isNullOrEmpty() || !uiState.kingKongConfig.isNullOrEmpty() || !uiState.operationConfig.isNullOrEmpty()
        if (hasRVData) {
            binding.rvOperation.addItemDecoration(object : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect, view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State
                ) {
                    (parent.adapter as? BaseRecyclerViewAdapter)?.let { adapter ->
                        val position = parent.getChildAdapterPosition(view)
                        if (position == adapter.itemCount - 1) {
                            val holder = parent.getChildViewHolder(view)
                            if (holder is DailyRewardViewHolder) {
                                outRect.bottom = 58.dp
                            } else {
                                outRect.bottom = 70.dp
                            }
                        } else {
                            outRect.bottom = 24.dp
                        }
                    }
                }
            })
        }

        operationAdapter.updateItemEntities(
            AdapterDataBuilder.create().apply {
                addEntities(uiState.editingAreaConfig, EditingAreaViewHolder::class.java)
                addEntities(listOf(uiState.kingKongConfig), KingKongViewHolder::class.java)
                addEntities(uiState.operationConfig, OperationViewHolder::class.java)
            }.build(), true)
    }

    private fun showFuncHalfDialog() {
        HomeFuncHalfDialogFragment().show(parentFragmentManager, "HomeFuncHalfDialogFragment")
    }
}