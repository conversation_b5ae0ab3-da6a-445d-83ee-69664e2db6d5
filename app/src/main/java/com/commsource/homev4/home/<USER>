package com.commsource.homev4.home

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.DecelerateInterpolator
import android.widget.FrameLayout
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.commsource.album.BpAlbumJumpRouter
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentHomeFuncHalfBinding
import com.commsource.homev4.vh.FuncHalfViewHolder
import com.commsource.studio.ImageStudioViewModel
import com.commsource.util.dp
import com.commsource.util.logV
import com.commsource.util.resColor
import com.commsource.widget.SpaceVerticalItemDecoration
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.meitu.library.util.device.DeviceUtils
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

class HomeFuncHalfDialogFragment : BottomSheetDialogFragment() {

    private var startDragY: Float = 0f
    private var isDragging: Boolean = false

    val viewModel by lazy { ViewModelProvider(requireActivity())[HomeV4ViewModel::class.java] }

    private val binding by lazy {
        FragmentHomeFuncHalfBinding.inflate(layoutInflater)
    }

    private val mAdapter by lazy {
        BaseRecyclerViewAdapter(this)
    }

    override fun getTheme(): Int {
        return R.style.CustomBottomSheetDialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.rvFunc.run {
            addItemDecoration(SpaceVerticalItemDecoration(0, 24.dp))
            this.adapter = mAdapter
            this.layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
        }
        binding.icClose.setOnClickListener {
            dismissAllowingStateLoss()
        }
        binding.flBottom.setOnClickListener {
            BpAlbumJumpRouter.toAlbumEditHasTab(
                requireActivity(),
                from = ImageStudioViewModel.FROM_WORK
            )
            dismissAllowingStateLoss()
        }
        viewModel.funcHalfListFlow.onEach {
            mAdapter.setSingleItemEntities(it, FuncHalfViewHolder::class.java)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
    }

    override fun onStart() {
        super.onStart()
        val dialog = dialog as? BottomSheetDialog ?: return
        val window = dialog.window ?: return
        val behavior = dialog.behavior

        window.navigationBarColor = R.color.gray_f1f1f5.resColor()
        val screenHeight = DeviceUtils.getRealScreenHeight()
        val fixedHeight = (screenHeight * 0.83).toInt()

        val bottomSheet = dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) ?: return
        bottomSheet.let {
            val layoutParams = it.layoutParams
            layoutParams.height = fixedHeight
            it.layoutParams = layoutParams
        }

        behavior.state = BottomSheetBehavior.STATE_EXPANDED
        behavior.peekHeight = fixedHeight
        behavior.isHideable = true
        behavior.addBottomSheetCallback(bottomSheetCallback)

        // 1. 立即隐藏默认的视图，防止它闪现
        bottomSheet.translationY = bottomSheet.height.toFloat()
        // 2. 启动我们自己的动画
        bottomSheet.post { // 使用 post 确保视图已经测量完毕
            bottomSheet.animate()
                .translationY(0f)
                .setDuration(2000) // 200ms
                .setInterpolator(DecelerateInterpolator()) // ease-out 效果
                .withEndAction {
                    // 动画结束后，确保behavior状态同步，这对于拖拽行为至关重要
                    if (isAdded) { // 检查Fragment是否还附加着
                        behavior.state = BottomSheetBehavior.STATE_EXPANDED
                    }
                }
                .start()
        }
    }

    private val bottomSheetCallback = object : BottomSheetBehavior.BottomSheetCallback() {
        override fun onStateChanged(bottomSheet: View, newState: Int) {
            when (newState) {
                BottomSheetBehavior.STATE_DRAGGING -> {
                    // 当用户开始拖拽时，记录起始Y坐标
                    isDragging = true
                    startDragY = bottomSheet.y
                }
                BottomSheetBehavior.STATE_SETTLING -> {
                    // 当用户松手，视图开始自动滑动时
                    if (isDragging) {
                        isDragging = false

                        // 计算拖拽距离
                        val dragDistance = bottomSheet.y - startDragY

                        // 将20dp转换为像素
                        val thresholdPx = 20.dp

                        // 判断规则
                        // a: 拖拽距离＞20dp → 松手收起
                        // b: 拖拽距离≤20dp → 松手回弹
                        "拖拽距离 $dragDistance".logV("AAAAA")
                        if (dragDistance > thresholdPx) {
                            "拖拽距离大于 $thresholdPx".logV("AAAAA")
                            dismissAllowingStateLoss()
                        }
                    }
                }
                else -> {
                    // 其他状态，例如 HIDDEN, EXPANDED, COLLAPSED
                }
            }
        }

        override fun onSlide(bottomSheet: View, slideOffset: Float) {
            // slideOffset: -1.0 (Hidden) to 1.0 (Expanded)
            // 你可以在这里根据滑动偏移量做一些其他动画，例如背景透明度变化
        }
    }

}