package com.commsource.homev4.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.animation.DecelerateInterpolator
import android.widget.FrameLayout
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.commsource.album.BpAlbumJumpRouter
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentHomeFuncHalfBinding
import com.commsource.homev4.vh.FuncHalfViewHolder
import com.commsource.studio.ImageStudioViewModel
import com.commsource.util.dp
import com.commsource.util.logV
import com.commsource.util.resColor
import com.commsource.widget.SpaceVerticalItemDecoration
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.meitu.library.util.device.DeviceUtils
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

class HomeFuncHalfDialogFragment : BottomSheetDialogFragment() {

    private var startDragY: Float = 0f
    private var isDragging: Boolean = false

    val viewModel by lazy { ViewModelProvider(requireActivity())[HomeV4ViewModel::class.java] }

    private val binding by lazy {
        FragmentHomeFuncHalfBinding.inflate(layoutInflater)
    }

    private val mAdapter by lazy {
        BaseRecyclerViewAdapter(this)
    }

    override fun getTheme(): Int {
        return R.style.CustomBottomSheetDialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.rvFunc.run {
            addItemDecoration(SpaceVerticalItemDecoration(0, 24.dp))
            this.adapter = mAdapter
            this.layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
        }
        binding.icClose.setOnClickListener {
            startCustomExitAnimation()
        }
        binding.flBottom.setOnClickListener {
            BpAlbumJumpRouter.toAlbumEditHasTab(
                requireActivity(),
                from = ImageStudioViewModel.FROM_WORK
            )
            dismissAllowingStateLoss()
        }
        viewModel.funcHalfListFlow.onEach {
            mAdapter.setSingleItemEntities(it, FuncHalfViewHolder::class.java)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
    }

    override fun onStart() {
        super.onStart()
        val dialog = dialog as? BottomSheetDialog ?: return
        val window = dialog.window ?: return
        val behavior = dialog.behavior

        window.navigationBarColor = R.color.gray_f1f1f5.resColor()
        val screenHeight = DeviceUtils.getRealScreenHeight()
        val fixedHeight = (screenHeight * 0.83).toInt()

        val bottomSheet = dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) ?: return
        bottomSheet.let {
            val layoutParams = it.layoutParams
            layoutParams.height = fixedHeight
            it.layoutParams = layoutParams
        }

        // 配置 BottomSheetBehavior，但不立即设置为 EXPANDED 状态
        behavior.peekHeight = fixedHeight
        behavior.isHideable = true
        behavior.addBottomSheetCallback(bottomSheetCallback)

        // 禁用默认的状态变化动画，防止与自定义动画冲突
        behavior.skipCollapsed = true

        // 先设置为 HIDDEN 状态，避免默认动画
        behavior.state = BottomSheetBehavior.STATE_HIDDEN

        // 执行自定义进入动画
        startCustomEnterAnimation(bottomSheet, behavior)
    }

    private fun startCustomEnterAnimation(bottomSheet: View, behavior: BottomSheetBehavior<FrameLayout>) {
        // 确保视图已经布局完成
        bottomSheet.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                bottomSheet.viewTreeObserver.removeOnGlobalLayoutListener(this)

                // 检查视图是否还有效
                if (!isAdded || bottomSheet.height <= 0) {
                    return
                }

                // 初始位置：将 bottomSheet 移动到屏幕底部外
                val initialTranslation = bottomSheet.height.toFloat()
                bottomSheet.translationY = initialTranslation

                // 设置为 EXPANDED 状态但不触发默认动画（因为已经在屏幕外）
                behavior.state = BottomSheetBehavior.STATE_EXPANDED

                // 启动自定义动画
                bottomSheet.animate()
                    .translationY(0f)
                    .setDuration(300) // 使用合理的动画时长
                    .setInterpolator(DecelerateInterpolator())
                    .withStartAction {
                        // 动画开始时确保视图可见
                        bottomSheet.visibility = View.VISIBLE
                        "开始自定义进入动画，初始translationY: $initialTranslation".logV("BottomSheetAnim")
                    }
                    .withEndAction {
                        // 动画结束后确保状态正确
                        if (isAdded) {
                            behavior.state = BottomSheetBehavior.STATE_EXPANDED
                            "自定义进入动画完成".logV("BottomSheetAnim")
                        }
                    }
                    .start()
            }
        })
    }

    private fun startCustomExitAnimation() {
        val dialog = dialog as? BottomSheetDialog ?: run {
            dismissAllowingStateLoss()
            return
        }

        val bottomSheet = dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) ?: run {
            dismissAllowingStateLoss()
            return
        }

        // 执行退出动画
        bottomSheet.animate()
            .translationY(bottomSheet.height.toFloat())
            .setDuration(250) // 退出动画稍快一些
            .setInterpolator(DecelerateInterpolator())
            .withStartAction {
                "开始自定义退出动画".logV("BottomSheetAnim")
            }
            .withEndAction {
                "自定义退出动画完成，关闭Dialog".logV("BottomSheetAnim")
                dismissAllowingStateLoss()
            }
            .start()
    }

    private val bottomSheetCallback = object : BottomSheetBehavior.BottomSheetCallback() {
        override fun onStateChanged(bottomSheet: View, newState: Int) {
            when (newState) {
                BottomSheetBehavior.STATE_DRAGGING -> {
                    // 当用户开始拖拽时，记录起始Y坐标
                    isDragging = true
                    startDragY = bottomSheet.y
                }
                BottomSheetBehavior.STATE_SETTLING -> {
                    // 当用户松手，视图开始自动滑动时
                    if (isDragging) {
                        isDragging = false

                        // 计算拖拽距离
                        val dragDistance = bottomSheet.y - startDragY

                        // 将20dp转换为像素
                        val thresholdPx = 20.dp

                        // 判断规则
                        // a: 拖拽距离＞20dp → 松手收起
                        // b: 拖拽距离≤20dp → 松手回弹
                        "拖拽距离 $dragDistance".logV("AAAAA")
                        if (dragDistance > thresholdPx) {
                            "拖拽距离大于 $thresholdPx".logV("AAAAA")
                            startCustomExitAnimation()
                        }
                    }
                }
                else -> {
                    // 其他状态，例如 HIDDEN, EXPANDED, COLLAPSED
                }
            }
        }

        override fun onSlide(bottomSheet: View, slideOffset: Float) {
            // slideOffset: -1.0 (Hidden) to 1.0 (Expanded)
            // 你可以在这里根据滑动偏移量做一些其他动画，例如背景透明度变化
        }
    }

}