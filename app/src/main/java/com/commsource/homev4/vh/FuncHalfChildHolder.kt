package com.commsource.homev4.vh

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.ViewGroup
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemHomev4FuncHalfChildBinding
import com.commsource.home.HomeLayoutUtils
import com.commsource.homev4.entity.HomeFuncEntity
import com.commsource.homev4.home.HomeRepo
import com.commsource.util.GlideProxy
import com.commsource.util.gone
import com.commsource.util.resColor
import com.commsource.util.visible
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseViewHolder
import androidx.core.graphics.drawable.toDrawable

class FuncHalfChildHolder(context: Context, parent: ViewGroup?) :
    BaseViewHolder<HomeFuncEntity.HomeFuncItem>(
        context,
        parent,
        R.layout.item_homev4_func_half_child
    ) {

    val binding by lazy { ItemHomev4FuncHalfChildBinding.bind(itemView) }

    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<HomeFuncEntity.HomeFuncItem>,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)
        binding.tvName.text = item.entity.name

        item.entity?.run {
            icon?.let {
                binding.bg.setBackgroundColor(Color.TRANSPARENT)
                binding.iv.visible()
                GlideProxy.with(mContext)
                    .load(it)
                    .placeHolder(R.color.Gray_E.resColor().toDrawable())
                    .requestOptions(HomeLayoutUtils.OPTIONS_TOP_BANNER)
                    .into(binding.iv)
            }

            val bubble = HomeRepo.showBubble(this.id)
            if (bubble != null) {
                // 1. 显示红点 2. 显示文字
                if (bubble.first == 1) {
                    binding.ivRedPoint.visible()
                } else {
                    binding.ivRedPoint.gone()
                }
            } else {
                binding.ivRedPoint.gone()
            }
        }
    }
}
