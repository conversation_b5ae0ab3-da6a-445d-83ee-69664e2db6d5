package com.commsource.homev4.vh

import android.content.Context
import android.view.ViewGroup
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemHomev4FuncHalfBinding
import com.commsource.beautyplus.databinding.ItemHomev4KingkongBinding
import com.commsource.homev3.entity.KingkongEntity
import com.commsource.homev4.entity.HomeFuncEntity
import com.commsource.homev4.home.HomeV4ViewModel
import com.commsource.util.dp
import com.commsource.widget.SpaceVerticalItemDecoration
import com.commsource.widget.recyclerview.AdapterDataBuilder
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.commsource.widget.recyclerview.BaseViewHolder

class FuncHalfViewHolder(context: Context, parent: ViewGroup) :
    BaseViewHolder<HomeFuncEntity>(
        context,
        parent,
        R.layout.item_homev4_func_half
    ) {

    private val viewModel by lazy {
        ViewModelProvider(context as FragmentActivity)[HomeV4ViewModel::class.java]
    }

    private val binding by lazy {
        ItemHomev4FuncHalfBinding.bind(itemView)
    }
    private val baseAdapter by lazy {
        BaseRecyclerViewAdapter(context)
    }

    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<HomeFuncEntity>,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)
        binding.tvTitle.text = item.entity.name
        binding.rvFunc.apply {
            isNestedScrollingEnabled = false
            layoutManager = GridLayoutManager(context, 5)
            adapter = baseAdapter
        }
        baseAdapter.updateItemEntities(
            AdapterDataBuilder.create()
                .addEntities(item.entity.funcItems, FuncHalfChildHolder::class.java)
                .build(),
        )
        baseAdapter.setOnEntityClickListener(KingkongEntity::class.java) { _, _ ->
            true
        }
    }
}