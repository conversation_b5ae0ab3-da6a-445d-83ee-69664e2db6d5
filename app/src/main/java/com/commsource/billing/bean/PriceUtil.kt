package com.commsource.billing.bean

import android.text.TextUtils
import com.commsource.beautyplus.R
import com.commsource.billing.bean.subsconfig.Config
import com.commsource.util.text
import com.meitu.library.util.Debug.Debug
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.text.NumberFormat
import java.text.ParseException

/**
 * author: admin
 * Date: 2023/6/20
 * Des:
 */
object PriceUtil {

    /**
     * 解析带符号的价格，获取数字部分
     *
     * @param price
     * @return
     */
    fun parsePrice(price: String): Float {
        val format = NumberFormat.getInstance()
        return try {
            val number = format.parse(parsePriceString(price))
            number.toFloat()
        } catch (e: ParseException) {
            e.printStackTrace()
            1f
        }
    }


    fun parsePriceString(price: String): String {
        var firstIndex = 0
        var lastIndex = price.length - 1
        var i = 0
        while (i < price.length) {
            if (isArabicDigit(price[i]) || isWesternArabicNumber(price[i])) {
                firstIndex = i
                break
            }
            i++
        }
        var j = lastIndex
        while (j >= 0) {
            if (isArabicDigit(price[j]) || isWesternArabicNumber(price[j])) {
                lastIndex = j
                break
            }
            j--
        }
        return price.substring(firstIndex, lastIndex + 1)
    }

    /**
     * 获取年相对月优惠百分比
     *
     * @return 优惠百分比, 不带%
     */
    fun getAnnualDiscount(subPriceInfo:SubPriceInfo?):String? {
        if (subPriceInfo != null && !TextUtils.isEmpty(subPriceInfo.monthlyPrice)
            && !TextUtils.isEmpty(subPriceInfo.yearlyPrice)
        ) {
            try {
                val monthPrice = parsePrice(subPriceInfo.monthlyPrice)
                val yearPrice = parsePrice(subPriceInfo.yearlyPrice)
                // 1- 年/（月*12）
                val save = ((1 - yearPrice / (monthPrice * 12)) * 100).toInt()
                return if (save > 0 && save < 100) {
                    save.toString() + ""
                } else {
                    null
                }
            } catch (throwable: Throwable) {
                Debug.e(throwable)
            }
        }
        return null
    }

    /**
     * 获取年相对月优惠百分比
     *
     * @return 优惠百分比
     */
    fun getAnnualDiscountPercentage(subPriceInfo: SubPriceInfo?): String? {
        if (subPriceInfo != null && !TextUtils.isEmpty(subPriceInfo.monthlyPrice)
            && !TextUtils.isEmpty(subPriceInfo.yearlyPrice)
        ) {
            try {

                val monthPrice = parsePrice(subPriceInfo.monthlyPrice)
                val yearPrice = parsePrice(subPriceInfo.yearlyPrice)
                // 1- 年/（月*12）
                val save = ((1 - yearPrice / (monthPrice * 12)) * 100).toInt()
                if (save in 1..99) {
                    return R.string.progress_percent.text(save.toString())
                }
                return null
            } catch (throwable: Throwable) {
                Debug.e(throwable)
            }
        }
        return null
    }


    /**
     * 获取首次年优惠价格相对月优惠百分比
     *
     * @return 优惠百分比, 不带%
     */
    fun getIntroductoryAnnualDiscount(subPriceInfo:SubPriceInfo?):String? {
        if (subPriceInfo == null || !subPriceInfo.isYearPeriodicity) {
            return null
        }
        val monthlyPrice = subPriceInfo?.monthlyPrice
        val yearIntroductoryPrice = subPriceInfo?.yearIntroductoryPrice
        if (subPriceInfo != null && !TextUtils.isEmpty(monthlyPrice)
            && !TextUtils.isEmpty(yearIntroductoryPrice)
        ) {
            try {
                val monthPrice = parsePrice(monthlyPrice!!)
                val yearPrice = parsePrice(yearIntroductoryPrice!!)
                // 1- 年/（月*12）
                val save = ((1 - yearPrice / (monthPrice * 12)) * 100).toInt()
                return if (save > 0 && save < 100) {
                    save.toString() + ""
                } else {
                    null
                }
            } catch (throwable: Throwable) {
                Debug.e(throwable)
            }
        }
        return null
    }

    fun getMonthlyOff(subPriceInfo: SubPriceInfo, config: Config?): String? {
        var discountStr: String? = null
        val monthDiscount = subPriceInfo.monthlyDiscount
        if (!TextUtils.isEmpty(monthDiscount) && "100%" != monthDiscount && "0%" != monthDiscount) {
            val discount = (100 - subPriceInfo.monthlyDiscountValue).toString()
            discountStr = R.string.progress_percent.text(discount)
        } else if (!subPriceInfo.isMonthlyFullPriceEmpty) {
            val monthlyPrice = parsePrice(
                if (config?.isClaimed == true && subPriceInfo.isMonthPeriodicity) {
                    subPriceInfo.monthIntroductoryPrice
                } else {
                    subPriceInfo.monthlyPrice
                }
            )
            val monthlyFullPrice = parsePrice(subPriceInfo.monthlyFullPrice)
            val temp = ((1 - monthlyPrice / monthlyFullPrice) * 100).toInt()
            if (temp in 1..99) {
                discountStr = R.string.progress_percent.text(temp.toString())
            }
        }
        return discountStr
    }

    /**
     * 需要将价格转成统一周期。比如年和月价格，需要先将价格转成一致周期，再计算off
     */
    fun getPriceOff(oriPrice: Long, dstPrice: Long): String? {
        var discountStr: String? = null
        val temp = ((1 - dstPrice * 1.0f / oriPrice) * 100).toInt()
        if (temp in 1..99) {
            discountStr = R.string.progress_percent.text(temp.toString())
        }
        return discountStr
    }

    /**
     * 获取年相对月优惠百分比
     *
     * @return 优惠百分比, 不带%
     */
    fun getPriceSave(oriPrice: Long, dstPrice: Long): String? {
        val save = ((1 - dstPrice * 1.0f / oriPrice) * 100).toInt()
        return if (save in 1..99) {
            save.toString() + ""
        } else {
            null
        }
    }

    fun getYearlyOff(subPriceInfo: SubPriceInfo, config: Config?, introductoryPriceEnable:Boolean = false): String? {
        var discountStr: String? = null
        val yearDiscount = subPriceInfo.yearlyDiscount
        if (!TextUtils.isEmpty(yearDiscount) && "100%" != yearDiscount && "0%" != yearDiscount) {
            val discount = (100 - subPriceInfo.yearlyDiscountValue).toString()
            discountStr = R.string.progress_percent.text(discount)
        } else if (!subPriceInfo.isYearlyFullPriceEmpty) {
            val yearlyPrice = parsePrice(
                if ((config?.isClaimed == true || introductoryPriceEnable) && subPriceInfo.isYearPeriodicity) {
                    subPriceInfo.yearIntroductoryPrice
                } else {
                    subPriceInfo.yearlyPrice
                }
            )
            val yearlyFullPrice = parsePrice(subPriceInfo.yearlyFullPrice)
            val temp = ((1 - yearlyPrice / yearlyFullPrice) * 100).toInt()
            if (temp in 1..99) {
                discountStr = R.string.progress_percent.text(temp.toString())
            }
        }
        return discountStr
    }

    fun replaceSubPrice(text: String, subPriceInfo: SubPriceInfo, config: Config):String? {
        val save: String? =
            if (subPriceInfo.hasYearIntroductoryPric() && subPriceInfo.isYearPeriodicity) {
                getIntroductoryAnnualDiscount(subPriceInfo)?.let {
                    R.string.progress_percent.text(it)
                }
            } else {
                getAnnualDiscountPercentage(subPriceInfo)
            }
        val monthOff = getMonthlyOff(subPriceInfo, config)
        val yearOff = getYearlyOff(subPriceInfo, config)
        val monthOfferPrice = subPriceInfo.monthIntroductoryPrice
        val yearOfferPrice = subPriceInfo.yearIntroductoryPrice
        val monthStandardPrice = subPriceInfo.monthlyPrice
        val yearStandardPrice = subPriceInfo.yearlyPrice

        var result:String?=null
        result = replace(text, KEY_SAVE, save)
        result = replace(result, KEY_MONTH_OFF, monthOff)
        result = replace(result, KEY_YEAR_OFF, yearOff)
        result = replace(result, KEY_MONTH_OFFER_PRICE, monthOfferPrice)
        result = replace(result, KEY_YEAR_OFFER_PRICE, yearOfferPrice)
        result = replace(result, KEY_MONTH_STANDARD_PRICE, monthStandardPrice)
        result = replace(result, KEY_YEAR_STANDARD_PRICE, yearStandardPrice)
        return result
    }

    private fun replace(text:String?, key:String, value:String?):String?{
        if (text.isNullOrEmpty()) {
            return null
        }
        var result:String?=text
        if (text.contains(key)) {
            result = if (!TextUtils.isEmpty(value)) {
                text.replace(key, value?:"")
            } else {
                null
            }
        }
        return result
    }

    private const val KEY_SAVE = "{save}"

    private const val KEY_MONTH_OFF = "{month_off}"

    private const val KEY_YEAR_OFF = "{year_off}"
    private const val KEY_MONTH_OFFER_PRICE = "{month_offer_price}"

    private const val KEY_YEAR_OFFER_PRICE = "{year_offer_price}"

    private const val KEY_MONTH_STANDARD_PRICE = "{month_standard_price}"
    private const val KEY_YEAR_STANDARD_PRICE = "{year_standard_price}"


    /**
     * 价格换算，分期付款后的价格
     *
     *
     * 根据商店页返回的小数点位数，保留相同的位数
     *
     * @param price
     * @param count
     * @return
     */
    fun getPrice(price: String?, count: Int): String? {
        if (price == null) return null
        var firstIndex = 0
        var lastIndex = price.length - 1
        var i = 0
        while (i < price.length) {
            if (isArabicDigit(price[i]) || isWesternArabicNumber(price[i])) {
                firstIndex = i
                break
            }
            i++
        }
        var j = lastIndex
        while (j >= 0) {
            if (isArabicDigit(price[j]) || isWesternArabicNumber(price[j])) {
                lastIndex = j
                break
            }
            j--
        }
        var part1 = ""
        var part3 = ""
        if (firstIndex > 0) {
            part1 = price.substring(0, firstIndex)
        }
        if (price.length > lastIndex + 1) {
            part3 = price.substring(lastIndex + 1)
        }
        val valueStr = price.substring(firstIndex, lastIndex + 1)
        val format = NumberFormat.getInstance()
        return try {
            val decimalSymbols = DecimalFormatSymbols()
            val decimalSeparator = decimalSymbols.decimalSeparator
            val floatIndex = valueStr.indexOf(decimalSeparator)
            // 小数点位数
            var floatSize = 0
            if (floatIndex > -1) {
                floatSize = valueStr.length - (floatIndex + 1)
            }
            val number = format.parse(valueStr)
            val newPrice = number.toFloat() / count
            val stringBuilder = StringBuilder("#0")
            if (floatSize > 0) {
                stringBuilder.append(".")
                for (index in 0 until floatSize) {
                    stringBuilder.append("0")
                }
            }
            val part2 = DecimalFormat(stringBuilder.toString()).format(newPrice.toDouble())
            part1 + part2 + part3
        } catch (e: ParseException) {
            e.printStackTrace()
            ""
        }
    }



    /**
     * 获取优惠百分比
     *
     * @return 优惠百分比
     */
    fun getDiscountPercentage(price: String?, oriPrice: String?): String? {
        if (!TextUtils.isEmpty(price)
            && !TextUtils.isEmpty(oriPrice)
        ) {
            try {
                val priceValue = parsePrice(price!!)
                val oriPriceValue = parsePrice(oriPrice!!)
                val value = ((1 - priceValue / oriPriceValue) * 100).toInt() / 100f
                return valueToPercentage(value)
            } catch (throwable: Throwable) {
                Debug.e(throwable)
            }
        }
        return null
    }
    fun valueToPercentage(percentage:Float):String {
        val numberFormat = NumberFormat.getPercentInstance()
        return numberFormat.format(percentage).replace(" ","")
    }


    /**
     * 获取年相对周优惠百分比
     *
     * @return 优惠百分比, 不带%
     */
    fun getYearVsWeekSave(subPriceInfo: SubPriceInfo?): String? {
        if (subPriceInfo != null && !TextUtils.isEmpty(subPriceInfo.weeklyPrice)
            && !TextUtils.isEmpty(subPriceInfo.yearlyPrice)
        ) {
            try {
                val weekPrice = parsePrice(subPriceInfo.weeklyPrice)
                val yearPrice = parsePrice(subPriceInfo.yearlyPrice)
                val save = ((weekPrice - yearPrice / 52) / weekPrice * 100).toInt()
                return if (save > 0 && save < 100) {
                    save.toString() + ""
                } else {
                    null
                }
            } catch (throwable: Throwable) {
                Debug.e(throwable)
            }
        }
        return null
    }

    /**
     * 获取首次年优惠价格相对周优惠百分比
     *
     * @return 优惠百分比, 不带%
     */
    fun getYearIntroductoryVsWeekDiscount(subPriceInfo: SubPriceInfo?): String? {
        if (subPriceInfo == null || !subPriceInfo.isYearPeriodicity) {
            return null
        }
        val weeklyPrice = subPriceInfo?.weeklyPrice
        val yearIntroductoryPrice = subPriceInfo?.yearIntroductoryPrice
        if (subPriceInfo != null && !TextUtils.isEmpty(weeklyPrice)
            && !TextUtils.isEmpty(yearIntroductoryPrice)
        ) {
            try {
                val weekPrice = parsePrice(weeklyPrice!!)
                val yearPrice = parsePrice(yearIntroductoryPrice!!)
                // 1- 年/（月*12）
                val save = ((weekPrice - yearPrice / 52) / weekPrice * 100).toInt()
                return if (save > 0 && save < 100) {
                    save.toString() + ""
                } else {
                    null
                }
            } catch (throwable: Throwable) {
                Debug.e(throwable)
            }
        }
        return null
    }

    @JvmStatic
    fun isArabicDigit(ch: Char): Boolean {
        return ch in '\u0660'..'\u0669'
    }

    @JvmStatic
    fun isWesternArabicNumber(ch: Char): Boolean {
        return ch in '0'..'9'
    }

    fun splitPrice(price: String): Triple<String, String, String> {
        var firstIndex = 0
        var lastIndex = price.length - 1
        var i = 0
        while (i < price.length) {
            if (isArabicDigit(price[i]) || isWesternArabicNumber(price[i])) {
                firstIndex = i
                break
            }
            i++
        }
        var j = lastIndex
        while (j >= 0) {
            if (isArabicDigit(price[j]) || isWesternArabicNumber(price[j])) {
                lastIndex = j
                break
            }
            j--
        }
        var part1 = ""
        var part3 = ""
        if (firstIndex > 0) {
            part1 = price.substring(0, firstIndex)
        }
        if (price.length > lastIndex + 1) {
            part3 = price.substring(lastIndex + 1)
        }
        val part2 = price.substring(firstIndex, lastIndex + 1)
        return Triple(part1, part2, part3)
    }
}