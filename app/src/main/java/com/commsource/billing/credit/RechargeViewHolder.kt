package com.commsource.billing.credit

import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.text.TextUtils
import android.util.TypedValue
import android.view.ViewGroup
import androidx.core.view.doOnNextLayout
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemRechargeViewHolderBinding
import com.commsource.billing.bean.PriceUtil
import com.commsource.billing.bean.formatPrice
import com.commsource.config.SubscribeConfig
import com.commsource.statistics.Meepo
import com.commsource.util.LanguageUtil
import com.commsource.util.RTLTool
import com.commsource.util.ViewUtils
import com.commsource.util.XDrawableFactory
import com.commsource.util.dpf
import com.commsource.util.gone
import com.commsource.util.isRtl
import com.commsource.util.resColor
import com.commsource.util.setCornerRadius
import com.commsource.util.setMargin
import com.commsource.util.setMarginCompat
import com.commsource.util.text
import com.commsource.util.visible
import com.commsource.widget.list.XItem
import com.commsource.widget.list.XViewHolder
import com.pixocial.androidx.core.extension.dp

/**
 * author: admin
 * Date: 2023/9/26
 * Des:
 */
class RechargeViewHolder(val context: Context, parent: ViewGroup) :
    XViewHolder<RechargeItemModel>(context, parent, R.layout.item_recharge_view_holder) {

    private val binding = ItemRechargeViewHolderBinding.bind(itemView).apply {
        if (ccTag.isRtl()) {
            ccTag.updateCorner(0f, 4.dpf, 4.dpf, 0f)
        } else {
            ccTag.updateCorner(4.dpf, 0f, 0f, 4.dpf)
        }
    }

    override fun onBindViewHolder(
        position: Int,
        item: XItem<RechargeItemModel>,
        payloads: List<Any>?
    ) {
        if (item.entity.product == null) {
            binding.vLoading.visible()
            binding.ccTag.gone()
            binding.rlItem.gone()
            binding.ccTag.gone()
            binding.ccTag.doOnNextLayout { }
            binding.flContent.isStrokeEnable = false
        } else {
            binding.rlItem.visible()
            item.entity.let {
                binding.tvCampaign.gone()
                val campaignSku = it.campaignSku
                binding.tvQuantity.setMarginCompat(top = 27.dp)
                val quantity = it.creditSkuModel?.quantity ?: return
                if (campaignSku?.isDiscount() == true) {
                    // 折扣营销
                    // 标签，原价和现价，无标签则显示折扣信息
                    binding.tvCampaign.text = it.product?.formatPrice() ?: ""
                    it.campaignProduct?.let {
                        binding.tvCampaign.paint.isStrikeThruText = true
                        binding.tvCampaign.background =
                            XDrawableFactory.getDrawable(0xff303233.toInt(), 4.dpf)
                        binding.tvQuantity.setMarginCompat(top = 24.dp)
                        binding.tvCampaign.setTextColor(0xff666666.toInt())
                        binding.tvCampaign.setTypeface(null, Typeface.NORMAL)
                        binding.tvCampaign.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12f)
                        binding.tvPerPrice.text =
                            R.string.t_price_per_credit.text(
                                PriceUtil.getPrice(
                                    it.price,
                                    quantity
                                )
                            )
                        binding.tvCampaign.visible()
                        binding.tvPrice.text = it.formatPrice()
                    }

                } else {
                    if (campaignSku != null) {
                        // 买赠营销
                        binding.tvQuantity.setMarginCompat(top = 24.dp)
                        binding.tvCampaign.paint.isStrikeThruText = false
                        binding.tvCampaign.background = XDrawableFactory.getProButtonDrawable(4.dpf)
                        binding.tvCampaign.setTextColor(Color.BLACK)
                        binding.tvCampaign.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 10f)
                        binding.tvPerPrice.text = R.string.t_price_per_credit.text(
                            PriceUtil.getPrice(
                                it.product?.price,
                                quantity + campaignSku.bonusCredit
                            )
                        )
                        binding.tvCampaign.setTypeface(null, Typeface.BOLD_ITALIC)
                        binding.tvCampaign.text =
                            R.string.t_credit_extra.text(campaignSku.bonusCredit.toString())
                        binding.tvCampaign.visible()
                        if (LanguageUtil.LANGUAGE_JP_NEW == LanguageUtil.getLanguage(
                                context,
                                true
                            )
                        ) {
                            //粗斜体 右侧类汉字文字会被截断
                            binding.tvCampaign.run {
                                val width =
                                    binding.tvCampaign.paint.measureText(binding.tvCampaign.text.toString()) + 30
                                ViewUtils.setWidth(this, width.toInt())
                            }
                        }

                    } else {
                        // 无营销
                        binding.tvQuantity.setMarginCompat(top = 27.dp)
                        binding.tvPerPrice.text = R.string.t_price_per_credit.text(
                            PriceUtil.getPrice(
                                it.product?.price,
                                quantity
                            )
                        )
                    }
                    binding.tvPrice.text = it.product?.price
                }
                binding.tvTag.text = ""
                val label = campaignSku?.label
                val skuLabel = it.creditSkuModel?.label
                if (!TextUtils.isEmpty(label)) {
                    binding.tvTag.text = label
                } else if (campaignSku?.isDiscount() == true) {
                    PriceUtil.getPriceOff(
                        it.product?.price_amount_micros ?: 1L,
                        it.campaignProduct?.price_amount_micros ?: 0L
                    )?.let {
                        binding.tvTag.text = R.string.t_discount_off.text(it.replace("%", ""))
                    }
                } else if (!TextUtils.isEmpty(skuLabel)) {
                    binding.tvTag.text = skuLabel
                } else if (it.recommend) {
                    binding.tvTag.text = R.string.t_credit_badge_recommend.text() + " "
                }

                it.creditSkuModel?.let {
                    binding.tvQuantity.text = it.quantity.toString()
                }
                binding.vLoading.gone()
            }

            if (item.isSelect) {
                selected()
                binding.tvTag.background = XDrawableFactory.getRechargeDrawable(0f)
                binding.tvTag.setTextColor(Color.WHITE)
                binding.ivTagSuffix.setImageResource(R.drawable.ic_pink_suffix)
            } else {
                unSelected()
                binding.tvTag.background = XDrawableFactory.getDrawable(0xff303233.toInt(), 0f)
                binding.tvTag.setTextColor(R.color.color_999999.resColor())
                binding.ivTagSuffix.setImageResource(R.drawable.ic_gray_suffix)
            }
            if (binding.tvTag.text.isNotEmpty() ) {
                binding.ccTag.visible()
            } else {
                binding.ccTag.gone()
            }
        }

    }


    private fun selected() {

        binding.tvQuantity.setTextColor(R.color.white.resColor())
        binding.tvCredits.setTextColor(R.color.white.resColor())
        binding.tvPrice.setTextColor(R.color.white.resColor())
        binding.tvPerPrice.setTextColor(R.color.white.resColor())

        binding.flContent.isStrokeEnable = true
//        binding.tvTag.background = XDrawableFactory.getRechargeDrawable(0f)
//        binding.tvTag.setTextColor(Color.WHITE)
//        binding.ivTagSuffix.setImageResource(R.drawable.ic_pink_suffix)
        binding.tvPrice.setMarginCompat(top = 8.dp)
        binding.tvPerPrice.visible()

    }

    private fun unSelected() {
        binding.flContent.isStrokeEnable = false
        binding.tvQuantity.setTextColor(R.color.white50.resColor())
        binding.tvCredits.setTextColor(R.color.white50.resColor())
        binding.tvPrice.setTextColor(R.color.Gray_B.resColor())
        binding.tvPerPrice.setTextColor(R.color.Gray_D.resColor())
//        binding.tvTag.background = XDrawableFactory.getDrawable(0xff303233.toInt(), 0f)
//        binding.tvTag.setTextColor(R.color.color_999999.resColor())
//        binding.ivTagSuffix.setImageResource(R.drawable.ic_gray_suffix)
        binding.tvPrice.setMarginCompat(top = 15.dp)
        binding.tvPerPrice.gone()
    }

}