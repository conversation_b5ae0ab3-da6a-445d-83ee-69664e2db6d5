package com.commsource.store.filter.search

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.LayoutFilterFunSearchResultBinding
import com.pixocial.camerasuite.commsource.camera.mvp.annotation.CameraRatioType
import com.commsource.camera.xcamera.cover.bottomFunction.effect.filter.BaseFilterViewHolder
import com.commsource.camera.xcamera.cover.bottomFunction.effect.filter.CameraFilterViewModel
import com.commsource.camera.xcamera.cover.bottomFunction.effect.filter.NormalFilterViewHolder
import com.commsource.repository.child.filter.FilterRepository
import com.commsource.repository.child.filter.FilterWrapper
import com.commsource.search_common.SearchConst
import com.commsource.studio.ImageStudioActivity
import com.commsource.studio.ImageStudioViewModel
import com.commsource.studio.component.ConfirmCancelComponent
import com.commsource.util.MaterialVisibleTracker
import com.commsource.util.ViewShowState
import com.commsource.util.dp
import com.commsource.util.gone
import com.commsource.util.setMarginCompat
import com.commsource.util.text
import com.commsource.util.visible
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter

class FilterEditFunSearchResultLayer : AbsSearchResultLayer<List<FilterV4GoodId>>() {

    private var applyFilterBeforeSearch: FilterWrapper? = null

    private val binding by lazy {
        LayoutFilterFunSearchResultBinding.inflate(layoutInflater)
    }

    private val filterViewModel: CameraFilterViewModel by lazy {
        ViewModelProvider(owner).get(
            CameraFilterViewModel::class.java
        )
    }

    private val baseAdapter by lazy {
        BaseRecyclerViewAdapter(context)
    }

    private val confirmCancelViewModel by lazy {
        ViewModelProvider(owner)[ConfirmCancelComponent.ConfirmCancelViewModel::class.java]
    }


    private val searchViewModel by lazy {
        ViewModelProvider(fragment)[FilterSearchRecordViewModel::class.java]
    }


    private val materialVisibleTracker by lazy {
        object : MaterialVisibleTracker<String>(isHorizon = true, visibleArray = filterViewModel.searchTrackLogMap) {
            override fun isScrollCheck(): Boolean {
                return true
            }

            override fun onCallback(int: Int?, viewHolder: RecyclerView.ViewHolder?) {
                if (viewHolder is NormalFilterViewHolder && int == ViewShowState.SHOW_COMPLETE) {
                    viewHolder.item.entity?.let { entity ->
                        if (isFirstVisible(entity.filter.id)) {
                            entity.let {
                                searchViewModel.logImplEvent(
                                    false, config.enterFrom == SearchConst.ENTRANCE_FUNCTION,
                                    keyWord, entity.filter.id
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    override fun getContentLayoutId(): Int {
        return R.layout.layout_filter_fun_search_result
    }

    override fun onCreateView(context: Context, parent: ViewGroup): View {
        applyFilterBeforeSearch = filterViewModel.applyFilterEvent.value
        acceptData?.map {
            FilterRepository.getFilterWrapper(it.g_id)
        }?.filter { it?.filter?.pictureEnable() == true }?.filterNotNull()?.let { list ->
            binding.rv.apply {
                filterViewModel.searchTrackLogMap.clear()
                layoutManager = LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
                adapter = baseAdapter.apply {
                    addTag(BaseFilterViewHolder.KEY_RATIO, CameraRatioType.PICTURE_RATIO_1_1)
                    setSingleItemEntities(list, NormalFilterViewHolder::class.java)
                    setOnEntityClickListener(FilterWrapper::class.java) { pos, entity ->
                        entity.applyFromSpecialCategory = "BP_cat_FIL_SCH"
                        filterViewModel.clickFilter(entity, true)
                        searchViewModel.logClickEvent(
                            false, config.enterFrom == SearchConst.ENTRANCE_FUNCTION,
                            keyWord, entity.filter.id
                        )
                        true
                    }

                    setOnItemPressChangeListener { isPress, position, item ->
                        if (!isPress) return@setOnItemPressChangeListener
                        val entity = item.entity as FilterWrapper
                        val pos = filterViewModel.newFilterData.getFilterPosition(entity)
                        if (entity.filter.needDownload()) {
                            filterViewModel.clickFilter(entity, true)
                            searchViewModel.logClickEvent(
                                false, config.enterFrom == SearchConst.ENTRANCE_FUNCTION,
                                keyWord, entity.filter.id
                            )
                        } else {
                            filterViewModel.onLongPressEvent.value = Pair(entity, pos)
                        }
                        baseAdapter.notifyItemChanged(position, NormalFilterViewHolder.PAYLOAD_FILTER_FAVORITE)
                    }
                }
                materialVisibleTracker.bindRecyclerView(this)
            }
            // 默认选中
            applyFilterBeforeSearch?.takeIf { !it.filter.isOriginalFilter() }?.let { wrapper ->
                list.find { it.filter.id == wrapper.filter.id }?.let {
                    binding.rv.post {
                        baseAdapter.currentSelectEntity = it
                    }
                    showConfirmCancel()
                }
            }
        }

        binding.confirmCancelLayout.pivCancel.setOnClickListener {
            binding.confirmCancelLayout.root.gone()
            searchViewModel.closeSearchPage()
            confirmCancelViewModel.cancelEvent.value = true
            applyFilterBeforeSearch = null
        }

        binding.confirmCancelLayout.pivApply.setOnClickListener {
            binding.confirmCancelLayout.root.gone()
            // 点击确认滚动到指定位置。
            (baseAdapter.currentSelectEntity as? FilterWrapper)?.let {
                val pos = filterViewModel.newFilterData.getFilterPosition(it)
                filterViewModel.applySmoothScrollPositionEvent.value = pos
            }
            searchViewModel.closeSearchPage()
            confirmCancelViewModel.confirmEvent.value = true
        }

        if (context is ImageStudioActivity) {
            val viewModel = ViewModelProvider(owner)[ImageStudioViewModel::class.java]
            if (viewModel.isBottomAdShowing) {
                binding.confirmCancelLayout.root.setMarginCompat(top = 50.dp())
            }
        }
        return binding.root
    }

    val observer = Observer<FilterWrapper?> { wrapper ->
        wrapper?.takeIf { !it.filter.isOriginalFilter() }?.let {
            showConfirmCancel()
        }
        baseAdapter.currentSelectEntity = wrapper
    }

    private val startDownloadObserver = Observer<FilterWrapper?> { wrapper ->
        baseAdapter.notifyItemChanged(wrapper, NormalFilterViewHolder.PAYLOAD_FILTER_DOWNLOADING)
    }
    private val failDownloadObserver = Observer<FilterWrapper?> { wrapper ->
        baseAdapter.notifyItemChanged(wrapper, NormalFilterViewHolder.PAYLOAD_FILTER_DOWNLOAD_FAILED)
    }
    private val successDownloadObserver = Observer<FilterWrapper?> { wrapper ->
        baseAdapter.notifyItemChanged(wrapper, NormalFilterViewHolder.PAYLOAD_FILTER_DOWNLOAD_SUCCESS)
    }

    override fun onAttach() {
        filterViewModel.applyFilterEvent.observe(fragment.viewLifecycleOwner, observer)
        FilterRepository.downloadObserver.apply {
            startEvent.observe(fragment.viewLifecycleOwner, startDownloadObserver)
            successEvent.observe(fragment.viewLifecycleOwner, successDownloadObserver)
            failedEvent.observe(fragment.viewLifecycleOwner, failDownloadObserver)
        }

        searchViewModel.cancelSearchEvent.observe(fragment.viewLifecycleOwner) {
            if (it) {
                // 点击确认滚动到指定位置。
                (baseAdapter.currentSelectEntity as? FilterWrapper)?.let {
                    val pos = filterViewModel.newFilterData.getFilterPosition(it)
                    filterViewModel.applySmoothScrollPositionEvent.value = pos
                }
                // 关闭搜索结果页面。保持编辑状态
                binding.confirmCancelLayout.root.gone()
            }
        }
    }

    override fun onDetach() {
        filterViewModel.applyFilterEvent.removeObserver(observer)
        FilterRepository.downloadObserver.apply {
            startEvent.removeObserver(startDownloadObserver)
            successEvent.removeObserver(successDownloadObserver)
            failedEvent.removeObserver(failDownloadObserver)
        }
    }


    private fun showConfirmCancel() {
        binding.confirmCancelLayout.root.visible()
        binding.confirmCancelLayout.tvName.text = R.string.filter.text()
        binding.confirmCancelLayout.ivHelp.gone()
    }
}