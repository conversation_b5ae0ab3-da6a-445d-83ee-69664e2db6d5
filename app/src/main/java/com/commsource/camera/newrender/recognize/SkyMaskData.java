package com.commsource.camera.newrender.recognize;

import android.graphics.Bitmap;

import com.commsource.easyeditor.utils.opengl.TextureEntity;

public class SkyMaskData {
    private int width;
    private int height;
    private TextureEntity textureEntity;

    private float maskDownBorder = 0;

    private Bitmap maskBitmap;

    public int getTextureId() {
        if (textureEntity == null) {
            return -1;
        }
        return textureEntity.textureId;
    }

    public void setTextureId(int textureId) {
        textureEntity = new TextureEntity(textureId, width, height);
    }

    public void setTextureEntity(TextureEntity textureEntity) {
        this.textureEntity = textureEntity;
    }

    public Bitmap getMaskBitmap() {
        return maskBitmap;
    }

    public void setMaskBitmap(Bitmap maskBitmap) {
        this.maskBitmap = maskBitmap;
    }

    public TextureEntity getTextureEntity() {
        return textureEntity;
    }


    public float getMaskDownBorder() {
        return maskDownBorder;
    }

    public void setMaskDownBorder(float maskDownBorder) {
        this.maskDownBorder = maskDownBorder;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public void release() {
        if (textureEntity != null) {
            textureEntity.release();
        }
        textureEntity = null;
    }
}
