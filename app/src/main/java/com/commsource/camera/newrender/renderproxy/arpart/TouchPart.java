package com.commsource.camera.newrender.renderproxy.arpart;

import android.graphics.Rect;
import android.view.MotionEvent;

import com.commsource.camera.newrender.recognize.RegionData;
import com.commsource.camera.render.ARTouchDataCallback;
import com.commsource.camera.render.ARTouchDataManager;

/**
 * AR模块中的触摸事件管理。
 */
public class TouchPart extends BaseArFunctionPart {
    /**
     * 触摸时间管理。
     */
    private ARTouchDataManager mTouchDataManager;

    public TouchPart() {
        mTouchDataManager = new ARTouchDataManager();
        mTouchDataManager.setTouchCallbackObject(new ARTouchCallback());
    }

    public Rect getDisplayRect() {
        RegionData regionData = getArRenderProxy().getRecognizeData(RegionData.class);
        if (regionData != null) {
            return regionData.getDisplayRect();
        }
        return null;
    }

    private class ARTouchCallback implements ARTouchDataCallback {

        @Override
        public void touchBegin(float x, float y, int pointer) {
            if (getArRenderProxy().isArKernelPrepared()) {
                getArRenderProxy().getARKernelInterface().onTouchBegin(x, y, pointer);
            }
        }

        @Override
        public void touchMove(float x, float y, int pointer) {
            if (getArRenderProxy().isArKernelPrepared()) {
                getArRenderProxy().getARKernelInterface().onTouchMove(x, y, pointer);
            }
        }

        @Override
        public void touchEnd(float x, float y, int pointer) {
            if (getArRenderProxy().isArKernelPrepared()) {
                getArRenderProxy().getARKernelInterface().onTouchEnd(x, y, pointer);
            }
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (event == null) {
            return false;
        }

        if (getArRenderProxy().getDetectorListener() != null) {
            getArRenderProxy().getDetectorListener().onTouchEvent(event);
        }
        int offsetX = 0;
        int offsetY = 0;
        final Rect displayRect = getDisplayRect();
        if (displayRect != null) {
            offsetX = displayRect.left;
            offsetY = displayRect.top;
            mTouchDataManager.setViewRect(displayRect);
        }
        final int pointerCount = event.getPointerCount();
        final int actionMasked = event.getActionMasked();
        // 平移事件位置 修图模式中 不主动偏移
        if (offsetX != 0 || offsetY != 0) {
            event.offsetLocation(-offsetX, -offsetY);
        }
        switch (actionMasked) {
            case MotionEvent.ACTION_DOWN:
            case MotionEvent.ACTION_POINTER_DOWN: {
                for (int i = 0; i < pointerCount; i++) {
                    int pointerId = event.getPointerId(i);
                    int x = (int) (event.getX(i) + 0.5F);
                    int y = (int) (event.getY(i) + 0.5F);
                    mTouchDataManager.touchManager(false, x, y, pointerId);
                }
                break;
            }
            case MotionEvent.ACTION_MOVE: {
                for (int i = 0; i < pointerCount; i++) {
                    int pointerId = event.getPointerId(i);
                    int x = (int) (event.getX(i) + 0.5F);
                    int y = (int) (event.getY(i) + 0.5F);
                    mTouchDataManager.touchManager(false, x, y, pointerId);
                }
                break;
            }
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_POINTER_UP:
            case MotionEvent.ACTION_CANCEL: {
                int pointerId = event.getPointerId(event.getActionIndex());
                int x = (int) (event.getX() + 0.5F);
                int y = (int) (event.getY() + 0.5F);
                mTouchDataManager.touchManager(true, x, y, pointerId);
                break;
            }
        }
        // 恢复原事件位置
        if (offsetX != 0 || offsetY != 0) {
            event.offsetLocation(offsetX, offsetY);
        }
        return true;
    }
}
