package com.commsource.camera.render;

import android.graphics.Rect;
import androidx.annotation.NonNull;

import com.meitu.library.util.Debug.Debug;

/**
 * AR底层所需要的touch事件管理
 * 适用于各类view裁剪后的touch坐标换算
 * 其原理是在显示的view区域内模拟追踪手势touch事件
 */
public class ARTouchDataManager {

    // 限制手指最大数量
    private final static int TOUCH_POINTS_MAX = 10;

    // 无效手指索引
    private final static int TOUCH_INVALID_POINTER = 10;

    // 触摸管理
    private class TouchManager {
        boolean down;
        int x;
        int y;
        int pointer;
        int touchID;
    }

    // 处理结果回调对象
    private ARTouchDataCallback mTouchCallback;

    // view宽
    private int mViewWidth;

    // view高
    private int mViewHeight;

    // 触摸管理数组
    private TouchManager[] mTouchManagers = new TouchManager[TOUCH_POINTS_MAX];

    public ARTouchDataManager() {
        for (int i = 0; i < TOUCH_POINTS_MAX; i++) {
            mTouchManagers[i] = new TouchManager();
        }
    }

    /**
     * 设置触摸回调对象
     * @param touchDataCallback 实现了ARTouchDataCallback回调的对象
     */
    public void setTouchCallbackObject(ARTouchDataCallback touchDataCallback) {
        mTouchCallback = touchDataCallback;
    }

    /**
     * 设置view rect
     *
     * @param viewRect view区域
     */
    public void setViewRect(@NonNull Rect viewRect) {
        mViewWidth = viewRect.width();
        mViewHeight = viewRect.height();

    }

    /**
     * 结束所有touch事件
     */
    public void endForAllTouchEvents() {
        for (int pointer = 0; pointer < TOUCH_POINTS_MAX; pointer++) {
            if (mTouchManagers[pointer].down) {
                mTouchManagers[pointer].down = false;
                touchEndDispatch(mTouchManagers[pointer].x, mTouchManagers[pointer].y, pointer);
            }
        }
    }

    /**
     * 触摸事件处理
     * @param ended     是否结束事件
     * @param x         触摸点x
     * @param y         触摸点y
     * @param touchID   外部触摸id
     */
    public void touchManager(boolean ended, int x, int y, int touchID) {
        // 是否触摸在view范围内
        boolean isInTheView = touchInTheView(x, y);

        // 先查找是否在manager中存在
        int pointer = 0;
        for (pointer = 0; pointer < TOUCH_POINTS_MAX; pointer++) {
            if (mTouchManagers[pointer].down && mTouchManagers[pointer].touchID == touchID) {
                break;
            }
        }

        // 假如不存在的话,说明是新增的手指
        if (pointer == TOUCH_INVALID_POINTER) {
            // 假如在范围内,那么就要开始记录并追踪手指
            if (isInTheView) {
                // 找一根还没用的手指索引
                pointer = 0;
                while (pointer < TOUCH_POINTS_MAX && mTouchManagers[pointer].down) {
                    pointer++;
                }
                // 如果找到空手指,那么使用这根手指并追踪
                if (pointer != TOUCH_INVALID_POINTER) {
                    mTouchManagers[pointer].down = true;
                    mTouchManagers[pointer].x = x;
                    mTouchManagers[pointer].y = y;
                    mTouchManagers[pointer].pointer = pointer;
                    mTouchManagers[pointer].touchID = touchID;

                    touchBeginDispatch(x, y, pointer);

                    // 如果这根手指是结束状态，那么再发送一个结束事件
                    if (ended) {
                        mTouchManagers[pointer].down = false;
                        touchEndDispatch(x, y, pointer);
                    }
                }
            }
            return;
        }

        // 假如存在的话,说明处于追踪状态
        // 如果在范围内,那么需要继续追踪
        if (isInTheView) {
            // 如果外部追踪处于结束状态,那么发送结束事件
            if (ended) {
                mTouchManagers[pointer].down = false;
                touchEndDispatch(x, y, pointer);
            }
            // 否则的话,move事件继续追踪
            else {
                mTouchManagers[pointer].x = x;
                mTouchManagers[pointer].y = y;
                touchMoveDispatch(x, y, pointer);
            }
        }
        // 不在范围内的话,要停止追踪
        else {
            mTouchManagers[pointer].down = false;
            touchEndDispatch(touchLimitX(x), touchLimitY(y), pointer);
        }
    }

    /**
     * 是否在view上面
     * @param x 触摸点x
     * @param y 触摸点y
     * @return  true or false
     */
    private boolean touchInTheView(int x, int y) {
        if (x < 0) {
            return false;
        }
        if (y < 0) {
            return false;
        }
        if (x > mViewWidth) {
            return false;
        }
        if (y > mViewHeight) {
            return false;
        }

        return true;
    }

    /**
     * 限制触摸坐标x在范围内
     * @param x 原始触摸点x
     * @return  限制区域后的x
     */
    private int touchLimitX(int x) {
        if (x < 0) {
            return 0;
        }
        if (x > mViewWidth) {
            return mViewWidth;
        }
        return x;
    }

    /**
     * 限制触摸坐标y在范围内
     * @param y 原始触摸点y
     * @return  限制区域后的y
     */
    private int touchLimitY(int y) {
        if (y < 0) {
            return 0;
        }
        if (y > mViewHeight) {
            return mViewHeight;
        }
        return y;
    }

    /**
     * touchBegin触摸事件分发
     * @param x         修正后的触摸点x
     * @param y         修正后的触摸点y
     * @param pointer   修正后的触摸手指id
     */
    private void touchBeginDispatch(int x, int y, int pointer) {
        if (mTouchCallback != null) {
            mTouchCallback.touchBegin((float) x / mViewWidth, (float) y / mViewHeight, pointer);
        }
    }

    /**
     * touchMove触摸事件分发
     * @param x         修正后的触摸点x
     * @param y         修正后的触摸点y
     * @param pointer   修正后的触摸手指id
     */
    private void touchMoveDispatch(int x, int y, int pointer) {
        if (mTouchCallback != null) {
            mTouchCallback.touchMove((float) x / mViewWidth, (float) y / mViewHeight, pointer);
        }
    }

    /**
     * touchEnd触摸事件分发
     * @param x         修正后的触摸点x
     * @param y         修正后的触摸点y
     * @param pointer   修正后的触摸手指id
     */
    private void touchEndDispatch(int x, int y, int pointer) {
        if (mTouchCallback != null) {
            mTouchCallback.touchEnd((float) x / mViewWidth, (float) y / mViewHeight, pointer);
        }
    }

}
