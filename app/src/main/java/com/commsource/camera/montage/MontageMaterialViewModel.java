package com.commsource.camera.montage;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.MutableLiveData;

import com.commsource.beautyplus.R;
import com.commsource.camera.montage.bean.MontageSuitConfig;
import com.commsource.camera.xcamera.cover.bottomFunction.BottomFunction;
import com.commsource.config.SelfieConfig;
import com.commsource.material.DownloadQueue;
import com.commsource.material.download.request.MaterialRequest;
import com.commsource.material.download.request.OnDownloadListener;
import com.commsource.material.download.task.CommonDownloadTask;
import com.commsource.statistics.MTAnalyticsAgent;
import com.commsource.statistics.constant.MTAnalyticsConstant;
import com.commsource.util.ThreadExecutor;
import com.commsource.util.thread.AbstractNamedRunnable;
import com.meitu.common.AppContext;
import com.meitu.library.util.net.NetUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class MontageMaterialViewModel extends AndroidViewModel {
    private final int[] colors =
        new int[] {R.color.skin_1, R.color.skin_2, R.color.skin_3, R.color.skin_4, R.color.skin_5, R.color.skin_6};
    private MontageRepository montageRepository;
    private List<MontageGroupEntity> montageGroupList;
    private MutableLiveData<List<MontageGroupEntity>> materialGroups = new MutableLiveData<>();
    private MutableLiveData<MontageMaterialEntity> onMaterialClickEvent = new MutableLiveData<>();
    private MutableLiveData<MontageMaterialEntity> downloadProgressEvent = new MutableLiveData<>();
    private MutableLiveData<Boolean> needPaidEvent = new MutableLiveData<>();
    private MutableLiveData<String> onBgChangeToCustom = new MutableLiveData<>();
    private DownloadQueue mDownloadManager;
    private HashMap<String, Integer> groupPositionMap = new HashMap<>(16);
    private HashMap<String, Integer> groupTypeMap = new HashMap<>(16);
    private HashMap<String, List<MontageMaterialEntity>> materialMap = new HashMap<>(16);
    private List<String> manShyIds = new ArrayList<>();
    private List<String> eventQueue = new ArrayList<>();
    private List<String> needPaidMaterials = new ArrayList<>();
    private int selectedType;

    private boolean isNeedReset;

    private BottomFunction lastFunction = BottomFunction.AR;

    public BottomFunction getLastFunction() {
        return lastFunction;
    }

    public MutableLiveData<Boolean> getNeedPaidEvent() {
        return needPaidEvent;
    }

    public void setNeedPaidEvent(MutableLiveData<Boolean> needPaidEvent) {
        this.needPaidEvent = needPaidEvent;
    }

    public void setLastFunction(BottomFunction lastFunction) {
        this.lastFunction = lastFunction;
    }

    public boolean isNeedReset() {
        return isNeedReset;
    }

    public void setNeedReset(boolean needReset) {
        isNeedReset = needReset;
    }

    public MutableLiveData<String> getOnBgChangeToCustom() {
        return onBgChangeToCustom;
    }

    public MutableLiveData<List<MontageGroupEntity>> getMontageMaterialGroupsEvent() {
        return materialGroups;
    }

    public MutableLiveData<MontageMaterialEntity> getDownloadProgressEvent() {
        return downloadProgressEvent;
    }

    public MutableLiveData<MontageMaterialEntity> getOnMaterialClickEvent() {
        return onMaterialClickEvent;
    }

    public List<MontageGroupEntity> getCurrentMontageGroup() {
        return montageGroupList;
    }

    public MontageMaterialViewModel(@NonNull Application application) {
        super(application);
        mDownloadManager = new DownloadQueue();
        montageRepository = MontageRepository.newInstance();
        montageGroupList = new ArrayList<>();
    }

    /**
     *  点击素材类型的事件
     *  action;Type;path
     *  RELOAD;EYE;Documents/ARMaterial/Eyes/Eye200375/
     */
    public List<String> produceSelectMaterialEvent(MontageMaterialEntity material) {
        int groupType = getGroupTypeByCategoryId(material.getCategoryId());
        StringBuilder builder = new StringBuilder();
        eventQueue.clear();
        // action
        builder.append("RELOAD;");
        // 目标路径
        String targetPath;
        String needRecordId;
        if (material.isAiEntity()) {
            // ai icon 下 判断使用默认路径 还是清除的图片 （清除图片用一张透明的图）
            if (groupType == MontageConfig.MontageGroupType.MOUSTACHE
                || groupType == MontageConfig.MontageGroupType.FRECKLE
                || groupType == MontageConfig.MontageGroupType.MOLE
                || groupType == MontageConfig.MontageGroupType.FOREGROUND) {
                targetPath = material.isSelected() ? material.getDefaultMaterialPath()
                    : MontageConfig.getClearMaterialPathWithGroupType(groupType);
            } else {
                targetPath = material.isSelected() ? material.getDefaultMaterialPath()
                    : MontageConfig.getClearMaterialPathWithMaterialType(material.getMaterialType());
            }
            needRecordId = material.isSelected() ? MontageSuitConfig.LOGEVENT_AI_ID : null;
        } else {
            // 不是ai icon判断使用自身素材路径 还是清除的图片
            // ai icon 下 判断使用默认路径 还是清除的图片 （清除图片用一张透明的图）
            if (groupType == MontageConfig.MontageGroupType.MOUSTACHE
                || groupType == MontageConfig.MontageGroupType.FRECKLE
                || groupType == MontageConfig.MontageGroupType.MOLE
                || groupType == MontageConfig.MontageGroupType.FOREGROUND) {
                targetPath = material.isSelected() ? material.getSavedLocalPath()
                    : MontageConfig.getClearMaterialPathWithGroupType(groupType);
            } else {
                targetPath = material.isSelected() ? material.getSavedLocalPath()
                    : MontageConfig.getClearMaterialPathWithMaterialType(material.getMaterialType());
            }

            needRecordId = material.isSelected() ? material.getMaterialId() : null;
        }

        if (groupType == MontageConfig.MontageGroupType.ACCESSORIES) {
            // 由于配饰分类下含有多个子分类
            // 配饰分类下 有可能反选，取消了素材。

            // 记录下该类选中素材的路径
            MontageSuitConfig.getMontageSuitConfig().setAccessoriesMaterialLocalPath(material.getMaterialType(),
                targetPath);
            // 记录配饰素材的Id
            MontageSuitConfig.getMontageSuitConfig().recordAccessoriesType(material.getMaterialType(), needRecordId);
            // append 分类
            builder.append(MontageConfig.getActualNameByMaterialType(material.getMaterialType()));
            // 设置眼镜是否选中，同步到json
            if (material.getMaterialType() == MontageConfig.MontageMaterialType.GLASS) {
                MontageSuitConfig.getMontageSuitConfig().setHasGlasses(material.isSelected());
            }
        } else {
            // 不是配饰分类放到另外一个SpareArray
            MontageSuitConfig suitConfig = MontageSuitConfig.getMontageSuitConfig();
            suitConfig.setOtherMaterialLocalPath(groupType, targetPath);
            // 记录对应的素材ID。打点使用
            suitConfig.recordCommonType(material.getCategoryId(), needRecordId);
            // append 分类
            builder.append(MontageConfig.getActualNameByGroupType(groupType));
            // 设置嘴巴、眼睛、腮红的类型到json
            if (groupType == MontageConfig.MontageGroupType.EYE || groupType == MontageConfig.MontageGroupType.MOUTH
                || groupType == MontageConfig.MontageGroupType.BLUSH) {

                if (material.isAiEntity()) {
                    // 如果是Ai 实体 就是默认的Type
                    suitConfig.setKindType(groupType, suitConfig.getDefaultKindByType(groupType));
                } else {
                    int targetType = getTypeFromMaterialId(groupType, material.getMaterialId());
                    // 对应种类同步回json。
                    suitConfig.setKindType(groupType, targetType);
                }
            } else if (groupType == MontageConfig.MontageGroupType.CLOTH) {
                // 同步衣服的clothColor到json
                suitConfig.setChangeCloth(!material.isAiEntity());
            } else if (groupType == MontageConfig.MontageGroupType.MOUSTACHE) {
                suitConfig.setHasMustache(material.isSelected());
            } else if (groupType == MontageConfig.MontageGroupType.FRECKLE) {
                suitConfig.setHasFreckle(material.isSelected());
            } else if (groupType == MontageConfig.MontageGroupType.MOLE) {
                suitConfig.setHasMole(material.isSelected());
            } else if (groupType == MontageConfig.MontageGroupType.FOREGROUND) {
                suitConfig.setHasFg(material.isSelected());
            }
        }
        builder.append(";");
        // path
        builder.append(targetPath);
        builder.append(File.separator);
        eventQueue.add(builder.toString());
        return eventQueue;
    }

    /**
     * 用户选中的肤色
     * 肤色联动逻辑。需要同步 腮红、嘴巴、眼睛部位
     */
    public List<String> onUserSelectSkin(@MontageConfig.MontageSkinType int type) {
        MontageSuitConfig suitConfig = MontageSuitConfig.getMontageSuitConfig();
        suitConfig.setChangeSkin(true);
        eventQueue.clear();
        boolean isAiCanClick;
        String raceSign = MontageConfig.getRaceSign(type);
        // 跟默认的做对比。
        // 切换种族的话，腮红、嘴巴、眼睛分类下的Ai icon 都是不可选中的。
        isAiCanClick = raceSign.equals(suitConfig.getDefaultRace());

        // 变更肤色
        StringBuilder builder = new StringBuilder();
        builder.append("COLOR;FACE;");
        builder.append(type);
        builder.append(",");
        builder.append(type);
        builder.append(",");
        builder.append(type);
        eventQueue.add(builder.toString());

        // 换基准图
        builder.delete(0, builder.length());
        builder.append("RELOAD;FACESKIN;");
        builder.append(MontageConfig.getSkinBaseChartPath(type));
        builder.append(File.separator);
        eventQueue.add(builder.toString());

        // 注意这里是跟上次的做对比。
        // 种族没有变化的素材不做变化
        boolean isCanBreak = (raceSign.equals(MontageConfig.getRaceSign(suitConfig.getSkinLevel())));

        // 临时缓存用户选中的SkinType
        suitConfig.setSkinLevel(type);
        // 缓存对应的基准图路径
        suitConfig.setAccessoriesMaterialLocalPath(MontageConfig.MontageMaterialType.SKIN,
            MontageConfig.getSkinBaseChartPath(type));
        // 把肤色记录到配饰分类下，方便打点使用。
        // suitConfig.recordAccessoriesType(MontageConfig.MontageMaterialType.SKIN, String.valueOf(type));

        if (isCanBreak) {
            return eventQueue;
        }

        // 腮红、 眼睛 、嘴巴联动
        List<MontageMaterialEntity> mouthEntities =
            getMaterialByCategoryId(getCategoryIdByGroupType(MontageConfig.MontageGroupType.MOUTH), false);
        String mouthMaterialId = getTargetMaterialId(MontageConfig.MontageGroupType.MOUTH);
        updateEntityStateAndCreateEvent(mouthEntities, mouthMaterialId, isAiCanClick);
        // Debug.d("yyp","-----嘴巴----"+mouthMaterialId);
        // 男生没有腮红 特殊处理
        if (suitConfig.getGender() == MontageConfig.MontageGender.GIRL) {
            List<MontageMaterialEntity> blushEntities =
                getMaterialByCategoryId(getCategoryIdByGroupType(MontageConfig.MontageGroupType.BLUSH), false);
            String blushMaterialId = getTargetMaterialId(MontageConfig.MontageGroupType.BLUSH);
            updateEntityStateAndCreateEvent(blushEntities, blushMaterialId, isAiCanClick);
        } else {
            // 男生的腮红不会显示在列表，需要从数据库过滤。
            String blushMaterialId = getTargetMaterialId(MontageConfig.MontageGroupType.BLUSH);
            // Debug.d("yyp","man shy id--->"+blushMaterialId);
            blushMaterialId = getManShy(blushMaterialId);
            String path = MontageConfig.getMaterialLocalPath() + blushMaterialId;
            String event = "RELOAD;SHYS;" + path + File.separator;
            // 记录素材路径，用于拷贝文件
            MontageSuitConfig.getMontageSuitConfig().setOtherMaterialLocalPath(MontageConfig.MontageGroupType.BLUSH,
                path);
            eventQueue.add(event);
        }

        List<MontageMaterialEntity> eyeEntities =
            getMaterialByCategoryId(getCategoryIdByGroupType(MontageConfig.MontageGroupType.EYE), false);
        String eyeMaterialId = getTargetMaterialId(MontageConfig.MontageGroupType.EYE);
        updateEntityStateAndCreateEvent(eyeEntities, eyeMaterialId, isAiCanClick);
        return eventQueue;
    }

    private String getTargetMaterialId(int type) {
        StringBuilder builder = new StringBuilder();
        MontageSuitConfig suitConfig = MontageSuitConfig.getMontageSuitConfig();
        builder.append("0");
        builder.append(MontageConfig.convertGroupTypeToLocal(type));
        builder.append(suitConfig.getRaceSign());
        // 白色--->棕色人
        // 棕色人 --->白人 (是包含于上面的不用单独处理)
        switch (type) {
            case MontageConfig.MontageGroupType.EYE:
                // 眼睛的类型
                int eyeType = suitConfig.getKindByType(MontageConfig.MontageGroupType.EYE);
                builder.append(eyeType);
                // 0 是预留 ，棕色人 只有1（普通双眼皮）
                builder.append("01");
                // 设置到json中
                suitConfig.setKindType(MontageConfig.MontageGroupType.EYE, eyeType);
                break;

            case MontageConfig.MontageGroupType.BLUSH:
                // 忽略这个位
                builder.append("0");
                // 拼接腮红种类
                int blushType = suitConfig.getKindByType(MontageConfig.MontageGroupType.BLUSH);
                if (blushType < 10) {
                    builder.append("0");
                    builder.append(blushType);
                } else {
                    builder.append(blushType);
                }
                // 设置到json中
                suitConfig.setKindType(MontageConfig.MontageGroupType.BLUSH, blushType);
                break;
            case MontageConfig.MontageGroupType.MOUTH:
                // 棕色人 只有普通色
                builder.append("0");
                // 嘴巴的类型
                int mouthType = suitConfig.getKindByType(MontageConfig.MontageGroupType.MOUTH);
                if (mouthType < 10) {
                    builder.append("0");
                    builder.append(mouthType);
                } else {
                    builder.append(mouthType);
                }
                // 设置到json中
                suitConfig.setKindType(MontageConfig.MontageGroupType.MOUTH, mouthType);
                break;
        }
        return builder.toString();
    }

    private void updateEntityStateAndCreateEvent(List<MontageMaterialEntity> sourceData, String targetMaterialId,
        boolean isAiCanClick) {
        for (MontageMaterialEntity entity : sourceData) {
            if (entity.isAiEntity()) {
                // 切换种族 ai 按钮不可点击
                entity.setCanClick(isAiCanClick);
                entity.setSelected(false);
            } else if (entity.isCommonMaterialEntity() && entity.getMaterialId().startsWith(targetMaterialId)) {
                // 选中这个实体
                entity.setSelected(true);
                StringBuilder builder = new StringBuilder();
                builder.append("RELOAD;");
                builder
                    .append(MontageConfig.getActualNameByGroupType(getGroupTypeByCategoryId(entity.getCategoryId())));
                builder.append(";");
                builder.append(entity.getSavedLocalPath());
                // 记录素材路径，用于拷贝文件
                MontageSuitConfig.getMontageSuitConfig().setOtherMaterialLocalPath(
                    getGroupTypeByCategoryId(entity.getCategoryId()), entity.getSavedLocalPath());

                // 记录对应的素材Id.打点使用
                MontageSuitConfig.getMontageSuitConfig().recordCommonType(entity.getCategoryId(),
                    entity.getMaterialId());

                builder.append(File.separator);
                eventQueue.add(builder.toString());
                break;
            }
        }
    }

    /**
     * 根据类别Id 获取的该类别下所有素材。
     */

    public List<MontageMaterialEntity> getMaterialByCategoryId(String categoryId, boolean isPriorityCache) {
        List<MontageMaterialEntity> result = null;
        MontageSuitConfig suitConfig = MontageSuitConfig.getMontageSuitConfig();
        if (isPriorityCache) {
            // 优先从缓存的Map中去数据
            result = materialMap.get(categoryId);
        }
        // 没有数据再去数据库查询
        if (result == null || result.size() == 0) {
            int groupType = getGroupTypeByCategoryId(categoryId);
            // 如果是腮红、眼睛、嘴巴的话需要按照肤色过滤查询
            if (groupType == MontageConfig.MontageGroupType.MOUTH || groupType == MontageConfig.MontageGroupType.EYE
                || groupType == MontageConfig.MontageGroupType.BLUSH) {
                result = montageRepository.getMontageMaterialWithSkinColor(categoryId, suitConfig.getRaceSign(),
                    suitConfig.getGender());
            } else {
                result = montageRepository.getMontageMaterial(categoryId, suitConfig.getGender());
            }

            if (result != null && result.size() > 0) {
                for (MontageGroupEntity groupEntity : montageGroupList) {
                    // 加入Ai Icon
                    if (groupEntity.getCategoryId().equals(categoryId)) {
                        // 分类一样，取分类对应的图标
                        MontageMaterialEntity tempAiEntity = new MontageMaterialEntity();
                        tempAiEntity.setMaterialEntityType(MontageMaterialEntity.EntityType.AI_TYPE);
                        tempAiEntity.setCategoryId(categoryId);

                        if (groupEntity.getGroupType() == MontageConfig.MontageGroupType.ACCESSORIES) {
                            // 根据有没有眼镜，判断item 能不能点击
                            boolean isHasGlasses = suitConfig.isHasGlasses();
                            tempAiEntity.setSelected(isHasGlasses);
                            tempAiEntity.setCanClick(isHasGlasses);
                            // 配饰类别Ai按钮只能和眼镜的互斥。
                            tempAiEntity.setMaterialType(MontageConfig.MontageMaterialType.GLASS);
                            tempAiEntity.setDefaultMaterialPath(
                                suitConfig.getDefaultLocalPath(MontageConfig.MontageGroupType.ACCESSORIES));
                            // 配饰分类下如果帽子之类的有选中。也要选中
                            for (MontageMaterialEntity montageMaterialEntity : result) {
                                if (montageMaterialEntity
                                    .getMaterialType() == MontageConfig.MontageMaterialType.GLASS) {
                                    continue;
                                }
                                if (suitConfig.isAccessoriesLastUsed(montageMaterialEntity.getMaterialType(),
                                    montageMaterialEntity.getMaterialId())) {
                                    montageMaterialEntity.setSelected(true);
                                } else {
                                    montageMaterialEntity.setSelected(false);
                                }
                            }
                        } else if (groupEntity.getGroupType() == MontageConfig.MontageGroupType.MOLE) {
                            tempAiEntity.setMaterialType(result.get(0).getMaterialType());
                            tempAiEntity.setSelected(suitConfig.isHasMole());
                            tempAiEntity.setCanClick(suitConfig.isHasMole());
                            tempAiEntity.setDefaultMaterialPath(suitConfig.getDefaultLocalPath(groupType));

                        } else if (groupEntity.getGroupType() == MontageConfig.MontageGroupType.FRECKLE) {
                            tempAiEntity.setMaterialType(result.get(0).getMaterialType());
                            tempAiEntity.setSelected(suitConfig.isHasFreckle());
                            tempAiEntity.setCanClick(suitConfig.isHasFreckle());
                            tempAiEntity.setDefaultMaterialPath(suitConfig.getDefaultLocalPath(groupType));
                        } else if (groupEntity.getGroupType() == MontageConfig.MontageGroupType.MOUSTACHE) {
                            tempAiEntity.setMaterialType(result.get(0).getMaterialType());
                            tempAiEntity.setSelected(suitConfig.isHasMustache());
                            tempAiEntity.setCanClick(suitConfig.isHasMustache());
                            tempAiEntity.setDefaultMaterialPath(suitConfig.getDefaultLocalPath(groupType));
                        } else if (groupEntity.getGroupType() == MontageConfig.MontageGroupType.FOREGROUND) {
                            tempAiEntity.setMaterialType(result.get(0).getMaterialType());
                            tempAiEntity.setSelected(suitConfig.isHasFg());
                            tempAiEntity.setCanClick(suitConfig.isHasFg());
                            tempAiEntity.setDefaultMaterialPath(suitConfig.getDefaultLocalPath(groupType));
                        } else {
                            // 不是配饰类的话，AI_TYPE 类型 的materialType 和普通素材一样（因为是互斥）
                            // 除了配饰类下 素材的 materialType 会出现不同。其他都一样。这边直接拿第0 个的就好
                            tempAiEntity.setMaterialType(result.get(0).getMaterialType());
                            tempAiEntity.setSelected(true);
                            tempAiEntity.setDefaultMaterialPath(suitConfig.getDefaultLocalPath(groupType));
                        }
                        tempAiEntity.setIconUrl(groupEntity.getIconUrl());
                        result.add(0, tempAiEntity);
                        // 加入调整实体
                        if (MontageConfig.isFaceOrganType(groupType)) {
                            MontageMaterialEntity tempAdjustEntity = new MontageMaterialEntity();
                            tempAdjustEntity.setMaterialEntityType(MontageMaterialEntity.EntityType.ADJUST_TYPE);
                            result.add(0, tempAdjustEntity);
                        }
                        // 脸部需要加入肤色
                        if (groupType == MontageConfig.MontageGroupType.FACE) {
                            for (int i = 5; i >= 0; i--) {
                                MontageMaterialEntity tempSkinEntity = new MontageMaterialEntity();
                                tempSkinEntity.setMaterialType(MontageConfig.MontageMaterialType.SKIN);
                                tempSkinEntity.setMaterialEntityType(MontageMaterialEntity.EntityType.SKIN_TYPE);
                                tempSkinEntity.setColorRes(getApplication().getResources().getColor(colors[i]));
                                tempSkinEntity.setSkinType(MontageConfig.getSkinTypeArray()[i]);
                                tempSkinEntity.setCategoryId(categoryId);
                                if (suitConfig.getSkinTypeOfUserSelected() == MontageConfig.getSkinTypeArray()[i]) {
                                    // 肤色选中
                                    tempSkinEntity.setSelected(true);
                                } else {
                                    tempSkinEntity.setSelected(false);
                                }
                                result.add(0, tempSkinEntity);
                            }
                        } else if (groupType == MontageConfig.MontageGroupType.BACKGROUND) {
                            // 背景加入自定义实体
                            MontageMaterialEntity tempCustomEntity = new MontageMaterialEntity();
                            tempCustomEntity.setCategoryId(categoryId);
                            tempCustomEntity.setMaterialType(result.get(0).getMaterialType());
                            tempCustomEntity.setMaterialEntityType(MontageMaterialEntity.EntityType.CUSTOM_TYPE);
                            tempCustomEntity.setMaterialId(MontageConfig.USER_CUSTOMER_BG_MATERIAL_ID);
                            result.add(0, tempCustomEntity);
                        }

                        break;
                    }
                }
                materialMap.put(categoryId, result);
            }
        }
        return result;
    }

    private String getCategoryIdByGroupType(int type) {
        for (MontageGroupEntity groupEntity : montageGroupList) {
            if (type == groupEntity.getGroupType()) {
                return groupEntity.getCategoryId();
            }
        }

        return null;
    }

    /**
     * @return
     */

    public Integer getGroupPositionByCategoryId(String categoryId) {
        if (groupPositionMap.get(categoryId) != null) {
            return groupPositionMap.get(categoryId);
        } else {
            for (int i = 0; i < montageGroupList.size(); i++) {
                groupPositionMap.put(montageGroupList.get(i).getCategoryId(), i);
                if (categoryId.equals(montageGroupList.get(i).getCategoryId())) {
                    return i;
                }
            }
            return -1;
        }
    }

    public Integer getGroupTypeByCategoryId(String categoryId) {
        if (groupTypeMap.get(categoryId) != null) {
            return groupTypeMap.get(categoryId);
        } else {
            for (int i = 0; i < montageGroupList.size(); i++) {
                // 缓存起来
                groupTypeMap.put(montageGroupList.get(i).getCategoryId(), montageGroupList.get(i).getGroupType());
                if (montageGroupList.get(i).getCategoryId().equals(categoryId)) {
                    return montageGroupList.get(i).getGroupType();
                }
            }
            return -1;
        }
    }

    public void initData() {
        manShyIds.clear();
        materialMap.clear();
        groupTypeMap.clear();
        montageGroupList.clear();
        clearCacheRewardedVideo();
        montageGroupList
            .addAll(montageRepository.getMontageGroupsWithGender(MontageSuitConfig.getMontageSuitConfig().getGender()));
        // checkIsNewMaterialInEveryGroup(montageGroupList);
        materialGroups.postValue(montageGroupList);

    }


    /**
     * 更新组别下的小红点状态
     */
    public void updateGroupRedDotState(int position) {
        if (montageGroupList == null || montageGroupList.isEmpty() || position < 0
            || position >= montageGroupList.size()) {
            return;
        }
        MontageSuitConfig suitConfig = MontageSuitConfig.getMontageSuitConfig();
        boolean needFix = false;
        if (suitConfig.getGender() == MontageConfig.MontageGender.GIRL) {
            if (montageGroupList.get(position).getGroupIsNewForGirl() == MontageConfig.State.NEW) {
                needFix = true;
                montageGroupList.get(position).setGroupIsNewForGirl(MontageConfig.State.OLD);
            }
        } else {
            if (montageGroupList.get(position).getGroupIsNewForMan() == MontageConfig.State.NEW) {
                needFix = true;
                montageGroupList.get(position).setGroupIsNewForMan(MontageConfig.State.OLD);
            }
        }
        if (needFix) {
            // 修改数据库
            ThreadExecutor.executeSlowTask(new AbstractNamedRunnable("UPDATE-GROUP-RED-DOT-STATE") {
                @Override
                public void execute() {
                    montageRepository.updateGroupIsNewStateByCategoryId(montageGroupList.get(position).getCategoryId(),
                        MontageConfig.State.OLD, suitConfig.getGender());
                }
            });
        }
    }

    /**
     * 根据计算的Id.从数据库中匹配
     */

    public String getManShy(String likeId) {
        if (manShyIds.size() == 0) {
            manShyIds.addAll(montageRepository.getMaterialIdsByType("05", MontageConfig.MontageGender.MAN));
        }
        for (String entity : manShyIds) {
            if (entity.startsWith(likeId)) {
                return entity;
            }
        }
        return null;
    }

    /**
     * 根据Id 获取的腮红、嘴巴、眼睛的类型
     */
    private int getTypeFromMaterialId(@MontageConfig.MontageGroupType int groupType, String sourceId) {

        int targetType;
        switch (groupType) {
            case MontageConfig.MontageGroupType.EYE:
                targetType = Integer.parseInt(sourceId.charAt(3) + "");
                break;
            default:
                targetType = Integer.parseInt(sourceId.charAt(4) + "");
                if (targetType > 0) {
                    targetType = Integer.parseInt(sourceId.substring(4, 6));
                } else {
                    targetType = Integer.parseInt(sourceId.charAt(5) + "");
                }
                break;
        }
        return targetType;
    }

    /**
     *  下载素材
     */
    void doDownload(MontageMaterialEntity entity) {
        if (!NetUtils.canNetworking(AppContext.getContext())
            || mDownloadManager.isInQueue(entity.getMaterialFileUrl())
            || mDownloadManager.isDownloading(entity.getMaterialFileUrl())) {
            return;
        }
        MTAnalyticsAgent.logEvent(MTAnalyticsConstant.AR_REQUEST_DOWNLOAD, MTAnalyticsConstant.AR_ID,
            "M" + entity.getMaterialId());
        // 开始下载。
        new MaterialRequest.Executor()
                .addTask(new CommonDownloadTask(entity.getMaterialFileUrl(),MontageConfig.getMaterialLocalPath() + entity.getFileName(true),true,entity.getTargetSavePath()),mDownloadManager,null)
                .execute(new OnDownloadListener() {
                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onProgressChange(int progress) {
                        entity.setDownloadProgress(progress);
                        downloadProgressEvent.postValue(entity);
                    }

                    @Override
                    public void onError(Throwable e) {
                        entity.setDownloadProgress(-1);
                        entity.setUnziped(false);
                        downloadProgressEvent.postValue(entity);
                    }

                    @Override
                    public void onSuccess() {
                        entity.setDownloadProgress(100);
                        entity.setUnziped(true);
                        downloadProgressEvent.postValue(entity);
                        MTAnalyticsAgent.logEvent(MTAnalyticsConstant.ARDOWNLOAD, MTAnalyticsConstant.AR_ID,
                                "M" + entity.getMaterialId());
                    }
                });
    }

    int getSelectedGroupType() {
        return selectedType;
    }

    void setSelectedGroupType(int selectedType) {
        this.selectedType = selectedType;
    }

    public void logEvent(MontageMaterialEntity materialEntity) {

        if (materialEntity.isAdjustEntity() || !materialEntity.isSelected()) {
            return;
        }

        String categoryId = materialEntity.getCategoryId();
        String materialId;
        if (materialEntity.isSkinEntity()) {
            materialId = String.valueOf(materialEntity.getSkinType());
        } else if (materialEntity.isAiEntity()) {
            materialId = MontageSuitConfig.LOGEVENT_AI_ID;
        } else if (materialEntity.isCommonMaterialEntity()) {
            materialId = materialEntity.getMaterialId();
        } else {
            return;
        }

        HashMap<String, String> logMap = new HashMap<>(4);
        logMap.put("类别", categoryId);
        logMap.put("素材", materialId);
        logMap.put("性别", MontageSuitConfig.getMontageSuitConfig().getGenderForLog());
        MTAnalyticsAgent.logEvent(MTAnalyticsConstant.EVENT_MONTAGE_ADJUST_CLK, logMap);
    }


    /**
     * 清楚激励视屏资格
     */
    private void clearCacheRewardedVideo() {
        ThreadExecutor.executeSlowTask(new AbstractNamedRunnable("") {
            @Override
            public void execute() {
                if (needPaidMaterials.size() == 0) {
                    needPaidMaterials.addAll(
                        montageRepository.getNeedPaidGoodId(MontageSuitConfig.getMontageSuitConfig().getGender()));
                }
                for (String goodId : needPaidMaterials) {
                    SelfieConfig.setIsRewardedVideoHasWatched(goodId, false);
                }
            }
        });
    }

}
