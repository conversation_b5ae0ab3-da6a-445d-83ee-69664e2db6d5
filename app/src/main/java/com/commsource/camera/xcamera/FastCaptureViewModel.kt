package com.commsource.camera.xcamera

import android.app.Application
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.commsource.beautyplus.base.BaseVm
import com.commsource.camera.fastcapture.FastCaptureController
import com.commsource.camera.fastcapture.SelfiePhotoData
import com.commsource.camera.fastcapture.event.ReadInfoEvent
import com.commsource.config.SelfieConfig

/**
 *
 * Created on 2020/5/6
 * <AUTHOR>
 */
class FastCaptureViewModel(application: Application) : BaseVm(application) {

    /**
     * 快速拍照开关状态
     */
    var isOpen: Boolean

    /**
     * 是否已校验拍照订阅
     */
    var isCheckSubscribe = false

    /**
     * 快速拍照的数据处理 动态清理
     */
    var fastCaptureSelfiePhotoData: SelfiePhotoData? = null

    /**
     * 快速拍照保存结果
     */
    val saveResultEvent = FastCaptureController.getInstance().saveResultEvent

    /**
     * 快速拍照数据保存处理状态
     */
    val readInfoEvent = MediatorLiveData<ReadInfoEvent>().apply {
        addSource(FastCaptureController.getInstance().readInfoEvent, Observer {
            value = it
        })
    }

    val flashViewEvent = MutableLiveData<Boolean>()

    private val fastCaptureController = FastCaptureController.getInstance()

    init {
        isOpen = SelfieConfig.isFastCapture(application)
    }

    /**
     * 快速保存拍照数据
     */
    fun fastCapture(selfiePhotoData: SelfiePhotoData, callback: ((Boolean) -> Unit)? = null) {
        fastCaptureController.fastCapture(selfiePhotoData, callback)
    }

    /**
     * 是否有快速拍照保存图片在处理中
     */
    fun hasFastSavingTasks(): Boolean {
        return isOpen && fastCaptureController.hasFastCaptureTasks()
    }

    /**
     * 获取当前的快拍处理任务量是否在限制范围内@see MAX_FAST_CAPTURE_TASKS
     *
     * @return true 在限制范围内
     */
    fun isAvailableFastCaptureTasks(): Boolean {
        return fastCaptureController.isAvailableFastCaptureTasks()
    }
}