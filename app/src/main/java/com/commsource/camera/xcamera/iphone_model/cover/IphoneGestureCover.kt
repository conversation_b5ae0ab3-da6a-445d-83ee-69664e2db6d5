package com.commsource.camera.xcamera.iphone_model.cover

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.graphics.Rect
import android.os.CountDownTimer
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.FrameLayout
import androidx.lifecycle.lifecycleScope
import com.commsource.beautymain.widget.gesturewidget.GestureDetectorPro
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.CoverIphoneGestureBinding
import com.commsource.camera.common.CameraContainerViewModel
import com.commsource.camera.xcamera.iphone_model.IphoneFunction
import com.commsource.camera.xcamera.iphone_model.IphoneModelCache
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneCameraNewViewModel
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneCaptureViewModel
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneFunctionViewModel
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneSettingViewModel
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneStyleViewModel
import com.commsource.camera.xcamera.iphone_model.widget.IphoneFocusView
import com.commsource.camera.xcamera.iphone_model.widget.PixViewPager
import com.commsource.camera.xcamera.iphone_model.widget.TransparentView
import com.commsource.util.LOGV_Camera
import com.commsource.util.ViewUtils
import com.commsource.util.coroutine.launchMain
import com.commsource.util.dpf
import com.commsource.util.gone
import com.commsource.util.hapticVirtualKey
import com.commsource.util.invisible
import com.commsource.util.isRtl
import com.commsource.util.isVisible
import com.commsource.util.setMarginTop
import com.commsource.util.visible
import com.commsource.widget.ExposureSeekBar
import com.pixocial.androidx.core.extension.dp
import com.pixocial.framework.cover.AbstractViewBindingCover
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

class IphoneGestureCover : AbstractViewBindingCover<CoverIphoneGestureBinding>() {

    private val containerViewModel by lazy { activityViewModel(CameraContainerViewModel::class) }
    private val iphoneCameraNewViewModel by lazy { fragmentViewModel(IphoneCameraNewViewModel::class) }
    private val iphoneSettingViewModel by lazy { fragmentViewModel(IphoneSettingViewModel::class) }
    private val iphoneFunctionViewModel by lazy { fragmentViewModel(IphoneFunctionViewModel::class) }
    private val iphoneStyleViewModel by lazy { fragmentViewModel(IphoneStyleViewModel::class) }
    private val iphoneCaptureViewModel by lazy { fragmentViewModel(IphoneCaptureViewModel::class) }

    private var rotation: Float = 0f

    private val stylePresets by lazy {
        iphoneStyleViewModel.initStylePreset()
    }

    private var isInitViewBox = true

    /**
     * 手势回调
     */
    private val gestureDetector by lazy {
        GestureDetectorPro(
            coverContainer.attachActivity,
            object : GestureDetectorPro.SimpleOnGestureListener() {

                override fun onMajorFingerDown(downEvent: MotionEvent?): Boolean {
                    return false
                }

                override fun onMajorScroll(
                    downEvent: MotionEvent?,
                    moveEvent: MotionEvent?,
                    distanceX: Float,
                    distanceY: Float
                ): Boolean {
                    if (iphoneCameraNewViewModel.isExposeSupported()) {
                        val offset = iphoneCameraNewViewModel.getOrientationAwareOffset(distanceX, -distanceY)

                        viewBinding.esb.takeIf { it.isVisible }?.scrollBy(offset)
                    }
                    return false
                }

                override fun onFlingFromLeftToRight(
                    downEvent: MotionEvent?,
                    upEvent: MotionEvent?,
                    velocityX: Float,
                    velocityY: Float
                ): Boolean {
                    return false
                }

                override fun onSingleTap(downEvent: MotionEvent?, upEvent: MotionEvent?) {
                    if (iphoneFunctionViewModel.hasBottomFunction()) {
                        iphoneFunctionViewModel.show(null)
                        return
                    }

                    if (!IphoneModelCache.getIphoneModelCacheEntity().touchPic) {
                        viewBinding.mFocusView.setShowFocusView()
                    }
                }

                override fun onMajorFingerUp(upEvent: MotionEvent?): Boolean {
                    return false
                }

                override fun onFlingFromRightToLeft(
                    downEvent: MotionEvent?,
                    upEvent: MotionEvent?,
                    velocityX: Float,
                    velocityY: Float
                ): Boolean {
                    return false
                }
            }
        )
    }

    override fun getLayoutId() = R.layout.cover_iphone_gesture

    override fun getBackPressWeight() = -1

    override fun getPhysicKeyEventWeight() = -1

    @SuppressLint("ClickableViewAccessibility")
    override fun onViewDidLoad() {
        if (IphoneModelCache.getIphoneModelCacheEntity().showGrid) {
            viewBinding.clLine.visible()
        } else {
            viewBinding.clLine.gone()
        }

        //曝光滑杆 处理
        viewBinding.esb.onProgressChangeListener =
            object : ExposureSeekBar.OnProgressChangeListener {
                var lastValue = 0

                override fun onProgressChange(progress: Int, fromUser: Boolean) {
                    if (iphoneCameraNewViewModel.isExposeSupported()) {
                        val value = iphoneCameraNewViewModel.calculateExposure(progress)
                        if (lastValue != value) {
                            iphoneCameraNewViewModel.updateExposure(value)
                            lastValue = value
                            IphoneModelCache.getIphoneModelCacheEntity().lightPercent = progress
                        }
                        viewBinding.esb.switchIphoneModel()
                        viewBinding.mFocusView.alpha100()
                        startCountDownTimer()
                    }
                }

                override fun onLockDefault(progress: Int) {
                    viewBinding.esb.hapticVirtualKey()
                }
            }

        //对焦成功处理 显示曝光
        viewBinding.mFocusView.setFocusListener(object : IphoneFocusView.OnFocusListener {

            override fun onAutoFocusStart(transX: Float, transY: Float, showRight: Boolean) {
                viewBinding.esb.visible()
                if (viewBinding.mFocusView.isShouldShowFocusView && !viewBinding.esb.isIphoneModel) {
                    viewBinding.esb.switchIphoneModel()
                }

                when {
                    rotation >= 270f -> {
                        viewBinding.esb.translationX = if (viewBinding.esb.isRtl()) {
                            transX + 36.dpf + 20.dpf
                        } else {
                            transX - (72.5f).dpf + 17.dpf
                        }
                        if (showRight) {
                            viewBinding.esb.translationY = transY + 20.dpf
                        } else {
                            viewBinding.esb.translationY = transY - (72 + 20).dpf
                        }
                    }

                    rotation >= 180 -> {
                        if (showRight) {
                            viewBinding.esb.translationX = if (viewBinding.esb.isRtl()) {
                                transX
                            } else {
                                transX
                            }
                        } else {
                            viewBinding.esb.translationX = if (viewBinding.esb.isRtl()) {
                                transX + (72 + 30 + 10).dpf
                            } else {
                                transX - 112.dpf
                            }
                        }
                        viewBinding.esb.translationY = transY - (36.5).dp
                    }

                    rotation >= 90f -> {
                        viewBinding.esb.translationX = if (viewBinding.esb.isRtl()) {
                            transX + 36.dpf + 20.dpf
                        } else {
                            transX - (72.5f).dpf + 17.dpf
                        }
                        if (showRight) {
                            viewBinding.esb.translationY = transY - (75 + 20).dpf
                        } else {
                            viewBinding.esb.translationY = transY + 20.dpf
                        }
                    }

                    else -> {
                        if (showRight) {
                            viewBinding.esb.translationX = if (viewBinding.esb.isRtl()) {
                                transX
                            } else {
                                transX
                            }
                        } else {
                            viewBinding.esb.translationX = if (viewBinding.esb.isRtl()) {
                                transX + (72 + 30 + 10).dpf
                            } else {
                                transX - 112.dpf
                            }
                        }
                        viewBinding.esb.translationY = transY - (36.5).dp
                    }
                }

                startCountDownTimer()
            }

            override fun onFocusTouch() {
                viewBinding.esb.invisible()
            }

            override fun onFocusResult(isFocus: Boolean) {

            }
        })

        //处理手势
        viewBinding.flGesture.setOnTouchListener { v, event ->
            if (iphoneCameraNewViewModel.showCameraBoxCoverEvent.value == true) {
                return@setOnTouchListener false
            }

            // 不再有效点击区域的拦截掉。
            iphoneFunctionViewModel.bottomSizeParamEvent.value?.let {
                var bottomCoverY = it.screenHeight
                when {
                    iphoneSettingViewModel.is11CameraRatio() -> {
                        bottomCoverY = it.screenHeight - it.mBottomCoverHeightOn11
                    }

                    iphoneSettingViewModel.is43CameraRatio() -> {
                        bottomCoverY = it.screenHeight - it.mBottomCoverHeightOn43
                    }
                }
                if (event.getY(0) > bottomCoverY) {
                    return@setOnTouchListener true
                }
            }
            //手势拦截
            gestureDetector.onTouchEvent(event)
            if (iphoneFunctionViewModel.hasBottomFunction()) {
                return@setOnTouchListener true
            }
            if (coverContainer.onDispatchScreenGestureEvent(event)) {
                return@setOnTouchListener true
            }

            if (iphoneCaptureViewModel.isBurstTaking() && iphoneCaptureViewModel.isBurstLongPress()) {
                return@setOnTouchListener true
            }

            // 触摸事件交给相机区域处理
            val fragment =
                coverContainer.attachActivity.supportFragmentManager.findFragmentByTag("CameraPreviewFragment")
            val view = (fragment?.view as? ViewGroup)?.findViewById<View>(R.id.cameraContainer)
            return@setOnTouchListener view?.onTouchEvent(event) ?: true
        }

        initLoopViewBox()
    }

    override fun onBindViewModel() {
        iphoneCameraNewViewModel.deviceOrientationFlow.onEach { r ->
            rotation = (r + 360) % 360
            viewBinding.esb.invisible()
            viewBinding.mFocusView.invisible()
            viewBinding.esb.rotation = rotation
            viewBinding.mFocusView.onViewRotationChange(rotation, 0f)
        }.launchIn(lifecycleOwner.lifecycleScope)

        iphoneCameraNewViewModel.previewCoverLiveData.observe(lifecycleOwner) {
            containerViewModel.previewCoverEnabledEvent.value = it
        }
        // 监听预览比例变化
        iphoneCameraNewViewModel.cameraRatioLiveData.observe(lifecycleOwner) {
            viewBinding.mFocusView.gone()
            viewBinding.esb.invisible()
        }

        iphoneSettingViewModel.showGridLineEvent.observe(lifecycleOwner) {
            "显示网格线 $it".LOGV_Camera()
            if (it && iphoneFunctionViewModel.lastFunction != IphoneFunction.STYLE) {
                viewBinding.clLine.visible()
            } else {
                viewBinding.clLine.gone()
            }

            pages.forEach { view ->
                view.showLine = it
                view.invalidate()
            }
        }

        iphoneSettingViewModel.touchPicEvent.observe(lifecycleOwner) {
            if (it) {
                viewBinding.mFocusView.gone()
                viewBinding.esb.invisible()
            }
        }

        iphoneSettingViewModel.screenRatioChangeEvent.observe(lifecycleOwner) {
            refreshCameraBoxSizAnimator(true)
        }

        iphoneCameraNewViewModel.showCameraBoxCoverEvent.observe(lifecycleOwner) {
            if (it) {
                showOrHideCameraBoxCover(true)
            } else {
                showOrHideCameraBoxCover(false)
            }
        }

        iphoneFunctionViewModel.bottomFunctionChangeEvent.observe(lifecycleOwner) {
            if (it == IphoneFunction.STYLE) {
                iphoneCameraNewViewModel.showCameraBoxCoverEvent.value = true
            } else {
                iphoneCameraNewViewModel.showCameraBoxCoverEvent.value = false
            }
        }

        iphoneCameraNewViewModel.cameraAccessor.run {
            launchMain {
                fetchConfigNotifier()?.autoFocusStartEvent?.collect {
                    it.let {
                        viewBinding.mFocusView.onAutoFocusStart(correctRect(it.first), it.second)
                    }
                }
            }

            launchMain {
                fetchConfigNotifier()?.autoFocusSuccessEvent?.collect {
                    it.let {
                        viewBinding.mFocusView.onAutoFocusSuccess(correctRect(it))
                    }
                }
            }

            launchMain {
                fetchConfigNotifier()?.autoFocusFailedEvent?.collect {
                    it.let {
                        viewBinding.mFocusView.onAutoFocusFailed(correctRect(it))
                    }
                }
            }
        }

        iphoneSettingViewModel.previewSizeLiveData.observe(lifecycleOwner) { viewport ->
            viewBinding.flGesture.setMarginTop(viewport.top)

            viewBinding.mFocusView.onCoverSizeChange(viewport)
            viewBinding.esb.onCoverSizeChange(viewport)

            viewBinding.clLine.setMarginTop(viewport.top)
            //图片设置保持和拍照取景窗口一致
            ViewUtils.setHeight(viewBinding.clLine, viewport.height())
        }

        iphoneCameraNewViewModel.firstFrameExposureLiveData?.observe(lifecycleOwner) { exposure ->
            val exp = exposure ?: return@observe
            val esb = viewBinding.esb

            // 相机模式切换时，这个回调会走两次，第一次回调时机过早，做下判断过滤
            if (esb.width == 0 || esb.height == 0) {
                return@observe
            }
            // 如果相机首帧有效后读到的曝光值为0，重置为初始值
            if (exp == 0) {
                viewBinding.esb.setProgressWithoutNotify(50)
            }
        }
    }

    private var countDownTimer: CountDownTimer? = null
    private fun startCountDownTimer() {
        //倒计时触发
        countDownTimer?.cancel()
        countDownTimer = null
        countDownTimer = object : CountDownTimer(2000, 1000) {

            override fun onTick(millisUntilFinished: Long) {

            }

            override fun onFinish() {
                "更新ui".LOGV_Camera()
                viewBinding.mFocusView.invisible()
                viewBinding.esb.invisible()
            }
        }
        countDownTimer?.start()
    }

    private fun refreshCameraBoxSizAnimator(withAnimation: Boolean) {
        iphoneSettingViewModel.screenRatioChangeEvent.value?.let { ratio ->
            var preParams = viewBinding.loopViewPager.layoutParams as FrameLayout.LayoutParams
            var cameraVisibleRect = Rect()
            iphoneSettingViewModel.calculateCameraVisibleSize(ratio, cameraVisibleRect)
            if (withAnimation) {
                val animator = ValueAnimator.ofFloat(0f, 1f)
                animator.duration = 300 // 动画持续时间，单位毫秒
                animator.interpolator = AccelerateDecelerateInterpolator()
                animator.addUpdateListener { animation ->
                    val fraction = animation.animatedValue as Float

                    val newWidth =
                        preParams.width + ((cameraVisibleRect.width() - preParams.width) * fraction).toInt()
                    val newHeight =
                        preParams.height + ((cameraVisibleRect.height() - preParams.height) * fraction).toInt()
                    val newTop =
                        preParams.topMargin + ((cameraVisibleRect.top - preParams.topMargin) * fraction).toInt()

                    val params = FrameLayout.LayoutParams(
                        newWidth, newHeight
                    )
                    params.topMargin = newTop
                    viewBinding.loopViewPager.layoutParams = params
                }
                animator.start()
            } else {
                val params = FrameLayout.LayoutParams(
                    cameraVisibleRect.width(), cameraVisibleRect.height()
                )
                params.topMargin = cameraVisibleRect.top
                viewBinding.loopViewPager.layoutParams = params
            }
        }
    }

    private fun showOrHideCameraBoxCover(show: Boolean) {
        if (show) {
            viewBinding.coverLayout.visible()
            viewBinding.coverLayout.alpha = 0f
            viewBinding.coverLayout.animate().cancel()
            viewBinding.coverLayout.animate().alpha(1f)
                .setDuration(300)
                .start()
        } else {
            viewBinding.coverLayout.animate().cancel()
            viewBinding.coverLayout.animate().alpha(0f)
                .setDuration(300)
                .withEndAction {
                    viewBinding.coverLayout.gone()
                }
                .start()
        }
    }

    /**
     * 返回的聚焦框是相机预览区域计算出来的，需要进行修正
     */
    private fun correctRect(rect: Rect): Rect {
        val top = iphoneSettingViewModel.previewSizeLiveData.value?.top ?: 0
        return Rect(rect.left, rect.top + top, rect.right, rect.bottom + top)
    }

    val pages = mutableListOf<TransparentView>()

    private fun initLoopViewBox() {
        pages.clear()
        stylePresets.forEach { _ ->
            val transparentView = createViewBox()

            transparentView.setOnClickListener {
                "transparentView click".LOGV_Camera()
                iphoneFunctionViewModel.show(null)
                transparentView.postDelayed({
                    iphoneSettingViewModel.showGridLineEvent.postValue(IphoneModelCache.getIphoneModelCacheEntity().showGrid)
                }, 100)
            }

            pages.add(transparentView)
            val transparentView2 = createViewBox()
            transparentView2.setOnClickListener {
                "transparentView click".LOGV_Camera()
                iphoneFunctionViewModel.show(null)

                transparentView.postDelayed({
                    iphoneSettingViewModel.showGridLineEvent.postValue(IphoneModelCache.getIphoneModelCacheEntity().showGrid)
                }, 100)
            }
            pages.add(transparentView2)

            transparentView.showLine = IphoneModelCache.getIphoneModelCacheEntity().showGrid
            transparentView2.showLine = IphoneModelCache.getIphoneModelCacheEntity().showGrid
        }

        viewBinding.loopViewPager.setData(pages, stylePresets.size)
        viewBinding.loopViewPager.setOnPageSelectedListener(object :
            PixViewPager.OnPageSelectedListener {
            override fun onPageSelected(position: Int) {
                if (!isInitViewBox) {
                    IphoneModelCache.getIphoneModelCacheEntity().syltPressetId =
                        stylePresets[position].id
                    iphoneStyleViewModel.changeStylePreset(stylePresets[position])
                    iphoneStyleViewModel.currentPosition.value = position
                }
            }

            override fun onReadyToScroll() {
                if (isInitViewBox) {
                    val presetID = IphoneModelCache.getIphoneModelCacheEntity().syltPressetId
                    var position = stylePresets.indexOfFirst { it.id == presetID }
                    if (position == -1) {
                        position = 0
                    }
                    viewBinding.loopViewPager.post {
                        viewBinding.loopViewPager.setCurrentItem(position, false)
                    }
                    isInitViewBox = false
                }
            }
        })
    }

    private fun createViewBox(): TransparentView {
        val transparentView = TransparentView(coverContainer.attachActivity)
        transparentView.layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        return transparentView
    }
}