package com.commsource.camera.xcamera.cover.confirmbottomfunction

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentMovieFlareBinding
import com.commsource.camera.xcamera.cover.CameraConfigViewModel
import com.commsource.camera.xcamera.cover.ProViewModel
import com.commsource.camera.xcamera.cover.bottomFunction.*
import com.commsource.camera.xcamera.cover.confirm.ConfirmViewModel
import com.commsource.camera.xcamera.cover.confirm.MovieMode
import com.commsource.camera.xcamera.cover.tips.TipsViewModel
import com.commsource.config.ApplicationConfig
import com.commsource.config.SelfieConfig
import com.commsource.config.SubscribeConfig
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.util.*
import com.commsource.util.delegate.process.SelfieSubscribeProcess
import com.commsource.widget.ProView
import com.commsource.widget.XSeekBar
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.meitu.common.AppContext
import com.meitu.library.util.device.DeviceUtils
import xcrash.XCrash

/**
 * @Desc : 电影模式下光斑界面
 * <AUTHOR> Bear - 2021/1/8
 */
class MovieFlareFragment : BaseBottomSubFragment() {

    val mAdapter: BaseRecyclerViewAdapter by lazy { BaseRecyclerViewAdapter(mActivity) }

    val confirmViewModel by lazy { ViewModelProvider(ownerActivity)[ConfirmViewModel::class.java] }

    val confirmBottomFunctionViewModel by lazy { ViewModelProvider(ownerActivity)[ConfirmBottomFunctionViewModel::class.java] }

    val tipsViewModel by lazy { ViewModelProvider(ownerActivity)[TipsViewModel::class.java] }

    val bottomViewModel by lazy { ViewModelProvider(ownerActivity)[BottomFunctionViewModel::class.java] }

    val cameraConfigViewModel by lazy { ViewModelProvider(ownerActivity)[CameraConfigViewModel::class.java] }

    val proViewModel by lazy { ViewModelProvider(ownerActivity)[ProViewModel::class.java] }

    val mViewBinding by lazy { FragmentMovieFlareBinding.inflate(LayoutInflater.from(mActivity), null, false) }

    val space = (DeviceUtils.getScreenWidth() - 60.dpf() * 4 - 40.dpf()) / 3f

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        initView()
        return mViewBinding.root
    }

    fun initView() {
        bottomViewModel.bottomSizeParamEvent.observe(viewLifecycleOwner, Observer {
            ViewUtils.setHeight(mViewBinding.clBlurMode, it.mBottomBarHeight)
        })

        mViewBinding.rvFlare.adapter = mAdapter
        mViewBinding.rvFlare.layoutManager = LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
        mAdapter.setOnEntityClickListener(Integer::class.java) { position, entity ->
            MTAnalyticsAgent.logEvent(MTAnalyticsConstant.MOVIE_LIGHT_CLICK, "light_id", entity.toString())
            confirmViewModel.processMovieWithMode(entity.toInt()).takeIf { it }?.let { tipsViewModel.showConfirmFloatingTips(ResourcesUtils.getString(getFlareName(entity.toInt())), isLeftToRight = position < mAdapter.currentPosition) }
            var isNeedPro = entity.toInt() == MovieMode.MODE_LOVE
            if (confirmViewModel.selfiePhotoData != null) {
                isNeedPro =
                    isNeedPro || SelfieSubscribeProcess.needSubscribe(confirmViewModel.selfiePhotoData!!)
            }
            proViewModel.checkScreenshotEvent.postValue(isNeedPro && !SubscribeConfig.isSubValid())
            if (entity.toInt() == MovieMode.MODE_LOVE) {
                proViewModel.showProTips(
                    arrayListOf(
                        AppContext.application.getString(R.string.join_to_unlock_all_exclusive_effects),
                        SubsUtil.getFreeDayString()
                    )
                )
                proViewModel.showPro(ProView.Mode.PROBANNER)
            }
            false
        }

        mViewBinding.rvFlare.addItemDecoration(object : RecyclerView.ItemDecoration() {

            override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
                super.getItemOffsets(outRect, view, parent, state)
                when (parent.getChildAdapterPosition(view)) {
                    0 -> {
                        outRect.setRTL(20.dp(), 0, space.toInt(), 0)
                    }

                    3 -> {
                        outRect.setRTL(0, 0, 20.dp(), 0)
                    }

                    else -> {
                        outRect.setRTL(0, 0, space.toInt(), 0)
                    }
                }
            }
        })
        mViewBinding.ifvClose.setOnClickListener {
            confirmBottomFunctionViewModel.show(null)
        }

        mAdapter.setSingleItemEntities(listOf(MovieMode.MODE_ROUND, MovieMode.MODE_TRIANGLE, MovieMode.MODE_HEXAGON, MovieMode.MODE_LOVE), MovieFlareViewHolder::class.java, true)
        mAdapter.currentSelectEntity = MovieMode.MODE_ROUND

        mViewBinding.rlBlurContainer.setOnClickListener {
            confirmBottomFunctionViewModel.show(null)
        }

        confirmViewModel.applyMovieEffectEvent.observe(viewLifecycleOwner, Observer {
            mAdapter.currentSelectEntity = it
        })

        //屏幕比例切换 动态修改
        cameraConfigViewModel.screenRatioChangeEvent.observe(viewLifecycleOwner, Observer {
            var isFullScreenStyle = cameraConfigViewModel.isFullScreen() || cameraConfigViewModel.is916CameraRatio()
            mAdapter.addTag(MovieFlareViewHolder.IS_FULL_SCREEN, isFullScreenStyle)
            mAdapter.notifyAllItemChange()
            if (isFullScreenStyle) {
                mViewBinding.ifvClose.setTextColor(ResourcesUtils.getColor(R.color.white))
            } else {
                mViewBinding.ifvClose.setTextColor(ResourcesUtils.getColor(R.color.Gray_A))
            }
            mViewBinding.clBlurMode.delegate.backgroundColor = if (isFullScreenStyle) ResourcesUtils.getColor(R.color.black70) else ResourcesUtils.getColor(R.color.white)
        })

        mViewBinding.xsb.addOnProgressChangeListener(object : XSeekBar.OnProgressChangeListener {
            override fun onPositionChange(progress: Int, leftDx: Float, fromUser: Boolean) {
            }

            override fun onStopTracking(progress: Int, leftDx: Float, fromUser: Boolean) {
                confirmViewModel.selfiePhotoData?.movieFlareEffect?.let {
                    MTAnalyticsAgent.logEvent(MTAnalyticsConstant.MOVECHECK_LIGHT_SLIDE, "light_id", it.flareMode.toString())
                }
                confirmViewModel.onChangeFlareAlpha(progress)
                SelfieConfig.setBlurDegree(context, progress)
            }

            override fun onProgressChange(progress: Int, leftDx: Float, fromUser: Boolean) {
            }

            override fun onStartTracking(progress: Int, leftDx: Float) {
            }

        })
        confirmViewModel.selfiePhotoData?.movieFlareEffect?.let {
            mViewBinding.xsb.setProgress(it.flareAlpha, false)
        }

        if (AppTools.isDebug() && ApplicationConfig.getCrashSwitch()) {
            XCrash.testJavaCrash(false)
        }
    }

    /**
     * 获取模式名称
     */
    private fun getFlareName(mode: Int): Int {
        when (mode) {
            MovieMode.MODE_ROUND -> return R.string.round
            MovieMode.MODE_TRIANGLE -> return R.string.triangle
            MovieMode.MODE_HEXAGON -> return R.string.hexagon
            MovieMode.MODE_LOVE -> return R.string.heart
        }
        return R.string.round
    }

    override fun animateIn(action: Function0<Unit>) {
        super.animateIn(action)
        mViewBinding.root.animate().setListener(null).cancel()
        mViewBinding.root.translationY = DeviceUtils.getScreenHeight() / 2f
        mViewBinding.root.animate().translationY(0f)
                .withLayer()
                .setInterpolator(BottomInInterpolator())
                .setDuration(BottomFunction.BOTTOM_DURATION)
                .start()
    }

    override fun animateOut(action: Function0<Unit>) {
        mViewBinding.root.animate().setListener(null).cancel()
        mViewBinding.root.animate().setDuration(BottomFunction.BOTTOM_DURATION)
                .withLayer()
                .setInterpolator(BottomOutInterpolator())
                .translationY(DeviceUtils.getScreenHeight() / 2f)
                .setListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        action.invoke()
                    }
                })
                .start()
    }

}