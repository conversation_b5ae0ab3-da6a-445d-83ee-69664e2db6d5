package com.commsource.camera.xcamera.cover.confirm

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PorterDuff
import android.media.MediaCodec
import android.media.MediaCodecInfo
import android.media.MediaFormat
import android.media.MediaMuxer
import android.view.Surface
import com.commsource.util.BitmapUtils
import com.commsource.util.DeviceLevelStrategy
import com.commsource.util.V
import com.commsource.videostudio.func.save.OutputSizeHelper
import com.commsource.videostudio.func.save.ResolutionItem
import com.commsource.videostudio.func.save.VideoCutHelper
import com.meitu.media.tools.editor.av.FFmpegMuxer
import java.io.IOException
import java.lang.IllegalStateException
import kotlin.math.ceil
import kotlin.math.min


object Utils {

    private const val MIME_TYPE: String = "video/avc" // H.264
    private const val FRAME_RATE: Int = 2// 视频帧率
    private const val IFRAME_INTERVAL: Int = 1 // 关键帧间隔（GOP 大小）

    const val TAG: String = "Utils combineImages2Video"

    @Throws(IOException::class)
    fun combineBitmaps2Video3(bitmaps: List<String>,
                             outputPath: String,
                             width: Int,
                             height: Int,
                             onSuccess: (duration: Long, width: Int, height: Int) -> Unit) {

        val minTotalDuration = 5
        val bitmapPlayInSeconds = 0.5

        val bitmapSize = bitmaps.size
        val loopCount = ceil(minTotalDuration / (bitmapPlayInSeconds * bitmapSize)).toInt()
        val frameIntervalUs = (bitmapPlayInSeconds * 1000000).toInt()

        val bitmapCacheMap = mutableMapOf<Int, Bitmap?>()
        val maxResolution = when (DeviceLevelStrategy.getDeviceLevel()) {
            DeviceLevelStrategy.VHIGH, DeviceLevelStrategy.HIGH -> ResolutionItem.Resolution1080
            else -> ResolutionItem.Resolution720
        }
        val outputSize = OutputSizeHelper.closeToTargetSize(width, height, maxResolution)
        val videoWidth = outputSize.first
        val videoHeight = outputSize.second

        // 配置编码器
        val mediaCodec = MediaCodec.createEncoderByType(MIME_TYPE)
        val mediaFormat = MediaFormat.createVideoFormat(MIME_TYPE, videoWidth, videoHeight)
        mediaFormat.setInteger(MediaFormat.KEY_BIT_RATE, 5000000)
        mediaFormat.setInteger(MediaFormat.KEY_FRAME_RATE, FRAME_RATE)
        mediaFormat.setInteger(
            MediaFormat.KEY_COLOR_FORMAT,
            MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface
        )
        mediaFormat.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, IFRAME_INTERVAL)
        mediaCodec.configure(mediaFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE)

        val inputSurface: Surface = mediaCodec.createInputSurface()
        mediaCodec.start()

        // 配置Muxer
        val mediaMuxer = MediaMuxer(outputPath, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4)
        var frameCount = 0
        var trackIndex = -1
        var muxerStarted = false
        val bufferInfo = MediaCodec.BufferInfo()
        var presentationTimeUs: Long = 0

        // 预加载所有图片
        bitmaps.forEachIndexed { index, filePath ->
            var bitmap = bitmapCacheMap[index]
            if (bitmap == null) {
                val oriBitmap = BitmapFactory.decodeFile(filePath)
                val scaleBitmap = resizeBitmapToTargetSize(oriBitmap, videoWidth, videoHeight)
                bitmapCacheMap[index] = scaleBitmap
            }
        }

        // 使用数组保存 trackIndex 以便在 drainEncoder 中更新
        val trackIndexRef = intArrayOf(-1)
        // 直接开始编码实际图片帧（无预热黑色帧）
        for (i in 0 until loopCount) {
            for (k in 0 until bitmapSize) {
                val bitmap = bitmapCacheMap[k] ?: continue
                // 绘制当前图片到输入Surface
                val canvas = inputSurface.lockCanvas(null)
                // 先清空背景（用透明或预设颜色，但这里一般无明显展示效果）
                canvas.drawColor(Color.BLACK, PorterDuff.Mode.CLEAR)
                canvas.drawBitmap(bitmap, 0f, 0f, null)
                inputSurface.unlockCanvasAndPost(canvas)
                bufferInfo.flags = MediaCodec.BUFFER_FLAG_KEY_FRAME
                // 排空编码器输出并更新时间戳
                presentationTimeUs = drainEncoder(
                    mediaCodec, mediaMuxer, bufferInfo,
                    trackIndexRef, presentationTimeUs, frameIntervalUs, isDrainEnd = false
                )
                Thread.sleep(50)
            }
        }

        // 通知编码器结束输入，并排空剩余数据
        mediaCodec.signalEndOfInputStream()
        drainEncoder(mediaCodec, mediaMuxer, bufferInfo, trackIndexRef, presentationTimeUs, frameIntervalUs, isDrainEnd = true)


        // 释放资源
        mediaCodec.stop()
        mediaCodec.release()
        mediaMuxer.stop()
        mediaMuxer.release()
        bitmapCacheMap.forEach { (_, bitmap) ->
            bitmap?.recycle()
        }

        val duration = (loopCount * bitmapSize * frameIntervalUs).toLong() / 1000000
        onSuccess.invoke(duration, videoWidth, videoHeight)
    }

    /**
     * 辅助方法：排空编码器的输出缓冲区并写入Muxer，同时更新 presentationTimeUs。
     *
     * @param mediaCodec 当前编码器实例
     * @param mediaMuxer 当前Muxer实例
     * @param bufferInfo 存储输出缓冲区信息的对象
     * @param trackIndexRef 用于保存Muxer track索引的可变引用（初始值为 -1）
     * @param currentPts 当前的 presentationTimeUs
     * @param frameIntervalUs 每帧间隔（微秒）
     * @param isDrainEnd 是否为结束排空（end-of-stream）
     * @return 更新后的 presentationTimeUs
     */
    private fun drainEncoder(
        mediaCodec: MediaCodec,
        mediaMuxer: MediaMuxer,
        bufferInfo: MediaCodec.BufferInfo,
        trackIndexRef: IntArray,
        currentPts: Long,
        frameIntervalUs: Int,
        isDrainEnd: Boolean
    ): Long {
        var pts = currentPts
        val timeoutUs = 10_000L
        var draining = true
        while (draining) {
            val outputBufferIndex = mediaCodec.dequeueOutputBuffer(bufferInfo, timeoutUs)
            when {
                outputBufferIndex == MediaCodec.INFO_TRY_AGAIN_LATER -> {
                    if (!isDrainEnd) {
                        draining = false
                    }
                }
                outputBufferIndex == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED -> {
                    if (trackIndexRef[0] == -1) {
                        val newFormat = mediaCodec.outputFormat
                        trackIndexRef[0] = mediaMuxer.addTrack(newFormat)
                        mediaMuxer.start()
                    }
                }
                outputBufferIndex >= 0 -> {
                    val encodedData = mediaCodec.getOutputBuffer(outputBufferIndex)
                        ?: throw IllegalStateException("编码器输出缓冲区 $outputBufferIndex 为 null")
                    if (bufferInfo.size != 0 && trackIndexRef[0] != -1) {
                        // 为当前帧设置正确的时间戳
                        bufferInfo.presentationTimeUs = pts
                        mediaMuxer.writeSampleData(trackIndexRef[0], encodedData, bufferInfo)
                        pts += frameIntervalUs
                    }
                    mediaCodec.releaseOutputBuffer(outputBufferIndex, false)
                    if ((bufferInfo.flags and MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) {
                        draining = false
                    }
                }
            }
        }
        return pts
    }


    fun convertBitmapToYUV420(bitmap: Bitmap, width: Int, height: Int): ByteArray {
        val yuv = ByteArray(width * height * 3 / 2) // YUV420 is Y + U + V, each Y, U, and V plane takes a different amount of space

        val rgb = IntArray(width * height)
        bitmap.getPixels(rgb, 0, width, 0, 0, width, height)

        var yIndex = 0
        var uvIndex = width * height // Start of U and V planes

        // Convert RGB to YUV
        for (i in 0 until height) {
            for (j in 0 until width) {
                val pixel = rgb[i * width + j]
                val r = (pixel shr 16) and 0xFF
                val g = (pixel shr 8) and 0xFF
                val b = pixel and 0xFF

                val y = (0.299 * r + 0.587 * g + 0.114 * b).toInt().coerceIn(0, 255)
                val u = (-0.169 * r - 0.331 * g + 0.5 * b + 128).toInt().coerceIn(0, 255)
                val v = (0.5 * r - 0.419 * g - 0.081 * b + 128).toInt().coerceIn(0, 255)

                // Y channel
                yuv[yIndex++] = y.toByte()

                // U and V channels (sampling is 4:2:0, so every 2x2 block shares U and V)
                if (i % 2 == 0 && j % 2 == 0) {
                    yuv[uvIndex++] = u.toByte()
                    yuv[uvIndex++] = v.toByte()
                }
            }
        }
        return yuv
    }

    fun resizeBitmapToTargetSize(bitmap: Bitmap, targetWidth: Int, targetHeight: Int): Bitmap {
        val originalWidth = bitmap.width
        val originalHeight = bitmap.height

        // 如果原图宽高与目标宽高一致，直接返回原图
        if (originalWidth == targetWidth && originalHeight == targetHeight) {
            return bitmap
        }

        // 目标 Bitmap
        val resultBitmap = Bitmap.createBitmap(targetWidth, targetHeight, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(resultBitmap)
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)

        var finalBitmap = bitmap
        var scale = 1f

        if (originalWidth == targetHeight && originalHeight == targetWidth) {
            // 宽高相反，需要旋转 90°
            val matrix = Matrix()
            matrix.postRotate(90f)
            finalBitmap = Bitmap.createBitmap(bitmap, 0, 0, originalWidth, originalHeight, matrix, true)
        }

        // 计算等比缩放比例
        scale = min(targetWidth.toFloat() / finalBitmap.width, targetHeight.toFloat() / finalBitmap.height)

        // 计算缩放后的尺寸
        val scaledWidth = (finalBitmap.width * scale).toInt()
        val scaledHeight = (finalBitmap.height * scale).toInt()

        // 计算居中偏移量
        val dx = (targetWidth - scaledWidth) / 2f
        val dy = (targetHeight - scaledHeight) / 2f

        // 计算缩放和位移
        val matrix = Matrix()
        matrix.postScale(scale, scale)
        matrix.postTranslate(dx, dy)

        // 绘制到目标 Bitmap
        canvas.drawBitmap(finalBitmap, matrix, paint)

        return resultBitmap
    }
}