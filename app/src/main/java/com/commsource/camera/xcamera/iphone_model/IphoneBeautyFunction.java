package com.commsource.camera.xcamera.iphone_model;

import com.commsource.camera.xcamera.cover.bottomFunction.BaseBottomSubFragment;
import com.commsource.camera.xcamera.cover.bottomFunction.effect.suspend.BeautyAcneSuspendFragment;

/**
 * @Desc : 美妆界面中悬浮栏功能
 */
public enum IphoneBeautyFunction {


    BeautyAcne("Suspend_Beauty_Acne", BeautyAcneSuspendFragment.class);

    private String tag;

    private Class<? extends BaseBottomSubFragment> fgClass;

    IphoneBeautyFunction(String tag, Class<? extends BaseBottomSubFragment> fgClass) {
        this.tag = tag;
        this.fgClass = fgClass;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Class<? extends BaseBottomSubFragment> getFgClass() {
        return fgClass;
    }

    public void setFgClass(Class<? extends BaseBottomSubFragment> fgClass) {
        this.fgClass = fgClass;
    }
}
