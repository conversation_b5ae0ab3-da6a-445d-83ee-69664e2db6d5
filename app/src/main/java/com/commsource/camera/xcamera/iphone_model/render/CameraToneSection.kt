package com.commsource.camera.xcamera.iphone_model.render

import com.meitu.mtlab.MTAiInterface.MT3rdpartyModule.MTSubColorToning.MTSubColorACUtilGL
import com.pixocial.camerasuite.camera.core.renders.effectbean.AbsEffectSection
import kotlin.math.max
import kotlin.math.min

class CameraToneSection : AbsEffectSection() {
    private var configPath: String = "MTAiModel/ColortoningModel/ColorAC"
    private var param: MTSubColorACUtilGL.ColorACParam = MTSubColorACUtilGL.ColorACParam()

    fun setConfig(path: String) {
        this.configPath = path
    }

    fun setExposure(value: Float) {
        param.exposure = paramGetInt(value)
    }

    fun setNatureSaturation(value: Float) {
        param.natrue_saturation = paramGetInt(value)
    }

    fun setBrilliance(value: Float) {
        param.brilliance = paramGetInt(value)
    }

    fun setHue(value: Float) {
        param.hue = paramGetInt(value)
    }

    fun setDehaze(value: Float) {
        param.dehaze = paramGetInt(value)
    }

    fun fetchConfigPath() = configPath
    fun fetchColorACParam() = param

    @Synchronized
    override fun onSyncCurrent2Target(target: AbsEffectSection) {
        (target as? CameraToneSection)?.let {
            it.configPath = configPath
            it.param = param
        }
    }

    override fun copy(): AbsEffectSection {
        val target = CameraToneSection()
        target.updateDetectType(target.needDetectType())
        onSyncCurrent2Target(target)
        return target
    }

    override fun isConfigChanged(old: AbsEffectSection?): Boolean {
        return this.configPath != (old as? CameraToneSection)?.configPath
    }

    private fun paramGetInt(value: Float): Int {
        return min(max((value * 100.0f).toInt(), -100), 100)
    }
}