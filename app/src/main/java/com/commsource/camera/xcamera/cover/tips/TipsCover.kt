package com.commsource.camera.xcamera.cover.tips

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.graphics.Color
import android.graphics.Rect
import android.text.TextUtils
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import androidx.lifecycle.Observer
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.CoverTipsBinding
import com.commsource.camera.mvp.annotation.CameraMode
import com.commsource.camera.util.XAnimatorCalculateValuer
import com.commsource.camera.xcamera.cover.AbsLazyCover
import com.commsource.camera.xcamera.cover.CameraCaptureViewModel
import com.commsource.camera.xcamera.cover.CameraConfigViewModel
import com.commsource.config.SubscribeConfig
import com.commsource.util.dp
import com.commsource.util.dpf
import com.commsource.util.gone
import com.commsource.util.setMarginTop
import com.commsource.util.visible
import com.commsource.widget.DisplayExtension
import com.commsource.widget.GDPRUtils
import com.commsource.widget.bind.marginTop
import com.meitu.common.AppContext
import com.meitu.library.util.device.DeviceUtils
import com.pixocial.androidx.core.extension.dp

/**
 * @Desc : 相机提示Cover
 * <AUTHOR> Bear - 2020/4/15
 */
class TipsCover : AbsLazyCover<CoverTipsBinding>() {

    private val tipsViewModel: TipsViewModel by lazy { getViewModel(TipsViewModel::class.java) }
    val cameraConfigViewModel by lazy { getViewModel(CameraConfigViewModel::class.java) }

    override fun getLayoutId(): Int {
        return R.layout.cover_tips
    }

    override fun onLazyCreate() {
        //相机提示
        tipsViewModel.tipsEvent.observe(coverGroup.mActivity, Observer { tips ->
            if (TextUtils.isEmpty(tips.first)) {
                //隐藏对应提示信息
            } else {
                //显示对应提示
                showTips(tips.first, tips.second)
            }
        })

        // 普通提示。
        tipsViewModel.floatingTipsEvent.observe(coverGroup.mActivity, Observer { tips ->
            if (!TextUtils.isEmpty(tips)) {
                showFloatingTips(tips, null, tipsViewModel.floatingTipsEvent.isLeftToRight)
            }
        })

        //提示
        tipsViewModel.multiFloatingTipsEvent.observe(coverGroup.mActivity, Observer {
            it?.let {
                showFloatingTips(
                        it.first,
                        it.second,
                        tipsViewModel.multiFloatingTipsEvent.isLeftToRight
                )
            }
        })

        //美妆类型提示
        tipsViewModel.makeupTipsEvent.observe(coverGroup.mActivity, Observer {
            it?.let {
                showFloatingTips(
                        it.first,
                        null,
                        tipsViewModel.makeupTipsEvent.isLeftToRight,
                        it.second
                )
            }
        })

        // 滤镜提示提示。
        tipsViewModel.filterTipsEvent.observe(coverGroup.mActivity, Observer { filterPair ->
            if (filterPair != null) {
                showFloatingTips(
                        filterPair.first.name,
                        filterPair.second,
                        tipsViewModel.filterTipsEvent.isLeftToRight
                )
            }
        })
    }

    override fun initView() {
    }

    override fun initViewModel() {
    }

    override fun onCoverSizeChange(fullRect: Rect, cameraViewPort: Rect) {
        super.onCoverSizeChange(fullRect, cameraViewPort)
        changeFilterTips(cameraViewPort)
    }

    override fun onCameraVisibleSizeChange(cameraViewPort: Rect, fraction: Float) {
        super.onCameraVisibleSizeChange(cameraViewPort, fraction)
        changeFilterTips(cameraViewPort)
    }

    /**
     * 改变FilterTips
     */
    private fun changeFilterTips(cameraViewPort: Rect) {

        var topMargin = cameraConfigViewModel.calculateTopBarSize() + DeviceUtils.dip2px(10f)
        // 根据视口更改订阅图标的高度
        if (cameraViewPort.top != 0) {
            topMargin = topMargin.coerceAtLeast(cameraViewPort.top + DeviceUtils.dip2px(14f))
        }

        if (!GDPRUtils.isEuroArea(AppContext.context) && !SubscribeConfig.isSubValid()) {
            topMargin = if (DeviceUtils.getStatusHeight() > 0) {
                topMargin.coerceAtLeast(DeviceUtils.getStatusHeight() + 62.dp())
            } else {
                topMargin.coerceAtLeast(106.dp())
            }
        }

        mViewBinding?.rlFloating?.marginTop( topMargin + 14.dp() +  28.dp())
    }

    /**
     * 显示提示信息
     */
    private fun showTips(tips: String, duration: Long) {
        createCover()
        mViewBinding!!.tvTips.animate().setStartDelay(0).setListener(null).cancel()
        mViewBinding!!.tvTips.visible()
        mViewBinding!!.tvTips.text = tips
        mViewBinding!!.tvTips.animate()
                .alpha(1f)
                .setStartDelay(0)
                .setDuration(200)
                .setListener(object : AnimatorListenerAdapter() {

                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        mViewBinding!!.tvTips.animate().setListener(null)
                                .alpha(0f)
                                .setStartDelay(duration)
                                .setDuration(200)
                                .start()
                    }
                })
                .start()
    }

    /**
     * 显示悬浮Tips
     */
    private fun showFloatingTips(
            tips: String?,
            subTips: String?,
            leftToRight: Boolean = true,
            colorStr: String? = null
    ) {
        createCover()
        tips?.let {
            if (TextUtils.isEmpty(colorStr)) {
                mViewBinding!!.flColor.gone()
            } else {
                mViewBinding!!.flColor.visible()
                mViewBinding!!.flColor.delegate.backgroundColor = Color.parseColor(colorStr)
            }
            mViewBinding!!.rlFloating.animate().setStartDelay(0).setListener(null).cancel()
            mViewBinding!!.rlFloating.visible()
            mViewBinding!!.rlFloating.alpha = 0f
            mViewBinding!!.tvFloating.text = tips
            mViewBinding!!.tvFloatingSub.run {
                if (subTips.isNullOrEmpty()) {
                    gone()
                } else {
                    visible()
                    text = subTips
                }
            }
            mViewBinding!!.rlFloating.translationX =
                    if (leftToRight) (-DeviceUtils.dip2px(30f)).toFloat() else (DeviceUtils.dip2px(30f)).toFloat()
            mViewBinding!!.rlFloating.animate()
                    .alpha(1f)
                    .setStartDelay(0)
                    .translationX(0f)
                    .setInterpolator(DecelerateInterpolator())
                    .setDuration(1000)
                    .setListener(object : AnimatorListenerAdapter() {

                        override fun onAnimationEnd(animation: Animator) {
                            super.onAnimationEnd(animation)
                            mViewBinding!!.rlFloating.animate().setListener(null)
                                    .alpha(0f)
                                    .translationX(
                                            if (leftToRight) 30.dpf() else (-30).dpf()
                                    )
                                    .setInterpolator(AccelerateInterpolator())
                                    .setDuration(1000)
                                    .start()
                        }
                    })
                    .start()
        }
    }

    var translationYValuer = XAnimatorCalculateValuer(0f, DeviceUtils.dip2fpx(50f))

    override fun onViewRotationChange(rotation: Float, fraction: Float) {
        super.onViewRotationChange(rotation, fraction)
        //文案不参与旋转
        // mViewBinding?.rlFloating?.rotation = rotation
        // mViewBinding?.tvTips?.rotation = rotation
        // mViewBinding?.tvTips?.translationY = translationYValuer.caculateValue(if (coverGroup.isDeviceOrientationVertical()) 1 - fraction else fraction)
    }
}