package com.commsource.camera.xcamera.glow_model.cover

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.Observer
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.viewModelScope
import com.commsource.album.BpAlbumJumpRouter
import com.commsource.album.SelectPhotoInterstitialCover
import com.commsource.album.XAlbum
import com.commsource.album.XAlbumConfig
import com.commsource.album.def.AlbumSource
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.CoverGlowTopBarBinding
import com.commsource.camera.common.CameraContainerViewModel
import com.commsource.camera.mvp.annotation.CameraMode
import com.commsource.camera.xcamera.bean.SwitchCameraMode
import com.commsource.camera.xcamera.bean.getAnalyticsName
import com.pixocial.camerasuite.commsource.camera.mvp.annotation.CameraRatioType
import com.commsource.camera.xcamera.cover.CameraConfigViewModel
import com.commsource.camera.xcamera.cover.bottomFunction.GuideViewModel
import com.commsource.camera.xcamera.glow_model.GlowCameraViewModel
import com.commsource.camera.xcamera.glow_model.GlowModelCache
import com.commsource.camera.xcamera.glow_model.viewmodel.GlowCaptureStateViewModel
import com.commsource.camera.xcamera.glow_model.viewmodel.GlowBottomFunctionViewModel
import com.commsource.camera.xcamera.glow_model.viewmodel.GlowSettingViewModel
import com.commsource.config.ApplicationConfig

import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.statistics.trace.TraceManager
import com.commsource.statistics.trace.TraceModule
import com.commsource.studio.ImageStudioActivity
import com.commsource.studio.ImageStudioViewModel
import com.commsource.util.*
import com.commsource.videostudio.VideoStudioActivity
import com.commsource.videostudio.VideoStudioEnterSource
import com.commsource.widget.IconFrontView
import com.meitu.common.AppContext
import com.pixocial.camerasuite.camera.core.service.notifier.VideoNotifier
import com.pixocial.framework.cover.AbstractViewBindingCover
import com.pixocial.library.albumkit.media.MediaType
import kotlinx.coroutines.launch

class GlowTopBarCover : AbstractViewBindingCover<CoverGlowTopBarBinding>(), LifecycleObserver {

    val glowCameraViewModel by lazy { fragmentViewModel(GlowCameraViewModel::class) }

    val glowSettingViewModel by lazy { fragmentViewModel(GlowSettingViewModel::class) }

    val glowBottomFunctionViewModel by lazy { fragmentViewModel(GlowBottomFunctionViewModel::class) }

    /**
     * 相机拍照ViewModel
     */
    val cameraCaptureStateViewModel by lazy { fragmentViewModel(GlowCaptureStateViewModel::class) }

    val guideViewModel by lazy { fragmentViewModel(GuideViewModel::class) }

    val containerActivityViewModel by lazy { activityViewModel(CameraContainerViewModel::class) }

    override fun getLayoutId(): Int {
        return R.layout.cover_glow_top_bar
    }


    override fun onViewDidLoad() {
        lifecycleOwner.lifecycle.addObserver(this)
        viewBinding.rlTopBar.setMarginTop(CameraConfigViewModel.getCameraSafePaddingTop())
        selectRadio()
        // 设置监听
        viewBinding.mIvBack.setOnClickListener {
            MTAnalyticsAgent.logEvent(
                MTAnalyticsConstant.EVENT_SELFIE_BACK,
                HashMap<String, String>(4).apply {
                    when (cameraCaptureStateViewModel.getMode()) {
                        CameraMode.MOVIE_MODE -> this["mode_a"] = "movie"
                        CameraMode.CAPTURE_MODE -> this["mode_a"] = "shoot"
                        CameraMode.VIDEO_MODE -> this["mode_a"] = "video"
                    }
                    this[MTAnalyticsConstant.KEY_BACK] = MTAnalyticsConstant.VALUE_VIEW_BACK_UP
                    this[MTAnalyticsConstant.camera_mode] = MTAnalyticsConstant.MODE_GLOW
                })
            TraceManager.pop(TraceModule.CAMERA)
            GlowModelCache.closeCamera()
            coverContainer.attachFragment.activity?.let {
                it.finish()
                VerifyMothod.doOutAnimationEx(it)
            }
            VerifyMothod.doOutAnimationEx(coverContainer.attachFragment.requireActivity())
        }


        viewBinding.icvBeauty.setOnClickListener {
            glowCameraViewModel.isBeautyOn.value = !glowCameraViewModel.isBeautyOn.value
            if (glowCameraViewModel.isBeautyOn.value) {
                glowCameraViewModel.logFuncClick("美颜")
            }
        }

        viewBinding.icvFaceShape.setOnClickListener {
            glowCameraViewModel.isFaceShapeOn.value = !glowCameraViewModel.isFaceShapeOn.value
            if (glowCameraViewModel.isFaceShapeOn.value) {
                glowCameraViewModel.logFuncClick("美型")
            }
        }

        viewBinding.mIvSwitchRatio.setOnClickListener {
            glowSettingViewModel.showRadio(true)
            glowCameraViewModel.logFuncClick("画幅")

        }


        glowCameraViewModel.fetchCameraRatioEvent()?.observe(lifecycleOwner) {
            updateByRatio(it)
        }


        glowCameraViewModel.viewModelScope.launch {
            glowCameraViewModel.viewRotationFlow.collect {
                viewBinding.topBarGroup.referencedIds.forEach {id->
                    viewBinding.root.findViewById<android.view.View>(id)?.rotation = it.toFloat()
                }
            }
        }

        glowCameraViewModel.viewModelScope.launch {
            glowCameraViewModel.isLightMode.collect {
                val iconColor = if (it) {
                    R.color.Gray_A.resColor()
                } else {
                    R.color.white.resColor()
                }
                viewBinding.topBarGroup.referencedIds.forEach {id->
                    viewBinding.root.findViewById<IconFrontView>(id)?.setTextColor(iconColor)
                }
            }
        }

        glowCameraViewModel.viewModelScope.launch {
            glowCameraViewModel.isCleanMode.collect {
                checkUiState()
            }
        }
    }


    private fun updateByRatio(ratioType: Int) {
        when (ratioType) {
            CameraRatioType.PICTURE_RATIO_1_1 -> {
                viewBinding.mIvSwitchRatio.setText(R.string.selfie_top_icon_1_1)
                viewBinding.mIvSwitchRatio.tag = "PICTURE_RATIO_1_1"
            }

            CameraRatioType.PICTURE_RATIO_4_3 -> {
                viewBinding.mIvSwitchRatio.setText(R.string.selfie_top_icon_3_4)
                viewBinding.mIvSwitchRatio.tag = "PICTURE_RATIO_3_4"
            }

            CameraRatioType.PICTURE_RATIO_FULL -> {
                viewBinding.mIvSwitchRatio.setText(R.string.selfie_top_icon_full)
                viewBinding.mIvSwitchRatio.tag = "PICTURE_RATIO_full"
            }

            CameraRatioType.PICTURE_RATIO_9_16 -> {
                viewBinding.mIvSwitchRatio.setText(R.string.selfie_top_icon_9_16)
                viewBinding.mIvSwitchRatio.tag = "PICTURE_RATIO_9_16"

            }
        }


        glowSettingViewModel.updateCameraPreviewRatio(ratioType)
    }



    override fun onBindViewModel() {
        // 摄像头改变。


        glowCameraViewModel.takePictureStateEvent.observe(lifecycleOwner) {
            if (it == GlowCameraViewModel.PRE_TAKE_PICTURE) {
                glowSettingViewModel.dismissAll()
            }
        }

        glowCameraViewModel.fetchVideoRecordingEvent()?.observe(lifecycleOwner) {
            checkUiState()
        }

        glowCameraViewModel.viewModelScope.launch {
            glowCameraViewModel.isBeautyOn.collect {
                viewBinding.icvBeauty.text = if (it) {
                    R.string.camera_glowcam_icon_beauty_on.string()
                } else {
                    R.string.camera_glowcam_icon_beauty_off.string()

                }
                viewBinding.icvBeauty.tag = if (it) {
                    "开"
                } else {
                    "关"
                }
            }
        }

        glowCameraViewModel.viewModelScope.launch {
            glowCameraViewModel.isFaceShapeOn.collect {
                viewBinding.icvFaceShape.text = if (it) {
                    R.string.camera_glowcam_icon_face_on.string()
                } else {
                    R.string.camera_glowcam_icon_face_off.string()
                }

                viewBinding.icvFaceShape.tag = if (it) {
                    "开"
                } else {
                    "关"
                }
            }
        }

    }

    private var isShow = true

    private fun checkUiState() {
        val isNotRecording =
            glowCameraViewModel.fetchVideoRecordingEvent()?.value !is VideoNotifier.VideoState.Recording && glowCameraViewModel.fetchVideoRecordingEvent()?.value !is VideoNotifier.VideoState.StartRecord
        if (!glowCameraViewModel.isCleanMode.value && isNotRecording) {
            if (!isShow) {
                isShow = true
                viewBinding.root.alphaShow()
            }
        } else {
            if (isShow) {
                isShow = false
                viewBinding.root.alphaDismiss()
            }
        }

    }


    private fun selectRadio() {
        when (GlowModelCache.getGlowModelCacheEntity().ratio) {
            CameraRatioType.PICTURE_RATIO_1_1 -> {
                viewBinding.mIvSwitchRatio.text = R.string.selfie_top_icon_1_1.string()
            }

            CameraRatioType.PICTURE_RATIO_4_3 -> {
                viewBinding.mIvSwitchRatio.text = R.string.selfie_top_icon_3_4.string()
            }

            CameraRatioType.PICTURE_RATIO_9_16 -> {
                viewBinding.mIvSwitchRatio.text = R.string.selfie_top_icon_9_16.string()
            }

            CameraRatioType.PICTURE_RATIO_FULL -> {
                viewBinding.mIvSwitchRatio.text = R.string.selfie_top_icon_full.string()
            }

            else -> {
                viewBinding.mIvSwitchRatio.text = R.string.selfie_top_icon_full.string()
            }
        }
    }

    /**
     * 要根据补光的颜色来改变顶部icon的颜色
     */
    private fun changeTopBarStyle(themeColor: Int) {

    }

}