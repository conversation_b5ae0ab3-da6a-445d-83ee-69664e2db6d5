package com.commsource.camera.xcamera.cover.transaction

import android.graphics.Bitmap
import androidx.lifecycle.Observer
import com.commsource.ad.ADCache
import com.commsource.ad.AdPreloader
import com.commsource.ad.AdSlotIds
import com.commsource.advertisiting.UserPortraitStrategy
import com.commsource.advertisiting.newad.NewInterstitialAdHelper
import com.commsource.beautyplus.BaseActivity
import com.commsource.beautyplus.face.FaceUtil
import com.commsource.beautyplus.face.Gender
import com.commsource.beautyplus.router.RouterEntity
import com.commsource.beautyplus.router.UriConstant
import com.commsource.camera.beauty.ArAnalyAgent
import com.commsource.camera.fastcapture.FastCaptureController
import com.commsource.camera.fastcapture.SelfiePhotoData
import com.commsource.camera.mvp.annotation.CameraMode
import com.commsource.camera.util.FilterUtil
import com.commsource.camera.xcamera.BpCameraViewModel
import com.commsource.camera.xcamera.CameraAnalytics
import com.commsource.camera.xcamera.FastCaptureViewModel
import com.commsource.camera.xcamera.bean.SwitchCameraMode
import com.commsource.camera.xcamera.cover.AbsTransaction
import com.commsource.camera.xcamera.cover.CameraCaptureViewModel
import com.commsource.camera.xcamera.cover.CameraConfigViewModel
import com.commsource.camera.xcamera.cover.bottomFunction.effect.advance.CameraFunc
import com.commsource.camera.xcamera.cover.bottomFunction.effect.beauty.BeautyViewModel
import com.commsource.camera.xcamera.cover.bottomFunction.effect.look.LookViewModel
import com.commsource.camera.xcamera.util.EffectUseLogger
import com.commsource.statistics.BeautySaveAnalyAngent
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.MaterialBuilder
import com.commsource.statistics.SpmAnalytics
import com.commsource.statistics.SpmParamConstant
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.statistics.trace.TraceManager
import com.commsource.statistics.trace.TraceModule
import com.commsource.util.delegate.IProcessHandler
import com.commsource.util.delegate.process.SelfieSubscribeProcess
import com.commsource.util.set
import com.meitu.hwbusinesskit.core.HWBusinessSDK
import com.meitu.hwbusinesskit.core.utils.TestLog
import com.meitu.library.hwanalytics.spm.SPMShare
import com.meitu.library.util.net.NetUtils
import com.pixocial.androidx.core.utils.isSameDay

/**
 * @Desc : 拍照、录制视频相关 相关事务
 * <AUTHOR> Bear - 2020/5/27
 */
class CameraTransaction : AbsTransaction() {

    val cameraCaptureViewModel by lazy { getViewModel(CameraCaptureViewModel::class.java) }

    val bpCameraViewModel by lazy { getViewModel(BpCameraViewModel::class.java) }

    val beautyViewModel by lazy { getViewModel(BeautyViewModel::class.java) }

    val cameraConfigViewModel by lazy { getViewModel(CameraConfigViewModel::class.java) }

    /**
     * 快速拍照数据处理类
     */
    private val fastCaptureViewModel by lazy { getViewModel(FastCaptureViewModel::class.java) }

    private val lookViewModel by lazy { getViewModel(LookViewModel::class.java) }

    override fun onAttachTransaction() {
        //处理快速拍照
        bpCameraViewModel.captureEvent.observe(coverGroup.mActivity, Observer {
            it?.let { captureResultBean ->
                //在confirmCover中已经处理好了拍照的selfiePhotoData
                fastCaptureViewModel.fastCaptureSelfiePhotoData?.let { selfiePhotoData ->
                    //更新数据
                    selfiePhotoData.glOriBitmap = captureResultBean.oriBitmap
                    selfiePhotoData.glEffectBitmap = captureResultBean.effectBitmap
                    selfiePhotoData.screenOrientation = captureResultBean.screenOrientation
                    selfiePhotoData.screenShotBitmap = captureResultBean.effectBitmap
                    // 赋值人脸数据。
                    selfiePhotoData.faceData = captureResultBean.faceData
                    selfiePhotoData.aiFaceData = captureResultBean.aiFaceResult
                    selfiePhotoData.aiFaceData?.takeIf { it.faces != null && it.faces.isNotEmpty() }
                        ?.let {
                            for (element in it.faces) {
                                when (FaceUtil.getGender(element)) {
                                    Gender.Female -> selfiePhotoData.femaleCount++
                                    Gender.Male -> selfiePhotoData.maleCount++
                                    Gender.Unknown -> {
                                    }
                                }
                            }
                        }
                    if (fastCaptureViewModel.isOpen && !cameraCaptureViewModel.isMovieMode() && !cameraCaptureViewModel.isInAppCaptureMode()) {// 电影模式不支持快速自拍。

                        val fastCaptureAction: () -> Unit = {
                            // 快速自拍
                            EffectUseLogger.instance.logSelfieTakeEvent(selfiePhotoData)
                            BeautySaveAnalyAngent.doPhotoSaveStatistics(3, selfiePhotoData)
                            if (selfiePhotoData.isAr || selfiePhotoData.isArGiphy) {
                                ArAnalyAgent.artakepicture(selfiePhotoData)
                                ArAnalyAgent.arpicturesave(
                                    selfiePhotoData.arFaceCount,
                                    selfiePhotoData.filter,
                                    selfiePhotoData.arMaterialId,
                                    selfiePhotoData.arMaterialGroup,
                                    selfiePhotoData.isUseArFilter,
                                    selfiePhotoData.isArVideoReward,
                                    selfiePhotoData.arMaterialGroup,
                                    selfiePhotoData.isArGiphy
                                )
                            }

                            fastCaptureViewModel.fastCapture(selfiePhotoData) { isSuccess ->
                                if (bpCameraViewModel.supportBurstMode(selfiePhotoData.takePictureBtn) && !cameraCaptureViewModel.isCreateMontageMode()) {
                                    bpCameraViewModel.singleCaptureEvent.isSuccess = isSuccess
                                    bpCameraViewModel.singleCaptureEvent.postValue(selfiePhotoData)
                                } else {
                                    bpCameraViewModel.restoreTakeByGestureEvent()
                                    // 单拍
                                    FastCaptureController.getInstance().saveResultEvent.set(isSuccess)
                                    fastCaptureViewModel.fastCaptureSelfiePhotoData = null
                                }
                            }
                        }

                        // 单张拍后
                        val isNeedShowSubscribeOrAd = !bpCameraViewModel.supportBurstMode(selfiePhotoData.takePictureBtn)
                        if (isNeedShowSubscribeOrAd) {
                            var doAdFunction = {
                                doAd(selfiePhotoData,captureResultBean.effectBitmap, fastCaptureAction)
                            }
                            doCheckSubscribe(selfiePhotoData, doAdFunction )
                        } else {
                            fastCaptureAction()
                        }
                        return@Observer
                    }
                }
            }
        })

        bpCameraViewModel.beforeCaptureForOpenEvent.observe(coverGroup.mActivity, Observer {
            val fastCaptureAction: () -> Unit = {
                fastCaptureViewModel.isCheckSubscribe = true
                bpCameraViewModel.startBurstTaskPicture()
            }

            it?.let { selfiePhotoData ->
                doCheckSubscribe(selfiePhotoData, fastCaptureAction)
            }
        })

        bpCameraViewModel.afterCaptureForOpenEvent.observe(coverGroup.mActivity, Observer {
            if (it == true) {
                val selfiePhotoData = bpCameraViewModel.singleCaptureEvent.value
                if (selfiePhotoData != null) {
                    doAd(selfiePhotoData, selfiePhotoData.glEffectBitmap, null)
                }
            }
        })

        fastCaptureViewModel.saveResultEvent.observe(coverGroup.mActivity) { saveResult ->
            if (saveResult) {
                // 快速拍照保存成功后，需要更新限免次数
                beautyViewModel.updateLimitFreeInfo()
                // 刷新TraceInfo
                TraceManager.refreshTraceInfo(TraceModule.CAMERA)
                CameraAnalytics.logCameraAppr(SwitchCameraMode.Classic)
                cameraCaptureViewModel.logCameraApprEvent(bpCameraViewModel.cameraRatioEvent.value)

            }
        }

        // 监听模式变化
        cameraCaptureViewModel.cameraCaptureModeEvent.observe(
            coverGroup.mActivity,
            Observer { mode ->
                cameraCaptureViewModel.logCameraApprEvent(bpCameraViewModel.cameraRatioEvent.value)
                if (mode == CameraMode.MOVIE_MODE) {
                    MTAnalyticsAgent.logEvent(MTAnalyticsConstant.SELFIE_MOVIE)
                }
            })

        bpCameraViewModel.singleCaptureEvent.observe(coverGroup.mActivity, Observer {
            if (bpCameraViewModel.singleCaptureEvent.isSuccess && bpCameraViewModel.isBrustMode()) {
                it?.let {  pictureData ->
                    bpCameraViewModel.updateBurstCount()
                    if (!fastCaptureViewModel.isOpen) {
                        // 非自动保存闪一下屏
                        fastCaptureViewModel.flashViewEvent.value = true
                        bpCameraViewModel.saveSelfiePhotoData2Cache(it) {
                            bpCameraViewModel.setBurstTaskPictureEvent(true)
                        }
                    } else {
                        bpCameraViewModel.setBurstTaskPictureEvent(true)
                    }

                }
            }
        })
    }

    /**
     * 检查订阅
     */
    private fun doCheckSubscribe(selfiePhotoData: SelfiePhotoData, fastCaptureAction: () -> Unit) {
        val beautyShapeFuncs = beautyViewModel.useNeedPayEffectWithLimitFree()
        if (SelfieSubscribeProcess.needSubscribe(
                selfiePhotoData,
                beautyShapeFuncs.size > 0
            )
        ) {
            addSpmSourceFeatureContent(selfiePhotoData, beautyShapeFuncs)
            IProcessHandler(coverGroup.mActivity)
                .execute(object : SelfieSubscribeProcess(selfiePhotoData) {
                    override fun onUseStateResult(
                        isSubcribe: Boolean,
                        canFreeUseOnce: Boolean
                    ) {
                        if (isSubcribe || canFreeUseOnce) {
                            // 订阅成功-快速拍照
                            bpCameraViewModel.subcribeSuccessEvent.value = true
                            fastCaptureAction.invoke()
                        } else {
                            if (bpCameraViewModel.supportBurstMode(selfiePhotoData.takePictureBtn)) {
                                bpCameraViewModel.shutDownBurstTakePicture()
                            }
                        }
                    }
                }.apply {
                    isEnableReward = true
                    isEnableInterstitialAd = false
                })
            return
        }
        fastCaptureAction.invoke()
    }

    /**
     * 检查拍后广告 for 自动保存拍照
     */
    private fun doAd(selfiePhotoData: SelfiePhotoData, effectBitmap: Bitmap?, fastCaptureAction: (() -> Unit)?) {
        preloadAd()
        // todo zdf AR 不需要分享AR，可删除
//        if (ArMaterialUtil.needShowShareDialog(selfiePhotoData.arMaterial)) {
//            if (AppTools.isFinishing(coverGroup.mActivity)) {
//                return
//            }
//            val isNeedArWaterMark = selfiePhotoData.arMaterial?.isNeedWaterMark == 1
//            val shareBitmap = WaterMarkUtils.drawWaterMark(
//                effectBitmap,
//                WaterMarkUtils.getWaterEntityDirectly(1001),
//                true,
//                isNeedArWaterMark
//            )
//            val shareImagePath = PathUtil.getArShareImagePath()
//            val result = BitmapUtil.saveImageToDisk(
//                shareBitmap,
//                shareImagePath,
//                100,
//                Bitmap.CompressFormat.JPEG
//            )
//            if (!result || !FileUtils.isFileExist(shareImagePath)) {
//                return
//            }
//            val arShareDialog = XDialog()
//            arShareDialog.VideoPictureTips {
//                cancelAble = true
//                closeEnable = true
//                PositiveButton(ResourcesUtils.getString(R.string.share)) { _dialog ->
//                    IProcessHandler(coverGroup.mActivity)
//                        .execute(object :
//                            ShareImageProcess(listOf(shareImagePath)) {
//                            override fun onShareResult() {
//                                ArMaterialRepository.getInstance()
//                                    .updateShared(selfiePhotoData.arMaterial)
//                                fastCaptureAction?.invoke()
//                            }
//                        })
//                    _dialog.dismiss()
//                }
//                pictureConfig = PictureConfig(null, null, shareBitmap)
//                popupCenter()
//            }
//            arShareDialog.show()
//
//            val map = HashMap<String, String>(4)
//            map[MTAnalyticsConstant.AR_ID] =
//                selfiePhotoData.arMaterial!!.id.toString()
//            MTAnalyticsAgent.logEvent(MTAnalyticsConstant.EVENT_AR_SHARE_IMP, map)
//            return
//        }
        showInterstitialAd(fastCaptureAction)
    }

    /**
     * 自拍第x次拍照预加载
     */
    private fun preloadAd() {
        if (cameraCaptureViewModel.isCaptureMode()) {
            val adSlotId = AdSlotIds.ad_camera_save
            if (!ADCache.getLastEnterTime(adSlotId).isSameDay(System.currentTimeMillis())) {
                // 重置进入次数
                ADCache.resetEnterCount(adSlotId)
            }
            if (!ADCache.getLastUnlockTime(adSlotId).isSameDay(System.currentTimeMillis())) {
                // 解锁时间和当前不是同一天了，重置解锁次数
                ADCache.resetUnlockCount(adSlotId)
            }
            val enterCount = ADCache.getEnterCount(adSlotId)
            val unlockCount = ADCache.getUnlockCount(adSlotId)
            // 判断是否需要预加载
            val interstitialAd = HWBusinessSDK.getInterstitialAd(adSlotId)
            val dailyMaxUnlockTimes =
                interstitialAd.getDailyMaxUnlockTimes(UserPortraitStrategy.getGroupIds())
            var needShowAd = !AdPreloader.isNeedDisableAd() && NetUtils.canNetworking()
            needShowAd = needShowAd && interstitialAd.isOpen
            needShowAd =
                needShowAd && (dailyMaxUnlockTimes <= 0 || unlockCount < dailyMaxUnlockTimes)
            //
            val needPreloadAd =
                needShowAd && (enterCount >= interstitialAd.getAdBeginShowCount(UserPortraitStrategy.getGroupIds()) - 1)
            if (needPreloadAd) {
                interstitialAdHelper.preload(coverGroup.mActivity)
            }
        }
    }

    private val interstitialAdHelper = NewInterstitialAdHelper(AdSlotIds.ad_camera_save)

    /**
     *
    1. 广告位支持在线开关
    2. 每日第x次保存展示插屏广告（x值默认“6”）
    3. 每日可展示插屏次数y次（y值默认“1”）
     */
    private fun showInterstitialAd(fastCapture: (() -> Unit)?) {
        val adSlotId = AdSlotIds.ad_camera_save
        NewInterstitialAdHelper.handleEnterCount(adSlotId)

        if (!ADCache.getLastUnlockTime(adSlotId).isSameDay(System.currentTimeMillis())) {
            // 解锁时间和当前不是同一天了，重置解锁次数
            ADCache.resetUnlockCount(adSlotId)
        }

        val enterCount = ADCache.getEnterCount(adSlotId)
        val unlockCount = ADCache.getUnlockCount(adSlotId)
        // 判断是否需要预加载
        val interstitialAd = HWBusinessSDK.getInterstitialAd(adSlotId)
        val dailyMaxUnlockTimes =
            interstitialAd.getDailyMaxUnlockTimes(UserPortraitStrategy.getGroupIds())
        var needShowAd = !AdPreloader.isNeedDisableAd() && NetUtils.canNetworking()
        needShowAd = needShowAd && interstitialAd.isOpen
        needShowAd = needShowAd && (dailyMaxUnlockTimes <= 0 || unlockCount < dailyMaxUnlockTimes)
        needShowAd =
            needShowAd && (enterCount >= interstitialAd.getAdBeginShowCount(UserPortraitStrategy.getGroupIds()))

        TestLog.log("needShowAd:$needShowAd")
        if (needShowAd) {
            TestLog.log("$adSlotId 后台设置${interstitialAd.getAdBeginShowCount(UserPortraitStrategy.getGroupIds())}次触发，今日已触发${enterCount}次,needShowAd:$needShowAd")
            TestLog.log("$adSlotId 后台设置${dailyMaxUnlockTimes}次解锁限制(小于等于0为无限制)，今日已解锁${unlockCount}次")
            val baseActivity = coverGroup.mActivity as BaseActivity
            interstitialAdHelper.showAd(
                baseActivity,
                loadingCallback = { loading ->
                    if (loading) {
                        baseActivity.showLoadingDialog()
                    } else {
                        baseActivity.dismissLoadingDialog()
                    }
                },
                resultCallback = {
                    baseActivity.dismissLoadingDialog()
                    if (it) {
                        ADCache.addUnlockCount(adSlotId)
                        ADCache.setLastUnlockTime(adSlotId, System.currentTimeMillis())
                    }
                    fastCapture?.invoke()
                })
        } else {
            fastCapture?.invoke()
        }
    }

    override fun onDetachTransaction() {
        interstitialAdHelper.destroy()
    }

    override fun onHandleUIProtocol(routerEntity: RouterEntity) {
        when (routerEntity.lastPathSegment) {
            UriConstant.PATH_M_MOVIE -> {
                //电影模式
                cameraCaptureViewModel.updateMode(CameraMode.MOVIE_MODE)
            }

            UriConstant.PATH_M_VIDEO -> {
                //视频模式
                cameraCaptureViewModel.updateMode(CameraMode.VIDEO_MODE)
            }
        }
    }

    override fun onHandleProtocol(routerEntity: RouterEntity) {

    }

    private fun addSpmSourceFeatureContent(
        selfieData: SelfiePhotoData,
        beautyShapeFuncs: MutableList<CameraFunc>
    ) {
        SPMShare.put(SpmParamConstant.KEY_SOURCE_CLICK_POSITION, "保存")
        val hashSet = HashSet<String>(4)
        val builder = MaterialBuilder()
        selfieData.arMaterial?.let {
            if (it.isNeedPaid()) {
                hashSet.add(it.materialId)
                builder.addMaterial(it.materialId, null)
            }
        }
        selfieData.filterWrapper?.let {
            if (FilterUtil.needPay(it.filter)) {
                hashSet.add(it.filter.id)
                builder.addMaterial(it.filter.id, it.categoryId)
            }
        }
        selfieData.makeupWrappers?.let {
            for (i in 0 until it.size()) {
                if (!it.valueAt(i).isPreset() && it.valueAt(i).isNeedPay()) {
                    val id = it.valueAt(i).getFeatureContent()
                    hashSet.add(id)
                    builder.addMaterial(id, "-1,")
                }
            }
        }
        selfieData.makeupStyleMaterials?.let {
            for (i in 0 until it.size()) {
                if (!it.valueAt(i).isPreset() && it.valueAt(i).isNeedPay()) {
                    val id = it.valueAt(i).getFeatureContent()
                    hashSet.add(id)
                    builder.addMaterial(id, "-1,")
                }
            }
        }

        //look
        val look = lookViewModel.applyLookEvent.value
        if (look?.isPayLook() == true) {
            hashSet.add(look.onlineId)
            builder.addMaterial(look.onlineId, look.belongCatId)
        }
        builder.builder(true)
        val functionSet = HashSet<String>(8)
        // update by btj 高级美颜
        if (beautyShapeFuncs.size > 0) {
            beautyShapeFuncs.forEach { func ->
                val staticsName = func.feature.sourceFeatureName
                hashSet.add(staticsName)
                functionSet.add(staticsName)
            }
        }

        SpmAnalytics.addSourceFeatureContentParam(hashSet)
        SPMShare.put(SpmParamConstant.KEY_PRF_FUNC, SpmAnalytics.appendIds2String(functionSet))
    }

}