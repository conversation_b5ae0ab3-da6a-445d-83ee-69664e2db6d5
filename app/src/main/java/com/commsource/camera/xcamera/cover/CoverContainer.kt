package com.commsource.camera.xcamera.cover

import android.content.Context
import android.content.Intent
import android.util.AttributeSet
import android.view.KeyEvent
import android.view.MotionEvent
import android.widget.FrameLayout
import com.commsource.beautyplus.router.RouterEntity
import com.commsource.util.ipermission.PermissionResult

/**
 * @Desc : Cover容器
 * <AUTHOR> Bear - 2021/1/7
 * 相机页面的主容器 以后所有相机界面仅需要一个CoverContainer + BPCameraViewModel + 其他业务ViewModel就可以搭建不同的相机界面
 * 希望之后 多人拍照和帮拍等业务也重构使用此结构 方便之后业务维护
 */
class CoverContainer @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : FrameLayout(context, attrs, defStyleAttr) {

    companion object {
        const val TAG = "CoverContainer"
    }

    fun addCoverGroup(coverGroup: CoverGroup): CoverContainer {
        if (indexOfChild(coverGroup) == -1) {
            coverGroups.add(coverGroup)
            coverGroup.container = this
            addView(coverGroup, -1, -1)
        }
        return this
    }

    /**
     * coverGroups
     */
    var coverGroups = ArrayList<CoverGroup>()

    /**
     * 返回键拦截权重队列
     */
    var backPressTransactions = ArrayList<ITransaction>()

    /**
     * 物理按键权重队列
     */
    var physicEventTransactions = ArrayList<ITransaction>()

    /**
     * 通过重新排序
     * 确认结果
     *
     * 针对部分拦截的事件  需要分发的事件做排序处理 方便拦截先后顺序
     */
    fun order() {
        backPressTransactions.clear()
        physicEventTransactions.clear()
        backPressTransactions.addAll(coverGroups)
        physicEventTransactions.addAll(coverGroups)
        backPressTransactions.sortWith(Comparator { o1, o2 ->
            when {
                o1.getBackPressedWeight() == o2.getBackPressedWeight() -> 0
                o1.getBackPressedWeight() > o2.getBackPressedWeight() -> -1
                else -> 1
            }
        })
        physicEventTransactions.sortWith(Comparator { o1, o2 ->
            when {
                o1.getPhysicKeyEventWeight() == o2.getPhysicKeyEventWeight() -> 0
                o1.getPhysicKeyEventWeight() > o2.getPhysicKeyEventWeight() -> -1
                else -> 1
            }
        })
    }

    /**
     * 懒加载创建
     */
    fun onLazyCreate() {
        for (cover in coverGroups) {
            cover.onLazyCreate()
        }
    }

    /**
     * 拦截处理协议
     */
    fun dispatchProtocol(routerEntity: RouterEntity) {
        for (cover in coverGroups) {
            cover.onHandleProtocol(routerEntity)
        }
    }

    /**
     * 拦截处理UI协议
     */
    fun dispatchUIProtocol(routerEntity: RouterEntity) {
        for (cover in coverGroups) {
            cover.onHandleUIProtocol(routerEntity)
        }
    }

    /**
     * 拦截物理按键
     */
    fun dispatchPhysicKeyEvent(event: KeyEvent?): Boolean {
        var isDispatch = false
        for (cover in physicEventTransactions) {
            isDispatch = isDispatch || cover.onDispatchPhysicKeyEvent(event)
        }
        return isDispatch
    }

    /**
     * 拦截Back。
     */
    fun dispatchBackPressed(): Boolean {
        var isDispatch = false
        for (transaction in backPressTransactions) {
            isDispatch = isDispatch || transaction.onDispatchBackPressed()
        }
        return isDispatch
    }

    /**
     * 拦截手势按键
     */
    fun dispatchScreenGestureEvent(event: MotionEvent?): Boolean {
        var isDispatch = false
        for (coverGroup in coverGroups) {
            isDispatch = isDispatch || coverGroup.onDispatchScreenGestureEvent(event)
        }
        return isDispatch
    }

    /**
     * 拦截设备方向变更
     */
    fun dispatchDeviceOrientationEvent(orientation: Int) {
        for (coverGroup in coverGroups) {
            coverGroup.onDispatchDeviceOrientationEvent(orientation)
        }
    }

    /**
     * 请求权限结果
     */
    fun dispatchPermissionResult(results: List<PermissionResult>?, isRequestResult: Boolean) {
        for (coverGroup in coverGroups) {
            coverGroup.onDispatchPermissionResult(results, isRequestResult)
        }
    }

    /**
     * Activity跳转结果。
     */
    fun dispatchActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        for (coverGroup in coverGroups) {
            coverGroup.onDispatchActivityResult(requestCode, resultCode, data)
        }
    }


}