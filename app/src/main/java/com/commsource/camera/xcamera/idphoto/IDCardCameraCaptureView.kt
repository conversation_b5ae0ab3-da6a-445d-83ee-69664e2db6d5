package com.commsource.camera.xcamera.idphoto

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.*
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.OvershootInterpolator

import com.commsource.beautymain.widget.gesturewidget.GestureDetectorPro
import com.commsource.beautyplus.R
import com.commsource.camera.mvp.annotation.CameraMode
import com.commsource.camera.mvp.annotation.TakePhotoMode
import com.commsource.camera.util.XAnimator
import com.commsource.camera.util.XAnimatorCalculateValuer
import com.commsource.camera.util.XAnimatorDrawable
import com.commsource.util.ResourcesUtils
import com.commsource.util.TimeLog
import com.meitu.library.util.device.DeviceUtils


/**
 * @Desc : 新的拍照按钮
 * <AUTHOR> Bear - 2020/4/29
 *
 * 绘制渐变以及相机按钮突变均以控件的中心为中心绘制
 * 将canvas中心优先迁移到中心绘制
 *
 */
class IDCardCameraCaptureView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) : View(context, attrs) {

    companion object {
        /**
         * 背景尺寸 70dp 宽
         */
        val BACKGROUND_RADIUS = DeviceUtils.dip2fpx(35f)

        /**
         * 模式的内圆半径
         */
        val MODE_INSET_RADIUS = DeviceUtils.dip2fpx(31f)

        /**
         * 视频录制中半径
         */
        val VIDEO_RECORDING_RADIUS = DeviceUtils.dip2fpx(40f)

        /**
         * 视频progress 背景内圆
         */
        val VIDEO_INNER_RECORDING_RADIUS = DeviceUtils.dip2fpx(37f)

        /**
         * 视频录制全屏情况下的背景内圆
         */
        val VIDEO_IN_RECORDING_BACKGROUND_RADIUS = DeviceUtils.dip2fpx(26f)

        /**
         * 中心图标尺寸
         */
        val CENTER_ICON_SIZE = DeviceUtils.dip2fpx(40f)

        /**
         * 录制视频半径
         */
        val RECORDING_STROKE_WIDTH = DeviceUtils.dip2fpx(3f)

        /**
         * 长按触发录制
         */
        private const val LONG_PRESS_TRIGGER_TIME = 300

        /**
         * 最小录制时长ms
         */
        private const val MIN_RECORDING_TIME = 900

        /**
         * 视频长按600毫秒停止
         */
        const val VIDEO_LONG_PRESS_PAUSE_TIME = 600
    }

    val captureCenterDrawable by lazy {
        XAnimatorDrawable(R.drawable.img_snapid_take_photo)
            .height(CENTER_ICON_SIZE)
            .width(CENTER_ICON_SIZE)
            .alpha(1f)
            .zoom(1f)
            .init()
    }

//    val movieCenterDrawable by lazy {
//        XAnimatorDrawable(R.drawable.selfie_movie_center_capture_icon)
//            .height(CENTER_ICON_SIZE)
//            .width(CENTER_ICON_SIZE)
//            .alpha(0f)
//            .zoom(0f)
//            .init()
//    }
//
//    val videoCenterDrawable by lazy {
//        XAnimatorDrawable(R.drawable.selfie_video_shoot_icon)
//            .height(CENTER_ICON_SIZE)
//            .width(CENTER_ICON_SIZE)
//            .alpha(0f)
//            .zoom(0f)
//            .init()
//    }
//
//    val videoPauseCenterDrawable by lazy {
//        XAnimatorDrawable(R.drawable.selfie_video_center_pause_icon)
//            .height(CENTER_ICON_SIZE)
//            .width(CENTER_ICON_SIZE)
//            .alpha(0f)
//            .zoom(0f)
//            .init()
//    }

    /**
     * 背景渐变色 是从0 - 70dp渐变的范围
     */
    val gradient by lazy {
        LinearGradient(
            BACKGROUND_RADIUS,
            -BACKGROUND_RADIUS,
            -BACKGROUND_RADIUS,
            BACKGROUND_RADIUS,
            intArrayOf(Color.parseColor("#73EC86"),Color.parseColor("#73EC87"),Color.parseColor("#73EC86")),
            floatArrayOf(0.0f, 0.59f, 1.0f),
            Shader.TileMode.CLAMP
        )
    }
    val backgroundPaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            shader = gradient
            style = Paint.Style.FILL_AND_STROKE
        }
    }

    /**
     * 背景矩形
     */
    val backgroundPath = Path()
    val backgroundRectF = RectF().apply {
        set(-BACKGROUND_RADIUS, -BACKGROUND_RADIUS, BACKGROUND_RADIUS, BACKGROUND_RADIUS)
    }

    var isFullScreen = false
    val recordingPath = Path()
    val outProgressValuer = XAnimatorCalculateValuer(BACKGROUND_RADIUS)
    val inProgressValuer = XAnimatorCalculateValuer(BACKGROUND_RADIUS)
    val outRectF = RectF().apply {
        set(-BACKGROUND_RADIUS, -BACKGROUND_RADIUS, BACKGROUND_RADIUS, BACKGROUND_RADIUS)
    }
    val inRectF = RectF().apply {
        set(-BACKGROUND_RADIUS, -BACKGROUND_RADIUS, BACKGROUND_RADIUS, BACKGROUND_RADIUS)
    }
    val recordingPaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = ResourcesUtils.getColor(R.color.black)
        }
    }

    /**
     * 白色背景按钮宽度
     */
    val insetAlphaValuer = XAnimatorCalculateValuer(0.9f)

    /**
     * 白色背景按钮宽度
     */
    val insetRadiusValuer = XAnimatorCalculateValuer(0f)

    /**
     * 按钮内白色背景画笔
     * 1.在模式切换下 拍照模式 直接是红色渐变那种模式 电影模式和视频模式是90白色颜色
     * 2.在录制视频的情况下有个切换的动画 背景白色切换到60白色颜色
     * 3.且全屏模式下中心还有一个白色图标
     */
    val insetBackgroundPaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = ResourcesUtils.getColor(R.color.white)
            alpha = (insetAlphaValuer.value * 255).toInt()
        }
    }

    /**
     * 内嵌矩阵
     */
    val insetRectF = RectF()

    /**
     * 录制视频情况下 全屏的半径
     */
    val videoInRecordingRadiusValuer = XAnimatorCalculateValuer(MODE_INSET_RADIUS)
    val videoInRecordingAlphaValuer = XAnimatorCalculateValuer(0f)
    val videoInRecordingPaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            alpha = 0
            color = ResourcesUtils.getColor(R.color.white)
        }
    }

    /**
     * 圆弧Rect
     */
    private val recordingRectF = RectF()

    /**
     * 是否处于录制状态模式
     */
    var isOnVideoRecordingMode = false

    /**
     * 是否录制暂停
     */
    var canStartRecording = true

    /**
     * 视频切换动画
     *
     * 切换到处于视频录制按钮模式下的动画
     */
    private val startRecordingAnimator = XAnimator.ofFloat(0f, 1f)
        .duration(250)
        .interpolator(OvershootInterpolator(1.0f))
        .setAnimationListener(object : XAnimator.SimpleAnimationListener() {
            override fun onAnimationStart(animation: XAnimator?) {
                super.onAnimationStart(animation)
                if (isOnVideoRecordingMode) {
                    insetRadiusValuer.to(VIDEO_RECORDING_RADIUS)
                    insetAlphaValuer.to(0.6f)
                    videoInRecordingRadiusValuer.to(VIDEO_IN_RECORDING_BACKGROUND_RADIUS)
                    videoInRecordingAlphaValuer.to(1f)
                   // videoPauseCenterDrawable.toZoom(1f)
//                        .toAlpha(1f)
                    captureCenterDrawable.toZoom(0f)
                        .toAlpha(0f)
                 //   videoCenterDrawable.toZoom(0f)
//                        .toAlpha(0f)
                    if (!isFullScreen) {
                        outProgressValuer.to(VIDEO_RECORDING_RADIUS)
                        inProgressValuer.to(VIDEO_INNER_RECORDING_RADIUS)
                    }
                } else {
                    insetAlphaValuer.to(0.9f)
                    //videoPauseCenterDrawable.toZoom(0f)
                    videoInRecordingRadiusValuer.to(MODE_INSET_RADIUS)
                    videoInRecordingAlphaValuer.to(0f)
                    if (cameraMode == CameraMode.CAPTURE_MODE) {
                        insetRadiusValuer.to(0f)
//                        videoCenterDrawable
//                            .toZoom(0f)
//                            .toAlpha(0f)
//                            .zoom(0f)
                        captureCenterDrawable
                            .toZoom(1f)
                            .toAlpha(1f)
                    } else if (cameraMode == CameraMode.VIDEO_MODE) {
                        insetRadiusValuer.to(MODE_INSET_RADIUS)
                        captureCenterDrawable
                            .toZoom(0f)
                            .toAlpha(0f)
                            .zoom(0f)
//                        videoCenterDrawable
//                            .toZoom(1f)
//                            .toAlpha(1f)
                    }
                    if (!isFullScreen) {
                        outProgressValuer.to(BACKGROUND_RADIUS)
                        inProgressValuer.to(BACKGROUND_RADIUS)
                    }
                }
            }

            override fun onAnimationUpdate(fraction: Float, value: Float) {
                super.onAnimationUpdate(fraction, value)

                val insetRadius = insetRadiusValuer.calculateValue(fraction)
                insetRectF.set(-insetRadius, -insetRadius, insetRadius, insetRadius)
                videoInRecordingRadiusValuer.calculateValue(fraction)

                videoInRecordingPaint.alpha =
                    XAnimatorCalculateValuer.limit(videoInRecordingAlphaValuer.calculateValue(fraction) * 255f, 0f, 255f).toInt()
                insetBackgroundPaint.alpha = (insetAlphaValuer.calculateValue(fraction) * 255).toInt()
               // videoPauseCenterDrawable.caculateFraction(fraction)
                captureCenterDrawable.caculateFraction(fraction)
               // videoCenterDrawable.caculateFraction(fraction)
                if (!isFullScreen) {
                    val outProgressRadius = outProgressValuer.calculateValue(fraction)
                    val inProgressRadius = inProgressValuer.calculateValue(fraction)
                    outRectF.set(-outProgressRadius, -outProgressRadius, outProgressRadius, outProgressRadius)
                    inRectF.set(-inProgressRadius, -inProgressRadius, inProgressRadius, inProgressRadius)
                }
                invalidate()
            }
        })

    /**
     * 视频暂停
     * 只有在多段录制情况下会存在暂停
     */
    private val recordingPauseAnimator = XAnimator.ofFloat(0f, 1f)
        .duration(250)
      //  .interpolator(CameraInterpolator())
        .setAnimationListener(object : XAnimator.SimpleAnimationListener() {
            override fun onAnimationStart(animation: XAnimator?) {
                super.onAnimationStart(animation)

            }

            override fun onAnimationUpdate(fraction: Float, value: Float) {
                super.onAnimationUpdate(fraction, value)

                invalidate()
            }
        })

    /**
     * 模式切换的动效
     */
    private val modeAnimator = XAnimator.ofFloat(0f, 1f)
        .duration(250)
        .interpolator(OvershootInterpolator(1.0f))
        .setAnimationListener(object : XAnimator.SimpleAnimationListener() {

            override fun onAnimationStart(animation: XAnimator?) {
                super.onAnimationStart(animation)
                //拍照模式
                when (cameraMode) {
                    CameraMode.CREATE_MONTAGE_MODE,
                    CameraMode.CAPTURE_MODE -> {
                        //内圆到0
                        insetRadiusValuer.to(0f)
                        //判断上个模式的来源 确定图片位移的问题
                        captureCenterDrawable
                            .toAlpha(1f)
                            .toZoom(1f)

                    }

                    CameraMode.VIDEO_MODE -> {
                        insetRadiusValuer.to(MODE_INSET_RADIUS)
                        captureCenterDrawable
                            .toAlpha(0f)
                            .toZoom(0f)

                    }

                    CameraMode.MOVIE_MODE -> {
                        insetRadiusValuer.to(MODE_INSET_RADIUS)
                        captureCenterDrawable
                            .toAlpha(0f)
                            .toZoom(0f)

                    }
                }
            }

            override fun onAnimationUpdate(fraction: Float, value: Float) {
                super.onAnimationUpdate(fraction, value)
                val insetRadius = insetRadiusValuer.calculateValue(fraction)
                insetRectF.set(-insetRadius, -insetRadius, insetRadius, insetRadius)
                captureCenterDrawable.caculateFraction(fraction)

                invalidate()
            }
        })!!

    /**
     * 准备删除的视频段画笔动画
     */
    val deleteAlphaValuer = XAnimatorCalculateValuer(1f, 0.2f)
    val readyDeleteAlphaAnimator = XAnimator.ofFloat(0f, 1f)
        .duration(500)
        .repeatCount(ValueAnimator.INFINITE)
        .reverseMode()
        .setAnimationListener(object : XAnimator.SimpleAnimationListener() {

            override fun onAnimationUpdate(fraction: Float, value: Float) {
                super.onAnimationUpdate(fraction, value)
                readyDeletePaint.alpha = (deleteAlphaValuer.calculateValue(fraction) * 255).toInt()
                invalidate()
            }
        })

    /**
     * 拍照缩放
     */
    private var tempScale = 1.0f
    private val captureScaleAnimator = XAnimator.ofFloat(1.0f, .5f, 1.0f)
        .duration(200)
        .interpolator(AccelerateDecelerateInterpolator())
        .setAnimationListener(object : XAnimator.SimpleAnimationListener() {

            override fun onAnimationStart(animation: XAnimator?) {
                super.onAnimationStart(animation)
                //当前的scale来做动画 因为scale可能被外层获取做动画
                tempScale = scaleX
                isEnabled = false
            }

            override fun onAnimationUpdate(fraction: Float, value: Float) {
                super.onAnimationUpdate(fraction, value)
                val scale = tempScale * value
                scaleX = scale
                scaleY = scale
                invalidate()
            }

            override fun onAnimationEnd(animation: XAnimator?) {
                super.onAnimationEnd(animation)
                isEnabled = true
                callback?.onTakePicture(mTakePhotoMode)
            }
        })

    /**
     * 拍照模式：普通、大头贴、视频
     */
    @CameraMode
    private var cameraMode = CameraMode.CAPTURE_MODE

    /**
     * 手势监听
     */
    private var gestureDetector: GestureDetectorPro? = null

    /**
     * 按钮回调
     */
    private var callback: CameraButtonCallback? = null

    /**
     * 视频录制回调
     */
    var videoRecordListener: VideoRecordListener? = null

    private val readyDeletePaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            style = Paint.Style.STROKE
            color = ResourcesUtils.getColor(R.color.color_73ec86)
            strokeWidth = RECORDING_STROKE_WIDTH
        }
    }

    private val partingLinePaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            style = Paint.Style.FILL_AND_STROKE
            color = ResourcesUtils.getColor(R.color.white)
            strokeWidth = RECORDING_STROKE_WIDTH
        }
    }
    private val recordingProgressPaint: Paint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            style = Paint.Style.STROKE
            color = ResourcesUtils.getColor(R.color.Primary_A)
            strokeWidth = RECORDING_STROKE_WIDTH
        }
    }

    /**
     * 是否支持录制
     */
    private var isVideoEnable = true

    /**
     * 当前机器是否支持录制，相当于是总开关。
     */
    private var isSupportVideo = true

    /**
     * handler
     */
    private val mHandler = Handler(Looper.getMainLooper())

    /**
     * 拍照模式
     */
    @TakePhotoMode
    private var mTakePhotoMode = TakePhotoMode.NO_MODE



    /**
     * 布局宽高
     */
    private var mWidth = 0
    private var mHeight = 0

    /**
     * 手指按下时间，用于判断视频模式长按抬起是否要停止。
     */
    private var fingerDownTime: Long = 0

    /**
     * 录制视频记录。
     */
    private val recordTimeLog = TimeLog.create()

    /*******************************************对外接口start */
    /**
     * 判断当前是否准备好删除视频段
     *
     * @return
     */
    var isInMultiVideoDelete = false
    fun confirmDeleteVideoSession(): Boolean {
        if (!isInMultiVideoDelete) {
            isInMultiVideoDelete = true
            readyDeleteAlphaAnimator.cancel()
            readyDeleteAlphaAnimator.start()
            return false
        }
        return true
    }




    /**
     * 是否切换到处于视频录制状态
     */
    fun onVideoRecording(toVideoRecording: Boolean) {
        if (this.isOnVideoRecordingMode == toVideoRecording) {
            return
        }
        this.isOnVideoRecordingMode = toVideoRecording
        startRecordingAnimator.cancel()
        startRecordingAnimator.start()
    }

    /**
     * Activity在onPause需要暂停录制。
     */
    fun onPause() {
        if (captureScaleAnimator.isRunning) {
            captureScaleAnimator.cancel()
            scaleX = 1f
            scaleY = 1f
        }
        mHandler.removeCallbacksAndMessages(null)
        if (isOnVideoRecordingMode) {
            videoRecordListener?.onStopRecord(false, true)
            stopRecording()
        }
    }

    /**
     * 是否有录制好的视频段。
     *
     * @return
     */
    fun hasVideoSession(): Boolean {
        return false
    }

    /**
     * 设置当前是否可触发录制视频
     *
     * @param isSupportVideo
     */
    fun setVideoEnable(isSupportVideo: Boolean) {
        isVideoEnable = isSupportVideo
    }

    /**
     * 设置当前机器是否支持录制
     *
     * @param supportVideo
     */
    fun setSupportVideo(supportVideo: Boolean) {
        isSupportVideo = supportVideo
    }

    private fun canRecordVideo(): Boolean {
        return isVideoEnable && isSupportVideo
    }

    /**
     * 设置当前的相机模式
     *
     * @param curMode
     * @param lastMode
     */
    fun setCameraMode(@CameraMode curMode: Int) {
        if (cameraMode != curMode) {
            cameraMode = curMode
            modeAnimator.cancel()
            modeAnimator.start()
        }
    }

    /**
     * 是否录制满了。
     *
     * @return
     */
    private fun hasRecordMax(): Boolean {
        return false
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        // 默认宽高一致
        mWidth = w
        mHeight = h
        recordingRectF.set(-VIDEO_RECORDING_RADIUS, -VIDEO_RECORDING_RADIUS, VIDEO_RECORDING_RADIUS, VIDEO_RECORDING_RADIUS)
        recordingRectF.inset(RECORDING_STROKE_WIDTH / 2f, RECORDING_STROKE_WIDTH / 2f)
    }

    private fun init(context: Context) {
        gestureDetector = GestureDetectorPro(context, CameraGestureDetectorListener())
    }

    override fun onDraw(canvas: Canvas) {
        //1.绘制透明度背景
        //2.绘制按钮的基本简便红色背景
        //3.绘制中心按钮
        //4.绘制全屏的白色中心按钮
        //5.绘制基本的拍照图标
        //6.绘制基本的视频图标
        //7.绘制基本的电影模式图标
        //7.绘制基本的视频停止图标
        //8.绘制基本的视频
        //9.绘制视频分段进度
        //绘制背景
        //移动到中心点
        canvas.save()
        canvas.translate(mWidth / 2f, mHeight / 2f)
        //绘制外渐变圆背景
        if (insetRadiusValuer.value <= BACKGROUND_RADIUS) {
            backgroundPath.reset()
            backgroundPath.addOval(backgroundRectF, Path.Direction.CW)
            backgroundPath.addOval(insetRectF, Path.Direction.CCW)
            canvas.drawPath(backgroundPath, backgroundPaint)
        }
        //绘制内圆
        if (insetRadiusValuer.value > 0f) {
            canvas.drawCircle(0f, 0f, insetRadiusValuer.value, insetBackgroundPaint)
        }
        //绘制视频录制中 非全屏下的背景
        if (!isFullScreen) {
            recordingPath.reset()
            recordingPath.addOval(outRectF, Path.Direction.CW)
            recordingPath.addOval(inRectF, Path.Direction.CCW)
            canvas.drawPath(recordingPath, recordingPaint)
        }
        //视频录制中心白色
        if (videoInRecordingAlphaValuer.value > 0f && videoInRecordingRadiusValuer.value > 0f) {
            canvas.drawCircle(0f, 0f, videoInRecordingRadiusValuer.value, videoInRecordingPaint)
        }
        //动画图标绘制
        captureCenterDrawable.draw(canvas)


        canvas.restore()
    }

    /************************************************手势触发部分start */
    interface CameraButtonCallback {
        /**
         * onTakePicture
         *
         * @param mode mode
         */
        fun onTakePicture(@TakePhotoMode mode: Int)
    }

    interface VideoRecordListener {
        /**
         * onStartRecord
         */
        fun onStartRecord()

        /**
         * onStopRecord
         *
         * @param isMax    isMax
         * @param needStop needStop
         * @return
         */
        fun onStopRecord(isMax: Boolean, needStop: Boolean): Boolean
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        return gestureDetector!!.onTouchEvent(event)
    }

    internal inner class CameraGestureDetectorListener : GestureDetectorPro.SimpleOnGestureListener() {
        override fun onMajorFingerDown(downEvent: MotionEvent): Boolean {
            if (!modeAnimator.isRunning) {
                onFingerDown(TakePhotoMode.TAKE_PHOTO_MODE_BTN)
            }
            return super.onMajorFingerDown(downEvent)
        }

        override fun onMajorFingerUp(upEvent: MotionEvent): Boolean {
            if (!modeAnimator.isRunning) {
                onFingerUp(TakePhotoMode.TAKE_PHOTO_MODE_BTN)
            }
            return super.onMajorFingerUp(upEvent)
        }
    }

    /**
     * 开始录制
     */
    private val startRecordingRunnable: Runnable = object : Runnable {
        override fun run() {
            // 开启录制时移出所有runnable
            mHandler.removeCallbacks(this)
            recordTimeLog.update()
            canStartRecording = false
            //如果已经处于录制放大的模式下 需要切换pause动画 这时候的draw延时一下
            if (isOnVideoRecordingMode) {
                recordingPauseAnimator.cancel()
                recordingPauseAnimator.start()
            }
            onVideoRecording(true)
            if (videoRecordListener != null) {
                //BugFix：如果在要开始录制时 存在正在确认删除的片段 需要取消选中
                if (isInMultiVideoDelete) {
                    isInMultiVideoDelete = false
                    readyDeleteAlphaAnimator.cancel()
                }
                videoRecordListener!!.onStartRecord()
            }
        }
    }

    /**
     * 主动停止
     */
    private fun stopRecording() {
        mHandler.removeCallbacksAndMessages(null)
        if (recordTimeLog.get() < MIN_RECORDING_TIME) {
            mHandler.postDelayed({
                // 再次按下停止。
                if (videoRecordListener != null) {
                    canStartRecording = true
                    videoRecordListener!!.onStopRecord(false, true)
                }
                recordingPauseAnimator.cancel()
                recordingPauseAnimator.start()
            }, MIN_RECORDING_TIME - recordTimeLog.get())
        } else {
            // 再次按下停止。
            if (videoRecordListener != null) {
                canStartRecording = true
                videoRecordListener!!.onStopRecord(false, true)
            }
            recordingPauseAnimator.cancel()
            recordingPauseAnimator.start()
        }
    }

    /**
     * 手指按下
     */
    private fun onFingerDown(@TakePhotoMode mode: Int) {
        if (!isEnabled) {
            return
        }
        if (mTakePhotoMode == TakePhotoMode.NO_MODE || mode == mTakePhotoMode) {
            // 不同手势不操作。
            mTakePhotoMode = mode
            when (cameraMode) {
                CameraMode.CAPTURE_MODE -> {
                    if (canRecordVideo() && !isOnVideoRecordingMode && cameraMode == CameraMode.CAPTURE_MODE) {
                        mHandler.postDelayed(startRecordingRunnable, LONG_PRESS_TRIGGER_TIME.toLong())
                    }
                }

                CameraMode.VIDEO_MODE -> {
                    // 视频模式
                    if (!canRecordVideo()) {
                        return
                    }
                    // 视频录制满了
                    if (hasRecordMax()) {
                        videoRecordListener?.onStopRecord(true, false)
                        return
                    }
                    fingerDownTime = System.currentTimeMillis() // 记录按下时间。
                    // 视频模式
                    if (canStartRecording) {
                        startRecordingRunnable.run()
                    } else {
                        stopRecording()
                    }
                }
            }
        }
    }

    /**
     * 手指抬起
     */
    private fun onFingerUp(@TakePhotoMode mode: Int) {
        if (!isEnabled) {
            mTakePhotoMode = TakePhotoMode.NO_MODE
            return
        }
        if (mode == mTakePhotoMode) {
            when (cameraMode) {
                CameraMode.CAPTURE_MODE -> {
                    mHandler.removeCallbacksAndMessages(null)
                    //如果已经开启了录制 其实就是captureMode
                    if (isOnVideoRecordingMode) {
                        stopRecording()
                    } else if (!hasVideoSession()) {
                        captureScaleAnimator.cancel()
                        captureScaleAnimator.start()
                    }
                }

                CameraMode.MOVIE_MODE -> {
                    captureScaleAnimator.cancel()
                    captureScaleAnimator.start()
                }

                CameraMode.CREATE_MONTAGE_MODE -> {
                    captureScaleAnimator.cancel()
                    captureScaleAnimator.start()
                }

                CameraMode.VIDEO_MODE -> {
                    if (!canRecordVideo() || hasRecordMax()) {
                        return
                    }
                    // 视频模式 长按超过600毫秒停止
                    if (System.currentTimeMillis() - fingerDownTime > VIDEO_LONG_PRESS_PAUSE_TIME) {
                        stopRecording()
                    }
                }
            }
            mTakePhotoMode = TakePhotoMode.NO_MODE
            invalidate()
        }
    }

    /**
     * 因为音量键的按下手势会一直传下去，这边需要判断是否有抬起。
     */
    private var mPhoneKeyUp = true

    /**
     * 按音量键时调用
     */
    fun onPhoneKeyEvent(event: KeyEvent) {
        when (event.action) {
            KeyEvent.ACTION_DOWN -> {
                if (cameraMode == CameraMode.VIDEO_MODE && !mPhoneKeyUp) {
                    return
                }
                mPhoneKeyUp = false
                onFingerDown(TakePhotoMode.TAKE_PHOTO_MODE_VOLUME)
            }

            KeyEvent.ACTION_UP -> {
                mPhoneKeyUp = true
                onFingerUp(TakePhotoMode.TAKE_PHOTO_MODE_VOLUME)
            }

            else -> {
            }
        }
    }

    fun onScreenEvent(event: MotionEvent) {
        when (event.action) {
            KeyEvent.ACTION_DOWN -> onFingerDown(TakePhotoMode.TAKE_PHOTO_MODE_SCREEN)
            KeyEvent.ACTION_UP -> onFingerUp(TakePhotoMode.TAKE_PHOTO_MODE_SCREEN)
            else -> {
            }
        }
    }

    fun setCallback(callback: CameraButtonCallback) {
        this.callback = callback
    }

    /**
     * 重置
     */
    fun reset() {
        isInMultiVideoDelete = false
        mTakePhotoMode = TakePhotoMode.NO_MODE
       // mVideoSessions = null
        mHandler.removeCallbacks(startRecordingRunnable)
        if (isOnVideoRecordingMode) {
            onVideoRecording(false)
        } else {
            invalidate()
        }
    }

    /**
     * 暂停视频录制
     */
    fun pauseVideoRecording() {
        if (hasVideoSession()) {
            stopRecording()
        } else {
            onVideoRecording(false)
        }
    }

    /************************************************手势触发部分end */
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        readyDeleteAlphaAnimator.cancel()
        recordingPauseAnimator.cancel()
        modeAnimator.cancel()
        startRecordingAnimator.cancel()
    }

    /*******************************************对外接口end */
    init {
        init(context)
    }
}