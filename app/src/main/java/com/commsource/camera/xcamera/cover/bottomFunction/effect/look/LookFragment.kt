package com.commsource.camera.xcamera.cover.bottomFunction.effect.look

import android.animation.Animator
import android.animation.AnimatorL<PERSON>enerAdapter
import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelProviders
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentLookBinding
import com.commsource.beautyplus.face.Gender
import com.commsource.camera.xcamera.BpCameraViewModel
import com.commsource.camera.xcamera.cover.CameraCaptureViewModel
import com.commsource.camera.xcamera.cover.CameraConfigViewModel
import com.commsource.camera.xcamera.cover.bottomFunction.BaseBottomSubFragment
import com.commsource.camera.xcamera.cover.bottomFunction.BottomFunction
import com.commsource.camera.xcamera.cover.bottomFunction.BottomFunctionViewModel
import com.commsource.camera.xcamera.cover.bottomFunction.BottomInInterpolator
import com.commsource.camera.xcamera.cover.bottomFunction.BottomOutInterpolator
import com.commsource.camera.xcamera.cover.bottomFunction.ar.CameraArViewModel
import com.commsource.camera.xcamera.cover.bottomFunction.effect.EffectFunctionViewModel
import com.commsource.camera.xcamera.cover.bottomFunction.effect.suspend.EffectSuspendFunction
import com.commsource.camera.xcamera.cover.tips.TipsViewModel
import com.commsource.repository.LoadState
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.studio.DecorateConstant
import com.commsource.util.ErrorNotifier
import com.commsource.util.RTLTool
import com.commsource.util.ResourcesUtils
import com.commsource.util.ThreadExecutor
import com.commsource.util.UIHelper
import com.commsource.util.ViewShowState
import com.commsource.util.ViewUtils
import com.commsource.util.common.BaseCallback2
import com.commsource.util.common.ProcessUtil
import com.commsource.util.gone
import com.commsource.util.isVisible
import com.commsource.util.print
import com.commsource.util.setMarginBottom
import com.commsource.util.traverseShowViewHolder
import com.commsource.util.visible
import com.commsource.widget.FastCenterScrollLayoutManager
import com.commsource.widget.dialog.common.ADialog
import com.commsource.widget.mask.DataMask
import com.commsource.widget.mask.MaskType
import com.commsource.widget.recyclerview.AdapterDataBuilder
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.meitu.common.AppContext
import com.meitu.http.net.NetworkSpeedMonitor
import com.meitu.library.util.device.DeviceUtils
import com.meitu.library.util.net.NetUtils
import com.meitu.template.bean.LookMaterial
import com.pixocial.camerasuite.commsource.camera.mvp.annotation.CameraRatioType
import kotlinx.coroutines.launch
import kotlin.collections.set

/**
 * 相机-风格
 * <AUTHOR> Bear - 2020/5/6
 */
class LookFragment : BaseBottomSubFragment() {

    val bottomFunctionViewModel by lazy {
        ViewModelProviders.of(mActivity as FragmentActivity)
            .get(BottomFunctionViewModel::class.java)
    }

    val lookViewModel by lazy {
        ViewModelProviders.of(mActivity as FragmentActivity).get(LookViewModel::class.java)
    }

    val effectFunctionViewModel by lazy {
        ViewModelProviders.of(mActivity as FragmentActivity)
            .get(EffectFunctionViewModel::class.java)
    }

    val bpCameraViewModel by lazy {
        ViewModelProviders.of(mActivity as FragmentActivity).get(BpCameraViewModel::class.java)
    }

    private val cameraArViewModel by lazy {
        ViewModelProvider(mActivity as FragmentActivity).get(CameraArViewModel::class.java)
    }

    val cameraCaptureViewModel by lazy {
        ViewModelProviders.of(mActivity as FragmentActivity).get(CameraCaptureViewModel::class.java)
    }

    val tipsViewModel by lazy {
        ViewModelProviders.of(mActivity as FragmentActivity).get(TipsViewModel::class.java)
    }

    val cameraConfigViewModel by lazy {
        ViewModelProviders.of(mActivity as FragmentActivity).get(CameraConfigViewModel::class.java)
    }

    val itemDecoration by lazy { LookItemDecoration() }

    val lookLayoutManager: FastCenterScrollLayoutManager by lazy {
        FastCenterScrollLayoutManager(
            context,
            LinearLayoutManager.HORIZONTAL,
            false
        )
    }

    val mAdapter: BaseRecyclerViewAdapter by lazy { BaseRecyclerViewAdapter(context) }
    val lookCatAdapter: BaseRecyclerViewAdapter by lazy { BaseRecyclerViewAdapter(context) }

    val titleDecoration by lazy { LookTitleItemDecoration() }

    val mViewBinding: FragmentLookBinding by lazy {
        DataBindingUtil.inflate<FragmentLookBinding>(
            layoutInflater,
            R.layout.fragment_look,
            null,
            false
        )
    }

    val reset: String = ResourcesUtils.getString(R.string.customization)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return mViewBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
    }

    override fun onSupportInvisible() {
        super.onSupportInvisible()
        lookViewModel.isFirEnterLook = false
        lookViewModel.resetAutoApplyLookMaterial()
        if (!fragmentSupportVisibleHelper.isFromPause) {
            lookViewModel.lookVisibleArray.clear()
        }
    }

    private fun initView() {
        val isFullScreen =
            cameraConfigViewModel.isFullScreen() || cameraConfigViewModel.is916CameraRatio()
        mViewBinding.maskContainer.maskContainerHelper.newBuilder()
            .addMaskGroup(DataMask.getShortPanelGroup(if (isFullScreen) DataMask.DataMaskTheme.CameraDark() else DataMask.DataMaskTheme.CameraLight()))
            .bindView(MaskType.NetError, R.id.tv_action) {
                if (ProcessUtil.isProcessing()) {
                    return@bindView
                }
                if (!NetUtils.canNetworking()) {
                    return@bindView
                }

                // 加载数据
                LookRepo.checkRelink()
            }.build()

        mViewBinding.rvLook.layoutManager = lookLayoutManager
        mViewBinding.rvLook.adapter = mAdapter
        mAdapter.addTag("Style", true)
        lookViewModel.isFirEnterLook = true
        mViewBinding.rvLook.addItemDecoration(itemDecoration)
        mViewBinding.rvLook.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            var isScrollByUser = false
            var curFirPos = 0
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                    isScrollByUser = true
                }
                if (isScrollByUser && newState == RecyclerView.SCROLL_STATE_IDLE) {
                    isScrollByUser = false
                }
            }

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val firPos = lookLayoutManager.findFirstVisibleItemPosition()
                if (isScrollByUser && firPos != curFirPos) {
                    curFirPos = firPos
                    lookViewModel.selectLookCatByLookPos(firPos)
                }
                checkLookVisible()
            }
        })

        mViewBinding.rvLookCat.apply {
            addItemDecoration(titleDecoration)
            layoutManager =
                FastCenterScrollLayoutManager(
                    context,
                    LinearLayoutManager.HORIZONTAL,
                    false
                )
            adapter = lookCatAdapter
        }

        //底部适配
        bottomFunctionViewModel.bottomSizeParamEvent.observe(viewLifecycleOwner, Observer {
            it?.let {
                ViewUtils.setHeight(mViewBinding.flEffectContent, it.mEffectBarHeight)
                mViewBinding.maskContainer.setMarginBottom(it.panelSafeBottom)
                ViewUtils.setHeight(mViewBinding.rvLook, it.mEffectContentHeight)
                ViewUtils.setHeight(mViewBinding.clMaskContent, it.mEffectContentHeight)
            }
        })

        mViewBinding.tvTips.setText(R.string.look_ar_use_tips)
        mViewBinding.tvRemoveAr.setOnClickListener { v ->
            // 展示统计
            MTAnalyticsAgent.logEvent(
                MTAnalyticsConstant.SELFIE_CANCEL_AR,
                HashMap<String, String>(4).apply {
                    put("模式", cameraCaptureViewModel.getModeStatisticString())
                    put("状态", "展示")
                    put("时机", "Look")
                })
            ADialog.Builder()
                .setContent(ResourcesUtils.getString(R.string.sure_to_cancel_ar))
                .setPositiveText(ResourcesUtils.getString(R.string.dialog_confirm))
                .setNegativeText(ResourcesUtils.getString(R.string.cancel))
                .setCancelable(true)
                .setPositiveClick {
                    cameraArViewModel.cancelArEffect()
                    it.dismissAllowingStateLoss()
                    if (lookViewModel.hasApplyLookMaterial()) {
                        effectFunctionViewModel.showSuspend(EffectSuspendFunction.Look)
                    } else {
                        effectFunctionViewModel.showSuspend(null)
                        if (isNeedLocateOnMale()) {
                            lookViewModel.dataEvent.value?.indexOfFirst {
                                it.categoryId != DecorateConstant.NEW_CATEGORY_RECOMMEND
                                        && it.lookMaterials.find { it.sex == 1 } != null
                            }?.let {
                                lookViewModel.selectLookCategory(it, true)
                            }
                        }
                    }

                    checkLookVisible()
                    // 点击取消统计。
                    MTAnalyticsAgent.logEvent(
                        MTAnalyticsConstant.SELFIE_CANCEL_AR,
                        HashMap<String, String>(4).apply {
                            put("模式", cameraCaptureViewModel.getModeStatisticString())
                            put("状态", "点击确认")
                            put("时机", "Look")
                        })
                }
                .setNegativeClick {
                    it.dismissAllowingStateLoss()
                    // 点击取消统计。
                    MTAnalyticsAgent.logEvent(
                        MTAnalyticsConstant.SELFIE_CANCEL_AR,
                        HashMap<String, String>(4).apply {
                            put("模式", cameraCaptureViewModel.getModeStatisticString())
                            put("状态", "点击取消")
                            put("时机", "Look")
                        })
                }
                .build()
                .show()
        }

        //数据监听
        lookViewModel.dataEvent.observe(viewLifecycleOwner, Observer {
            it?.let {
                //title计算
                titleDecoration.calculateGroups(it)
                // 分类数据
                lookCatAdapter?.setSingleItemEntities(it, LookCatViewHolder::class.java)
                // 列表数据
                val looks: List<LookMaterial> = it.flatMap { it.lookMaterials }
                mAdapter.updateItemEntities(
                    AdapterDataBuilder.create()
                        .addEntities(looks, LookViewHolder::class.java)
                        .build()
                )
                // 分割符
                setGroupIndicatorIfNeed(it)
                //默认选中
                mViewBinding.rvLook.post {
                    val curApplyLook = lookViewModel.applyLookEvent.value
                    mAdapter.currentSelectEntity =
                        if (curApplyLook?.isDefaultLook == false) {
                            curApplyLook
                        } else {
                            reset
                        }
                    var isNeedTrigger = false
                    val targetPos = when {
                        lookViewModel.protocolLookCategoryId != null -> {
                            val category =
                                it.find { it.categoryId == lookViewModel.protocolLookCategoryId }
                            //单次判断使用完
                            lookViewModel.protocolLookCategoryId = null
                            if (category == null) {
                                0
                            } else {
                                isNeedTrigger = true
                                it.indexOf(category)
                            }
                        }

                        curApplyLook?.isDefaultLook == false -> {
                            it.indexOfFirst { it.categoryId == curApplyLook.categoryId }
                        }
                        !cameraArViewModel.hasApplyAr()
                                && !lookViewModel.hasLookProtocol()
                                && !lookViewModel.hasApplyLookMaterial()
                                && bpCameraViewModel.cameraParams.gender == Gender.Male -> {
                            isNeedTrigger = true
                            it.indexOfFirst {
                                it.categoryId != DecorateConstant.NEW_CATEGORY_RECOMMEND
                                        && it.lookMaterials.find { it.sex == 1 } != null
                            }
                        }

                        else -> {
                            0
                        }
                    }
                    lookViewModel.selectLookCategory(
                        if (targetPos >= 0) targetPos else 0,
                        isNeedTrigger
                    )
                }
            }
        })
        lookViewModel.maskTypeEvent.observe(viewLifecycleOwner) {
            if (it.isNullOrEmpty()) {
                mViewBinding.lookContainer.alpha = 1.0f
                mViewBinding.maskContainer.hideAll()
                mViewBinding.maskContainer.gone()
            } else {
                mViewBinding.maskContainer.showMask(it)
                mViewBinding.lookContainer.alpha = 0f
            }
        }
        LookRepo.loadStateEvent.observe(
            viewLifecycleOwner,
            object : NoStickLiveData.CustomObserver<Int?>() {
                override fun onAccept(it: Int?) {
                    if (!lookViewModel.hasOnlineLookMaterials) {
                        if (it == LoadState.LOADING) {
                            lookViewModel.maskTypeEvent.value = MaskType.Loading
                        } else if (it != LoadState.SUCCEED) {
                            lookViewModel.maskTypeEvent.value = MaskType.NetError
                        }
                    }
                }
            }
        )
        LookRepo.downloadObserver.startEvent.observe(viewLifecycleOwner) {
            it?.let {
                refreshItem(it)
                lookViewModel.logOnDownload(
                    it,
                    cameraCaptureViewModel.cameraCaptureModeEvent.value,
                    true
                )
            }
        }
        LookRepo.downloadObserver.progressChangeEvent.observe(viewLifecycleOwner) { refreshItem(it) }
        LookRepo.downloadObserver.successEvent.observe(viewLifecycleOwner) {
            it?.let {
                refreshItem(it)
                lookViewModel.logOnDownload(
                    it,
                    cameraCaptureViewModel.cameraCaptureModeEvent.value,
                    false
                )
            }
        }
        LookRepo.downloadObserver.failedEvent.observe(viewLifecycleOwner) {
            refreshItem(it)
        }
        LookRepo.downloadObserver.networkErrorEvent.observe(viewLifecycleOwner) {
            if (isResumed) {
                ErrorNotifier.showNetworkErrorToast()
            }
        }
        LookRepo.downloadObserver.serverErrorEvent.observe(viewLifecycleOwner) {
            if (isResumed) {
                ErrorNotifier.showServerErrorToast()
            }
        }

        //屏幕比例切换
        cameraConfigViewModel.screenRatioChangeEvent.observe(viewLifecycleOwner, Observer {
            when (it) {
                CameraRatioType.PICTURE_RATIO_FULL, CameraRatioType.PICTURE_RATIO_9_16 -> {
                    mAdapter.addTag("Style", true)
                    mAdapter.notifyAllItemChange()
                    lookCatAdapter.notifyAllItemChange()
                    mViewBinding.lineSelect.setIndicatorColor(Color.WHITE)
                    mViewBinding.flMask.setBackgroundColor(0xd9222222.toInt())
                    mViewBinding.tvTips.setTextColor(Color.WHITE)
                    mViewBinding.dashLine.setBackgroundColor(ResourcesUtils.getColor(R.color.color_1affffff))
                    mViewBinding.flEffectContent.delegate.backgroundColor =
                        ResourcesUtils.getColor(R.color.black70)
                    mViewBinding.flEffectContent.elevation = 0f
                    itemDecoration.paint.color = 0x4DFFFFFF
                    mViewBinding.icNone.setTextColor(ResourcesUtils.getColor(R.color.white))

                    mViewBinding.maskContainer.notifyAllChangeByPayload(DataMask.DataMaskTheme.CameraDark())
                }

                else -> {
                    mAdapter.addTag("Style", false)
                    mAdapter.notifyAllItemChange()
                    lookCatAdapter.notifyAllItemChange()
                    mViewBinding.lineSelect.setIndicatorColor(0xff333333.toInt())
                    mViewBinding.dashLine.setBackgroundColor(ResourcesUtils.getColor(R.color.color_10000000))
                    mViewBinding.flMask.setBackgroundColor(0xccffffff.toInt())
                    mViewBinding.tvTips.setTextColor(0xff333333.toInt())

                    mViewBinding.flEffectContent.delegate.backgroundColor =
                        ResourcesUtils.getColor(R.color.white)
                    mViewBinding.flEffectContent.elevation = DeviceUtils.dip2fpx(8f)
                    itemDecoration.paint.color = 0x33000000
                    mViewBinding.icNone.setTextColor(ResourcesUtils.getColor(R.color.black))

                    mViewBinding.maskContainer.notifyAllChangeByPayload(DataMask.DataMaskTheme.CameraLight())
                }
            }
        })

        cameraArViewModel.applyArEvent.observe(viewLifecycleOwner) {
            if (it == null) {
                mViewBinding.flMask.gone()
            } else {
                mViewBinding.flMask.visible()
                effectFunctionViewModel.showSuspend(null)
            }
        }

        lookViewModel.applyLookEvent.observe(viewLifecycleOwner, Observer {
            //没有选中look 默认选中第一位的None
            if (it != null) {
                if (it.isDefaultLook) {
                    mAdapter.currentSelectEntity = reset
                } else {
                    mAdapter.currentSelectEntity = it
                }
            }
            //如果选中look 显示对应
            if (bottomFunctionViewModel.isShow(BottomFunction.LOOK) && !cameraArViewModel.hasApplyAr()) {
                if (lookViewModel.hasApplyLookMaterial()) {
                    effectFunctionViewModel.showSuspend(EffectSuspendFunction.Look)
                } else {
                    effectFunctionViewModel.showSuspend(null)
                }
            }

            UIHelper.runOnIdleTiming(ownerActivity) {
                val position = mAdapter.getEntityPosition(it)
                if (position > 0) {
                    //选中分类
                    lookViewModel.selectLookCatByLookPos(position)
                    //滚动到中心
                    mViewBinding.rvLook.stopScroll()
                    lookLayoutManager.snapPreference = FastCenterScrollLayoutManager.Snap.CENTER
                    mViewBinding.rvLook.smoothScrollToPosition(position)
                }
            }
            lookViewModel.logLookMaterialApply(
                it,
                cameraCaptureViewModel.cameraCaptureModeEvent.value
            )
        })


        // Look选中
        lookViewModel.lookSelectEvent.observe(viewLifecycleOwner, Observer {
            ">>>>>${mViewBinding.rvLook.width}".print("yyp")
            lookLayoutManager.snapPreference = if (RTLTool.isLayoutRtl()) {
                FastCenterScrollLayoutManager.Snap.RIGHT
            } else {
                FastCenterScrollLayoutManager.Snap.LEFT
            }
            mViewBinding.rvLook.smoothScrollToPosition(it)
        })

        // 类别选中
        lookViewModel.lookCatSelectEvent.observe(viewLifecycleOwner, Observer {
            mViewBinding.lineSelect.setSelectIndex(it)
            lookCatAdapter?.setCurrentSelectPosition(it)
            mViewBinding.rvLookCat.smoothScrollToPosition(it)
        })

        lookCatAdapter?.setOnEntityClickListener(LookCategory::class.java) { position, entity ->
            lookViewModel.selectLookCategory(position, true)
            true
        }

        //点击某个素材
        mAdapter.setOnEntityClickListener(LookMaterial::class.java) { position, entity ->
            lookViewModel.logLookMaterialClick(
                entity,
                cameraCaptureViewModel.cameraCaptureModeEvent.value
            )
            if (lookViewModel.isApplyLookMaterial(entity)) {
                return@setOnEntityClickListener true
            }
            if (entity.isDownloading()) {
                return@setOnEntityClickListener true
            }
            if (!entity.isDownload()) {
                if (!NetUtils.canNetworking(AppContext.context)) {
                    ErrorNotifier.showNetworkErrorToast()
                    return@setOnEntityClickListener true
                }
                //请求下载
                NetworkSpeedMonitor.needDownloadTips()
                lookViewModel.requestLookDownload(lookMaterial = entity)
            } else {
                // 提示
                if (!entity.isDefaultLook && !TextUtils.isEmpty(entity.title)) {
                    if (entity.isNeedShowTime()) {
                        tipsViewModel.showMultiFloatingTips(
                            entity.title,
                            "- ${ResourcesUtils.getString(R.string.time_limit)} -",
                            isLeftToRight = position < mAdapter.currentPosition
                        )
                    } else {
                        tipsViewModel.showFloatingTips(
                            entity.title!!,
                            isLeftToRight = position < mAdapter.currentPosition
                        )
                    }
                }
                //如果已经下载 套用已选中
                lookViewModel.applyLookMaterial(entity)
            }
            return@setOnEntityClickListener true
        }

        mViewBinding.vNone.setOnClickListener {
            setDefaultLook()
        }

        //取消套用
//        mAdapter.setOnEntityClickListener({ position, entity ->
//            lookViewModel.applyLookEvent.value?.takeIf { !it.isDefaultLook }?.let {
//                tipsViewModel.showFloatingTips(reset, isLeftToRight = true)
//                lookViewModel.reset()
//                lookViewModel.logLookMaterialClick(
//                    lookViewModel.createDefaultLookEvent.value,
//                    cameraCaptureViewModel.cameraCaptureModeEvent.value
//                )
//            }
//            return@setOnEntityClickListener false
//        }, String::class.java)
    }

    //BugFix:修复协议条状 滑动的位置被可见时的重置为中心滚动
    private var isFirstVisible = true

    //将风格替换为默认风格
    private fun setDefaultLook() {
        lookViewModel.applyLookEvent.value?.takeIf { !it.isDefaultLook }?.let {
            tipsViewModel.showFloatingTips(reset, isLeftToRight = true)
            lookViewModel.reset()
            lookViewModel.logLookMaterialClick(
                lookViewModel.createDefaultLookEvent.value,
                cameraCaptureViewModel.cameraCaptureModeEvent.value
            )
        }
    }

    override fun onSupportVisible() {
        super.onSupportVisible()
        if (isFirstVisible) {
            isFirstVisible = false
            return
        }
        //第一个look无 选中不自动滚动
        if (mAdapter.currentPosition > 0 && mAdapter.currentPosition < mAdapter.itemCount) {
            lookLayoutManager.snapPreference = FastCenterScrollLayoutManager.Snap.CENTER
            mViewBinding.rvLook.smoothScrollToPosition(mAdapter.currentPosition)
            lookViewModel.selectLookCatByLookPos(mAdapter.currentPosition)
        }
        checkLookVisible()
    }

    /**
     * 刷新item
     */
    private fun refreshItem(lookMaterial: LookMaterial?) {
        lookViewModel.dataEvent.value?.let {
            lookMaterial?.let { lookViewModel.synDownloadStateIfLookSame(it) }
            mAdapter.items.filter {
                val entity = it.entity
                entity is LookMaterial && entity.onlineId == lookMaterial?.onlineId
            }.forEach {
                mAdapter.notifyItemChanged(it.entity)
            }
        }
    }

    private fun checkLookVisible() {
        if (mViewBinding.flMask.isVisible) {
            return
        }
        mViewBinding.rvLook.traverseShowViewHolder(false, callback2 = visibleCallback)
    }

    private val visibleCallback: BaseCallback2<Int, RecyclerView.ViewHolder> =
        BaseCallback2 { integer, viewHolder ->
            if (viewHolder is LookViewHolder && integer == ViewShowState.SHOW_COMPLETE) {
                viewHolder.item?.entity?.let {
                    if (lookViewModel.isLookFirstVisible(it)) {
                        ThreadExecutor.executeFastTask("look visibleCallback") {
                            val map = HashMap<String, String>()
                            map["Look素材ID"] = it.onlineId
                            it.belongCatId?.let {
                                map["lok_material_tag"] = it
                            }
                            when {
                                cameraCaptureViewModel.isMovieMode() -> MTAnalyticsConstant.movie_look_imp
                                cameraCaptureViewModel.isVideoMode() -> MTAnalyticsConstant.selfievideo_look_imp
                                else -> MTAnalyticsConstant.selfie_look_imp
                            }.let {
                                MTAnalyticsAgent.logEvent(it, map)
                            }
                        }
                    }
                }
            }
        }

    override fun animateIn(action: Function0<Unit>) {
        super.animateIn(action)
        mViewBinding.flEffectContent.animate().setListener(null).cancel()
        mViewBinding.flEffectContent.translationY =
            bottomFunctionViewModel.bottomSizeParam.mEffectBarHeight.toFloat()
        mViewBinding.root.post {
            mViewBinding.flEffectContent.animate().translationY(0f)
                .withLayer()
                .setInterpolator(BottomInInterpolator())
                .setDuration(BottomFunction.BOTTOM_DURATION)
                .setListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        checkLookVisible()
                    }
                })
                .start()
        }
    }

    override fun animateOut(action: Function0<Unit>) {
        mViewBinding.flEffectContent.animate().setListener(null).cancel()
        mViewBinding.flEffectContent.post {
            mViewBinding.flEffectContent.animate().setDuration(BottomFunction.BOTTOM_DURATION)
                .withLayer()
                .setInterpolator(BottomOutInterpolator())
                .translationY(bottomFunctionViewModel.bottomSizeParam.mEffectBarHeight.toFloat())
                .setListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        action.invoke()
                    }
                })
                .start()
        }
    }


    private fun setGroupIndicatorIfNeed(catList: List<LookCategory>) {
        // 设置每一组的边界。
        var lookSize = 0
        val lookCategoriesIndex = ArrayList<Int>()
        catList.forEach { category ->
            val materials = category.lookMaterials.size

            lookSize += materials
            if (materials > 0 && lookSize > 0) {
                lookCategoriesIndex.add(lookSize - 1)
            }
        }
        // 超过1 个分类之后才需要设置分割符
        if (catList.size > 1) {
            (mViewBinding.rvLook.getItemDecorationAt(0) as? LookItemDecoration)?.setCategoryRange(
                lookCategoriesIndex
            )
        }
    }


    /**
     * 定位到男性分类
     */
    private fun isNeedLocateOnMale(): Boolean {
        return !cameraArViewModel.hasApplyAr()
                && lookViewModel.isFirEnterLook == true
                && !lookViewModel.hasLookProtocol()
                && !lookViewModel.hasApplyLookMaterial()
                && bpCameraViewModel.cameraParams.gender == Gender.Male
    }
}