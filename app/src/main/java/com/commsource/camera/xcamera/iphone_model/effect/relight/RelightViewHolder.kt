package com.commsource.camera.xcamera.iphone_model.effect.relight

import android.content.Context
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.databinding.DataBindingUtil
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemCameraIphoneCircleIconfontBinding
import com.commsource.beautyplus.databinding.ItemCameraIphoneRelightBinding
import com.commsource.camera.xcamera.cover.bottomFunction.effect.beauty.BeautyAdapter
import com.commsource.camera.xcamera.iphone_model.effect.IphoneFuncAdapter
import com.commsource.util.gone
import com.commsource.util.invisible
import com.commsource.util.visible
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseViewHolder

class RelightViewHolder(context: Context, parent: ViewGroup?) :
    BaseViewHolder<IphoneReLight>(context, parent, R.layout.item_camera_iphone_circle_iconfont) {
    private val mViewBinding: ItemCameraIphoneCircleIconfontBinding = DataBindingUtil.bind<ItemCameraIphoneCircleIconfontBinding>(itemView)!!

    override fun onBindViewHolder(position: Int, item: BaseItem<IphoneReLight>?, payloads: MutableList<Any>?) {
        super.onBindViewHolder(position, item, payloads)

        val mAdapter = getAdapter() as IphoneFuncAdapter
        if (item == null) {
            return
        }

        mViewBinding.ifv.setText(item.entity.iconFontRes)
        if (item.isSelect) {
            mViewBinding.ifv.setTextColor(mAdapter.selectColor)
        } else {
            mViewBinding.ifv.setTextColor(mAdapter.normalColor)
        }
    }
}