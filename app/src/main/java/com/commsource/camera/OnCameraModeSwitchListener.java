package com.commsource.camera;

import com.commsource.camera.mvp.annotation.CameraMode;

/**
 * Created by meitu on 2018/4/11.
 * <AUTHOR>
 * 模式切换接口抽取出来
 */

public interface OnCameraModeSwitchListener {
    /**
     * onTabChange
     * @param lastMode lastMode
     * @param curMode  curMode
     */
    void onTabChange(@CameraMode int lastMode, @CameraMode int curMode,
                     boolean isHandClick);

    /**
     * 是否不可以切换。
     * @return
     */
    boolean isBlockSwitch();
}
