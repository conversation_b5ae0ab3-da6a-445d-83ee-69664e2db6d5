package com.commsource.ad.adtaskcenter.rewardcenter

import android.os.Handler
import android.os.Looper
import com.commsource.ad.adtaskcenter.AdTaskCenterSpConfig
import com.google.gson.Gson
import java.util.Calendar

object RewardCenter {
    object RewardGainReason {
        const val doneAdTaskCenterTasks = "done_ad_task_center_tasks"
    }

    fun init() {
        // 触发奖励状态刷新
        hasTodayVip()

        scheduleClearTodayVipReward()
    }

    fun grantTodayVipReward(grantReason: String) {
        val todayVip = VipReward(expireTime = getLastMillisecondOfDay(), grantReason = grantReason)
        RewardCenterSpConfig.setTodayVipReward(Gson().toJson(todayVip))
    }

    fun hasTodayVip(): Boolean {
        val todayVip = RewardCenterSpConfig.getTodayVipReward()
        if (todayVip.isNullOrEmpty()) {
            return false
        }
        val todayVipReward = Gson().fromJson(todayVip, VipReward::class.java)

        if (todayVipReward.isExpired()) {
            clearTodayVipReward()
            return false
        }
        return true
    }

    /**
     * 每日24点，应当清空之前的每日会员
     * 时间发生变化时，是否可以作弊？
     */
    private fun clearTodayVipReward() {
        // 检测是否有每日会员
        val todayVip = RewardCenterSpConfig.getTodayVipReward()
        if (todayVip.isNullOrEmpty()) {
            return
        }
        // 有奖励过会员，不管有没有过期
        AdTaskCenterSpConfig.setNeedShowVipExpiredDialog(true)
        RewardCenterSpConfig.setTodayVipReward("")
    }

    // 获取当天的最后一毫秒
    private fun getLastMillisecondOfDay(): Long = Calendar.getInstance().run {
        set(Calendar.HOUR_OF_DAY, 23)
        set(Calendar.MINUTE, 59)
        set(Calendar.SECOND, 59)
        set(Calendar.MILLISECOND, 999)
        return timeInMillis
    }

    /**
     * 每日最后1秒，清空奖励
     */
    private fun scheduleClearTodayVipReward() {
        val delayMills = getLastMillisecondOfDay() - System.currentTimeMillis()
        Handler(Looper.getMainLooper()).postDelayed({
            clearTodayVipReward()
        }, delayMills)
    }

    fun debugClear() {
        RewardCenterSpConfig.setTodayVipReward("")
    }
}