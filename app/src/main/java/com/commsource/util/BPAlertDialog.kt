package com.commsource.util

import com.commsource.widget.dialog.XAlertDialog
import com.commsource.widget.dialog.delegate.Content
import com.commsource.widget.dialog.delegate.NegativeButton
import com.commsource.widget.dialog.delegate.PositiveButton
import com.commsource.widget.dialog.delegate.Title
import com.commsource.widget.dialog.delegate.VideoPictureTips
import com.commsource.widget.dialog.delegate.XDialog
import com.commsource.widget.dialog.delegate.config.DialogUIMode

/**
 * BP弹窗配置处理
 */
class BPAlertDialog(config: XAlertDialog.Config) : XAlertDialog.IXAlertDialog(config) {

    val dialog by lazy {
        XDialog {
            VideoPictureTips {
                if (config.mode == XAlertDialog.Config.MODE_DART) {
                    mode = DialogUIMode.DARK_MODE
                }
                Title(config.title)
                config.contents?.forEach {
                    Content(it)
                }
                NegativeButton(config.negativeText) {
                    config.negativeClick?.invoke(this@BPAlertDialog)
                }
                PositiveButton(config.positiveText) {
                    config.positiveClick?.invoke(this@BPAlertDialog)
                }
                config.dismissListener?.let {
                    setOnDismissListener(Runnable {
                        it.onDismiss(this@BPAlertDialog)
                    })
                }
                cancelAble = config.cancelable
                cancelOutside = config.cancelable
                closeEnable = config.closeable
            }
        }
    }

    override fun show() {
        dialog.show()
    }

    override fun cancel() {
        dialog.dismiss()
    }

    override fun dismiss() {
        dialog.dismiss()
    }
}