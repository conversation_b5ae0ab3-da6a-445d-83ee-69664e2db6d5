package com.commsource.util;

import static android.opengl.GLES20.GL_CLAMP_TO_EDGE;
import static android.opengl.GLES20.GL_COLOR_ATTACHMENT0;
import static android.opengl.GLES20.GL_FLOAT;
import static android.opengl.GLES20.GL_LINEAR;
import static android.opengl.GLES20.GL_NEAREST;
import static android.opengl.GLES20.GL_RGBA;
import static android.opengl.GLES20.GL_TEXTURE_2D;
import static android.opengl.GLES20.GL_TEXTURE_MAG_FILTER;
import static android.opengl.GLES20.GL_TEXTURE_MIN_FILTER;
import static android.opengl.GLES20.GL_TEXTURE_WRAP_S;
import static android.opengl.GLES20.GL_TEXTURE_WRAP_T;
import static android.opengl.GLES20.glBindFramebuffer;
import static android.opengl.GLES20.glBindTexture;
import static android.opengl.GLES20.glDeleteFramebuffers;
import static android.opengl.GLES20.glDeleteTextures;
import static android.opengl.GLES20.glGetError;
import static android.opengl.GLES20.glTexParameteri;
import static android.opengl.GLES20.glGenFramebuffers;
import static android.opengl.GLES20.GL_FRAMEBUFFER;
import static android.opengl.GLES20.glViewport;
import static android.opengl.GLES20.glFramebufferTexture2D;
import static android.opengl.GLES20.glCheckFramebufferStatus;
import static android.opengl.GLES20.GL_FRAMEBUFFER_COMPLETE;
import static android.opengl.GLES20.glTexImage2D;
import static android.opengl.GLES20.GL_NO_ERROR;

import static android.opengl.GLES20.glGenTextures;
import static android.opengl.GLES20.glTexParameterf;
import static android.opengl.GLES30.GL_RGBA16F;
import static android.opengl.GLES30.GL_RGBA32F;

import android.opengl.GLES20;

import android.util.Log;

import com.meitu.library.util.Debug.Debug;


/**
 * GLES 3.0 工具类
 *
 * <AUTHOR> 2020.01.08
 */
public class GLES30Util {
    /*
    * 是否支持浮点纹理
    * 支持：   返回true（函数内部打印支持的位数）
    * 不支持：  返回false
    * */
    public static boolean isSupportFloatRenderTarget() {
        int[] offScreenFramebuffer = new int[1];
        glGenFramebuffers(offScreenFramebuffer.length, offScreenFramebuffer, 0);
        glBindFramebuffer(GL_FRAMEBUFFER, offScreenFramebuffer[0]);

        int[] texture = new int[1];
        glGenTextures(1, texture, 0);
        glBindTexture(GL_TEXTURE_2D, texture[0]);
        int w = 32, h = 32;
        GLES20.glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA32F, w, h, 0, GL_RGBA, GL_FLOAT, null);

        glTexParameterf(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
        glTexParameterf(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_NEAREST);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_NEAREST);

        glViewport(0, 0, w, h);
        glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, GL_TEXTURE_2D, texture[0], 0);
        int status = glCheckFramebufferStatus(GL_FRAMEBUFFER);
        boolean bRes = (GL_FRAMEBUFFER_COMPLETE == status);
        int nSupportBite = 0;
        if (bRes) {
            nSupportBite = 32;
        }
        else {
            glBindTexture(GL_TEXTURE_2D, texture[0]);
            glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA16F, w, h, 0, GL_RGBA, GL_FLOAT, null);
            glViewport(0, 0, w, h);
            glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, GL_TEXTURE_2D, texture[0], 0);
            status = glCheckFramebufferStatus(GL_FRAMEBUFFER);
            bRes = (GL_FRAMEBUFFER_COMPLETE == status);
            if (bRes) {
                nSupportBite = 16;
            }
        }

        if (offScreenFramebuffer != null) {
            glDeleteFramebuffers(offScreenFramebuffer.length, offScreenFramebuffer, 0);
            offScreenFramebuffer = null;
        }

        if (texture != null) {
            glDeleteTextures(texture.length, texture, 0);
            texture = null;
        }

        glBindFramebuffer(GL_FRAMEBUFFER, 0);
        glBindTexture(GL_TEXTURE_2D, 0);
        int nErrorState = glGetError();
        if(nErrorState != GL_NO_ERROR) {
            Log.e("GLES30Util","glGetError()="+nErrorState);
        }

        Debug.i("GLES30Util", "nSupportBite="+nSupportBite+", result="+bRes);
        return bRes;
    }
}
