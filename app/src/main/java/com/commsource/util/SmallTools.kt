package com.commsource.util

import android.view.ViewTreeObserver.OnDrawListener
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.meitu.common.AppContext


//根据线程确定发送方法
fun <T : Any?> MutableLiveData<T>.set(v: T?) {

    if (UIHelper.isMainThread()) {
        value = v
    } else {
        postValue(v)
    }
}

fun RecyclerView.clickPositionBeforeDraw(position: Int) {
    "click $position".LOGV()
    viewTreeObserver.addOnDrawListener(object : OnDrawListener {
        var hasCallClick = false
        override fun onDraw() {
            if (!hasCallClick) {
                //可能position这时候不可见，recyclerview未加载
                smoothScrollToPosition(position)
                postDelayed({
                    getChildAt(position)?.run {
                        "has view".LOGV()
                        callOnClick()
                    }
                    viewTreeObserver.removeOnDrawListener(this)
                }, 500)
                hasCallClick = true
            }

        }

    })

}

fun RecyclerView.clickPosition(position: Int) {
    //可能position这时候不可见，recyclerview未加载
    smoothScrollToPosition(position)
    postDelayed({
        getChildAt(position)?.run {
            callOnClick()
        }
    }, 500)

}

fun CharSequence.removeLastChat(): String {
    var tmp = this
    if (length > 1) {
        tmp = subSequence(0, length - 1).toString()
    }
    return tmp.toString()
}

/**
 * int 转string
 * <p>
 * Author:
 * Date: 2020-12-16
 * @receiver Int
 * @return String
 */
fun Int.string(): String = AppContext.application.getString(this)


/**
 * 图片资源获取
 * <p>
 * Author:
 * Date: 2020-12-16
 * @receiver Int
 * @return Drawable?
 */
fun Int.drawable() = AppContext.application.getDrawable(this)

