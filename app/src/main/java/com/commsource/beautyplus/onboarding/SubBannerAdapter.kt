package com.commsource.beautyplus.onboarding

import android.app.Activity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.commsource.beautyplus.R
import com.commsource.util.GlideProxy

/**
 *
 * Created on 2021/9/27
 * <AUTHOR>
 */
class SubBannerAdapter (val activity:Activity, val banners:List<String>):

    RecyclerView.Adapter<SubBannerAdapter.SubBannerViewHolder>() {


    class SubBannerViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        val ivBanner:ImageView = itemView.findViewById(R.id.iv_banner)

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SubBannerViewHolder {
        return SubBannerViewHolder(LayoutInflater.from(parent.context).inflate(R.layout.sub_banner_item_layout, parent, false))
    }

    override fun onBindViewHolder(holder: SubBannerViewHolder, position: Int) {
        GlideProxy.with(activity).load(banners[position]).into(holder.ivBanner)
    }

    override fun getItemCount(): Int {
        return banners.size
    }
}