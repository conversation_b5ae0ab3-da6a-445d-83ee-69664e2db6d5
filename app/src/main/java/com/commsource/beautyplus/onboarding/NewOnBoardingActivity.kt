package com.commsource.beautyplus.onboarding

import android.content.Intent
import android.graphics.Color
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.DynamicDrawableSpan
import android.util.TypedValue
import android.view.MotionEvent
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import com.commsource.BaseDialog
import com.commsource.beautyplus.BaseActivity
import com.commsource.beautyplus.R
import com.commsource.beautyplus.constant.AppConfigs
import com.commsource.beautyplus.databinding.ActivityNewOnBoardingBinding
import com.commsource.beautyplus.setting.abtest.ABTestDataEnum
import com.commsource.beautyplus.web.WebActivity
import com.commsource.beautyplus.web.WebConstant
import com.commsource.billing.SubConfigEnum
import com.commsource.billing.SubSource
import com.commsource.billing.activity.SubscribeViewModel
import com.commsource.billing.bean.SubPriceInfo
import com.commsource.billing.pro.GmsManager
import com.commsource.camera.util.XAnimator
import com.commsource.camera.util.animationTransition
import com.commsource.camera.xcamera.widget.CameraSwitchButton
import com.commsource.config.ApplicationConfig
import com.commsource.config.SubscribeConfig
import com.commsource.homev3.HomeRouter
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.MTPageDurationManager
import com.commsource.statistics.Meepo
import com.commsource.statistics.SpmParamConstant
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.util.AppTools
import com.commsource.util.BPLocationUtils
import com.commsource.util.GoFunctionUtils
import com.commsource.util.LOGV_Pro
import com.commsource.util.ResourcesUtils
import com.commsource.util.UIHelper
import com.commsource.util.ViewUtils
import com.commsource.util.alphaDismiss
import com.commsource.util.common.ProcessUtil
import com.commsource.util.compatRtl
import com.commsource.util.delegate.IProcessHandler
import com.commsource.util.delegate.process.SubscribeProcess
import com.commsource.util.dp
import com.commsource.util.dpf
import com.commsource.util.drawable
import com.commsource.util.gone
import com.commsource.util.invisible
import com.commsource.util.isVisible
import com.commsource.util.setHeight
import com.commsource.util.setMarginCompat
import com.commsource.util.setMarginTop
import com.commsource.util.setSize
import com.commsource.util.text
import com.commsource.util.visible
import com.commsource.widget.DisplayExtension
import com.commsource.widget.GDPRUtils
import com.commsource.widget.bind.marginTop
import com.commsource.widget.dialog.common.ADialog
import com.commsource.widget.dialog.delegate.PositiveButton
import com.commsource.widget.dialog.delegate.VideoPictureTips
import com.commsource.widget.dialog.delegate.XDialog
import com.commsource.widget.dialog.delegate.popupCenter
import com.meitu.common.AppContext
import com.meitu.common.utils.ToastUtils
import com.meitu.library.hwanalytics.spm.SPMManager
import com.meitu.library.hwanalytics.spm.SPMShare
import com.meitu.library.hwanalytics.spm.bean.PositionModel
import com.meitu.library.util.device.DeviceUtils
import com.meitu.library.util.net.NetUtils
import com.meitu.ratiorelativelayout.RatioRelativeLayout

/**
 *
 * Created on 2021/9/25
 * <AUTHOR>
 */
class NewOnBoardingActivity : BaseActivity() {

    private lateinit var mViewBinding: ActivityNewOnBoardingBinding

    private val subViewModel by lazy { ViewModelProvider(this)[SubscribeViewModel::class.java] }

    private val mViewModel by lazy { ViewModelProvider(this)[BoardingViewModel::class.java] }

    /**
     * 是否可以统计订阅埋点信息，从其他界面再返回不统计
     */
    private var canLogSub = true

    private var isSubPanelShow = false

    private var freeTestTrigger = false

    private var vBanner: View? = null
    private var leftBanner: View? = null
    private var rightBanner: View? = null

    private var textPanelHeight = 180.dp

    private var textPanelTranslateY = 0.dpf

    private val showIntro =
        Meepo.isNewUserOnboardingTest() && ApplicationConfig.isOnboardingIntroOpen()

    private var introPageId = 1

    override fun onCreate(savedInstanceState: Bundle?) {
        isNeedLoadLib = false
        MTPageDurationManager.getInstance().appColdStartTime = 0L
        super.onCreate(savedInstanceState)
        mViewBinding = DataBindingUtil.setContentView(this, R.layout.activity_new_on_boarding)
        initViewSize()
        initViewListener()
        initViewModel()

        Meepo.enterTest(
            ABTestDataEnum.ONBOARDING_SUB_ENTRANCE_REF,
            ABTestDataEnum.ONBOARDING_SUB_ENTRANCE_TEST
        )
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        if (isSubPanelShow) {
            goHome()
        }
    }

    override fun finish() {
        SPMManager.instance.setSpmLock(true)
        super.finish()
        SPMShare.clear()
        subViewModel.release()
    }

    override fun onStart() {
        super.onStart()
        staticsSubImp()
        if (!canLogSub) {
            canLogSub = true
        }
    }

    override fun onDestroy() {
        mViewBinding.videoComponent.release()
        mViewBinding.rvBanner.stopPlay()
        super.onDestroy()
    }

    override fun addSpm() {
        val model = PositionModel()
        model.pageId = NewOnBoardingActivity::class.java.simpleName
        model.setPageObject(this)
        model.page = "1008"
        SPMManager.instance.pushSpm(model)
    }

    override fun onScreenSizeConfigurationChanged() {
        super.onScreenSizeConfigurationChanged()
        initViewSize()
    }

    private fun initViewSize() {
        mViewBinding.bgNext.radius = 29.dpf
        setRatio(mViewBinding.vShadow, -1f)
        setRatio(mViewBinding.cbIconContent, -1f)

        if (Meepo.isInOnboardingSubEntranceTest()) {
            // 引导文案和试用开关上移，确保试用开关区域正常显示
            val lp = mViewBinding.llTextPanel.layoutParams
            if (lp is RatioRelativeLayout.LayoutParams) {
                lp.ratioMarginTop = 1.dpf
            }
            mViewBinding.tvContent.marginTop(6)
        } else {
            mViewBinding.llToSubPage.setHeight(0)
            val lp = mViewBinding.llToSubPage.layoutParams
            if (lp is RatioRelativeLayout.LayoutParams) {
                lp.ratioMarginBottom = 0f
            }
        }

        setCancelText()

        val devY = if (DeviceUtils.isTotalFullScreenDevice()) {
            (-44).dp() + DeviceUtils.getStatusHeight()
        } else if (DeviceUtils.isFullScreenDevice()) {
            (-44).dp()
        } else {
            (-44 - 18).dp()
        }

        if (DisplayExtension.hasCutout()) { // 奇怪这里设置margin怎么不生效
            mViewBinding.ivClose.translationY = 24.dpf()
        }

        val height = DeviceUtils.getScreenWidth() * 1758 / 1125
        ViewUtils.setHeight(mViewBinding.ivBg, height)
        mViewBinding.ivBg.setMarginTop(devY)
        mViewBinding.ivBg.scaleAnimEnable = false
        val screenH = if (DeviceUtils.hasNavigationBar()) {
            DeviceUtils.getRealScreenHeight() - DeviceUtils.getNavigationBarHeight()
        } else {
            DeviceUtils.getRealScreenHeight()
        }
        val screenRatio = screenH * 1.0f / DeviceUtils.getScreenWidth()
        var videoW = when {
            screenRatio >= 2.0f -> DeviceUtils.getScreenWidth() - 60.dp
            screenRatio >= 1.76f -> DeviceUtils.getScreenWidth() - 90.dp//16:9
            else -> DeviceUtils.getScreenWidth() - 120.dp
        }
        var videoH = videoW * 400 / 315
        val marginTop = (height - videoH) / 2 + devY

        val videoComponentMaxAvailableHeight = screenH - marginTop
        if (videoH >= videoComponentMaxAvailableHeight) {
            // 0.5：预设值，具体值可以根据UI走查再定
            videoH = (videoComponentMaxAvailableHeight * 0.5).toInt()
            videoW = videoH * 315 / 400
        }

        mViewBinding.videoComponent.setSize(height = videoH, width = videoW)
        mViewBinding.videoComponent.setMarginTop(marginTop)
        var bannerMarginTop = (height - 317.dp()) / 2 + devY
        if (isFoldable) {
            bannerMarginTop /= 2
        }
        ViewUtils.setMarginTop(mViewBinding.rvBanner, bannerMarginTop)
        mViewBinding.videoComponent.post {
            val bannerRect = Rect()
            mViewBinding.rvBanner.getGlobalVisibleRect(bannerRect)
            val buttonRect = Rect()
            mViewBinding.flNext.getGlobalVisibleRect(buttonRect)
            textPanelHeight = if (DeviceUtils.isFullScreenDevice()) {
                buttonRect.top - bannerRect.bottom - 65.dp
            } else {
                buttonRect.top - bannerRect.bottom - 16.dp
            }

            // 这里必须设置一下
            mViewBinding.llTextPanel.setSize(height = textPanelHeight)

            val videoRect = Rect()
            mViewBinding.videoComponent.getGlobalVisibleRect(videoRect)
            val temp = buttonRect.top - videoRect.bottom - 30.dpf
            textPanelTranslateY = temp - textPanelHeight
        }
    }

    private fun setRatio(view: View, ratio: Float) {
        val lp = view.layoutParams
        if (lp is RatioRelativeLayout.LayoutParams) {
            lp.ratioWidth = ratio
            lp.ratioHeight = ratio
        }
    }

    private fun initViewListener() {
        mViewBinding.tvSubFreeTrail.text = R.string.t_continue.text()

        mViewBinding.flNext.setOnClickListener {
            if (ProcessUtil.isProcessing()) {
                return@setOnClickListener
            }
            if (mViewBinding.subBtnContainer.visibility == View.VISIBLE) {
                subscribe()
            } else {
                goNext()
            }
        }

        mViewBinding.flNext.setOnTouchListener { v, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                mViewBinding.flNext.alpha = 0.8f
            } else if (event.action == MotionEvent.ACTION_UP) {
                mViewBinding.flNext.alpha = 1.0f
            }
            false
        }

        mViewBinding.llToSubPage.setOnClickListener {
            // 跳转到ProActivity的订阅，此页面不用再重复上报
            subViewModel.setNeedLogSubSuccess(false)

            SPMShare.put(SpmParamConstant.KEY_SOURCE_CLICK_POSITION, "查看所有方案")
            IProcessHandler(this)
                .execute(object : SubscribeProcess(SubSource.FROM_ON_BOARDING_SEE_ALL_PLANS) {
                    override fun onSubscribeResult(isSubcribe: Boolean) {
                        // 订阅成功会回调 subGracePeriodEvent，此处不用重复处理
                    }
                })
        }

        mViewBinding.tvRestore.setOnClickListener {
            subViewModel.restore()
        }

        mViewBinding.tvPrivacyPolicy.setOnClickListener {
            canLogSub = false
            val intent = Intent(this, WebActivity::class.java).apply {
                putExtra(WebConstant.PARAMETER_URL, AppConfigs.PRIVACY_POLICY)
                putExtra(WebConstant.SETTINGS_SET_SUPPORT_ZOOM, false)
            }
            startActivity(intent)
        }

        mViewBinding.tvTermsOfService.setOnClickListener {
            canLogSub = false
            val intent = Intent(this, WebActivity::class.java).apply {
                putExtra(WebConstant.PARAMETER_URL, AppConfigs.TERMS_OF_SERVICE)
                putExtra(WebConstant.SETTINGS_SET_SUPPORT_ZOOM, false)
            }
            startActivity(intent)
        }

        mViewBinding.ivClose.setOnClickListener {
            goHome()
        }

        mViewBinding.videoComponent.onPlayChangeListener = object :
            VideoTransformView.OnPlayChangeListener {

            override fun onProgress(currentPosition: Int) {
                mViewModel.selectGallery?.let {
                    if (it.videoNodes.size <= 0) {
                        return
                    }
                    var frame = currentPosition / 30
                    if (frame <= 1) {
                        frame = 1
                    }
                    for (node in it.videoNodes) {
                        if (frame == (node.frame)) {
                            mViewBinding.ifvIcon.setIconFontResWithAnim(node.iconFontRes)
                        }
                    }
                }
            }

        }
    }

    private fun subscribe() {
        // 点击立即订阅按钮，在Onboarding页订阅时，需要Onboarding页的 subViewModel 自己上报埋点
        subViewModel.setNeedLogSubSuccess(true)

        subViewModel.purchaseType = SubPriceInfo.PURCHASE_TYPE_YEARLY
        subViewModel.subscribe(this)
    }

    private fun initViewModel() {
        GmsManager.instance.subGracePeriodEvent.observe(this@NewOnBoardingActivity) {
            it?.let {
                if (SubscribeConfig.isSubValid()) {
                    goHome()
                }
            }
        }

        subViewModel.apply {
            loadPriceEvent.observe(this@NewOnBoardingActivity, Observer { priceInfo ->
                priceInfo?.let {
                    setSubPrice(it)
                }
            })

            subSuccessEvent.observe(this@NewOnBoardingActivity, Observer {
                if (subViewModel.isHandClickSub) {
                    goHome()
                }
            })

            toastEvent.observe(this@NewOnBoardingActivity, Observer {
                if (isResume) {
                    ToastUtils.showShortToastSafe(it)
                }
            })

            restoreLoadingEvent.observe(this@NewOnBoardingActivity, Observer {
                if (it == true) {
                    showLoadingDialog()
                } else {
                    dismissLoadingDialog()
                }
            })

            dialogEvent.observe(this@NewOnBoardingActivity, Observer {
                showCustomDialog(it)
            })
        }

        mViewModel.locate()
        mViewModel.asiaCountry.observe(this, Observer { isAsiaCountry ->

            if (showIntro) {
                logOnboardingIntroType("B")
                logOnboardingIntroAppr()
                mViewModel.initData(isAsiaCountry)
                mViewModel.getGallery()?.let { gallery ->
                    mViewBinding.ivBg.setShowImage(gallery.background)
                    mViewBinding.videoComponent.startPlayVideo(gallery.videoPath)
                    val videoNode = gallery.videoNodes.get(0)
                    mViewBinding.textPageView.setText(gallery.title, gallery.content)
                    mViewBinding.ifvIcon.setIconFontRes(videoNode.iconFontRes)
                }
            } else {
                logOnboardingIntroType("A")
            }

            "国家代码 ${mViewModel.locateBean?.country_code}".LOGV_Pro()
            if (ApplicationConfig.isNewUser(AppContext.context) && (BPLocationUtils.US_COUNTRY_CODE == mViewModel.locateBean?.country_code)) {
                "美国新用户，进入免费试用逻辑".LOGV_Pro()
                subViewModel.freeSwitchState.supported = true
                subViewModel.freeSwitchState.supportSubConfigEnum.let {
                    it.add(SubConfigEnum.NEW_USER)
                }
            } else {
                subViewModel.freeSwitchState.supported = false
            }
            subViewModel.initSubs(true, true)
        })

        if (!showIntro) {
            //直接显示订阅页
            showSubPanel()
        }
    }


    /**
     * 展示自定义弹框
     * @param dialogType 弹框类型
     */
    private fun showCustomDialog(dialogType: Int) {
        when (dialogType) {
            SubscribeViewModel.DIALOG_TYPE_RECOVERY_IAP -> ADialog.showWithParam(
                getString(R.string.half_restored),
                getString(R.string.dialog_confirm),
                { obj: BaseDialog<*> -> obj.dismiss() },
                true
            )

            SubscribeViewModel.DIALOG_TYPE_RECOVERY_SUCCESS -> ADialog.showWithParam(
                getString(R.string.restored),
                getString(R.string.dialog_confirm),
                { obj: BaseDialog<*> ->
                    obj.dismiss()
                    goHome()
                },
                false
            )

            SubscribeViewModel.DIALOG_TYPE_RECOVERY_ILLEGAL -> ADialog.showWithParam(
                getString(R.string.not_subs_no_restore),
                getString(R.string.dialog_confirm),
                { obj: BaseDialog<*> -> obj.dismiss() },
                true
            )

            SubscribeViewModel.DIALOG_TYPE_RECOVERY_FAILED, SubscribeViewModel.DIALOG_TYPE_RECOVERY_HELP -> {
                if (ApplicationConfig.isAgreeUserProtocol(AppContext.context) && !GDPRUtils.isEuroArea(
                        AppContext.context
                    )
                ) {
                    ADialog.showWithParam(
                        ResourcesUtils.getString(R.string.purchase_restore_failed),
                        ResourcesUtils.getString(R.string.solve_now),
                        { dialog: BaseDialog<*> ->
                            //GoFunctionUtils.restoreFailure(this, "订阅：" + subViewModel.from)
                            GoFunctionUtils.go2ZendeskFAQ(this);
                            dialog.dismiss()
                        },
                        false
                    )
                } else {
                    ToastUtils.showShortToastSafe(ResourcesUtils.getString(R.string.purchase_restore_failed))
                }
            }

            else -> {
            }
        }
    }

    private fun goHome() {
        ApplicationConfig.setShowOnBoarding(false)
        val intent = HomeRouter.obtain2HomeIntent(this)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
        startActivity(intent)
        finish()
        overridePendingTransition(R.anim.dialog_alpha_in, R.anim.slide_out_to_bottom)
    }

    private fun staticsSubImp() {
        if (canLogSub && isSubPanelShow) {
            MTAnalyticsAgent.logEvent(MTAnalyticsConstant.SUB_PAGE_IMP)
        }
    }

    private fun setSubPrice(subPriceInfo: SubPriceInfo) {
        val freeSwitchState = subViewModel.freeSwitchState
        if (freeSwitchState.enable) {
            if (isSubPanelShow && !freeTestTrigger) {
                freeTestTrigger = true
            }
            mViewBinding.switchFree.open(freeSwitchState.on)
            mViewBinding.flFreeSwitch.visible()
            if (freeSwitchState.on) {
                mViewBinding.tvFreeSwitchTip.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14f)
                mViewBinding.tvSure.gone()
                mViewBinding.tvFreeSwitchTip.text =
                    R.string.t_free_trial_enable.text(subPriceInfo.yearlyFreeTrialPeriod.toString())
            } else {
                mViewBinding.tvSure.visible()
                mViewBinding.tvFreeSwitchTip.text = R.string.t_onboarding_not_sure2.text()
            }
        } else {
            mViewBinding.flFreeSwitch.gone()
            if (ApplicationConfig.isNewUser(AppContext.context) && BPLocationUtils.isUS(AppContext.context) && subPriceInfo.yearlyFreeTrialPeriod > 0) {
                freeTestTrigger = true
            }
        }
        SPMManager.instance.getTopModel()?.let {
            val temp = if (freeSwitchState.on) {
                "1"
            } else {
                "0"
            }
            it.addExtraInfo("is_trial_open", temp)
        }
        val yearlyFreeTrialPeriod = subPriceInfo.yearlyFreeTrialPeriod
        if (!subPriceInfo.isPriceEmpty) {
            if (isSubPanelShow) {
                showSubBtn()
                mViewBinding.llTextPanel.animationTransition(duration = 200) {
                    showSubPanelSwitch()
                }
            }
        }

        "国家代码 ${BPLocationUtils.getLocationBean(AppContext.context).country_code} yearlyPrice ${subPriceInfo.yearlyPrice}".LOGV_Pro()
        if (subPriceInfo.subUserState.isResubscribe && subPriceInfo.hasYearIntroductoryPric() && subPriceInfo.yearIntroductoryDay > 360) {
            mViewBinding.tvSubFreeTrail.text = R.string.t_purchase_resubs_10_off.text()
            mViewBinding.tvSubDetail.visible()
            mViewBinding.tvSubDetail.text = String.format(
                ResourcesUtils.getString(R.string.t_purchase_resubs_1st_year_then_yearly),
                subPriceInfo.yearIntroductoryPrice,
                subPriceInfo.yearlyPrice
            )
        } else if (yearlyFreeTrialPeriod > 0) {
            mViewBinding.tvSubFreeTrail.text = String.format(
                ResourcesUtils.getString(R.string.v77126_A_1), yearlyFreeTrialPeriod.toString()
            )
            mViewBinding.tvSubDetail.visible()
            mViewBinding.tvSubDetail.text = String.format(
                ResourcesUtils.getString(R.string.v77126_A_2),
                yearlyFreeTrialPeriod.toString(),
                subPriceInfo.yearlyPrice
            )
        } else {
            mViewBinding.tvSubFreeTrail.text =
                ResourcesUtils.getString(R.string.new_home_subscribe_now)
            mViewBinding.tvSubDetail.visible()
            mViewBinding.tvSubDetail.text =
                subPriceInfo.yearlyPrice + ResourcesUtils.getString(R.string.per_year)
        }
    }

    private fun goNext() {
        if (mViewBinding.videoComponent.isPreparing()) {
            return
        }
        logOnboardingIntroClick()
        val gallery = mViewModel.getGallery()
        if (gallery == null) {
            if (SubscribeConfig.isSubValid() || !NetUtils.canNetworking()) {
                // 已订阅用户返回首页
                goHome()
                return
            }
            showSubPanel()
            return
        }


        introPageId++
        logOnboardingIntroAppr()

        //下一段
        mViewBinding.videoComponent.startPlayVideoWithAnim(gallery.videoPath) {
            val videoNode = gallery.videoNodes[0]
            mViewBinding.textPageView.setTextWithAnim(gallery.title, gallery.content)
            mViewBinding.ifvIcon.setIconFontResWithAnim(videoNode.iconFontRes)
            mViewBinding.ivBg.setImageWithAnim(gallery.background)
        }

    }

    private fun showSubPanel() {
        if (isSubPanelShow) {
            return
        }

        val screenH = if (DeviceUtils.hasNavigationBar()) {
            DeviceUtils.getRealScreenHeight() - DeviceUtils.getNavigationBarHeight()
        } else {
            DeviceUtils.getRealScreenHeight()
        }
        val screenRatio = screenH * 1.0f / DeviceUtils.getScreenWidth()
        if (screenRatio >= 2.0f) {
            mViewBinding.flNext.setMarginCompat(bottom = 82.dp)
        } else {
            mViewBinding.flNext.setMarginCompat(bottom = 72.dp)
        }


        showSubscribeView()
        mViewBinding.switchFree.onSwitchListener = object : CameraSwitchButton.OnSwitchListener {
            override fun onSwitch(isOpen: Boolean, fromUser: Boolean) {
                if (fromUser) {
                    val freeSwitchState = subViewModel.freeSwitchState
                    freeSwitchState.on = isOpen
                    subViewModel.buildProModel()
                }
            }

        }

        if (subViewModel.freeSwitchState.enable && !freeTestTrigger) {
            freeTestTrigger = true
            mViewBinding.flFreeSwitch.visible()
        } else {
            mViewBinding.flFreeSwitch.gone()
            val yearlyFreeTrialPeriod =
                subViewModel.loadPriceEvent.value?.yearlyFreeTrialPeriod ?: 0
            if (!freeTestTrigger && ApplicationConfig.isNewUser(AppContext.context) && BPLocationUtils.isUS(
                    AppContext.context
                ) && yearlyFreeTrialPeriod > 0
            ) {
                freeTestTrigger = true
            }
        }
        isSubPanelShow = true
        mViewBinding.llBottom.visible()
        mViewBinding.tvNext.gone()
        mViewBinding.bgNext.gone()

        mViewBinding.bgPremium.radius = 35.dpf
        mViewBinding.bgPremium.visible()
        val layoutParams = mViewBinding.flNext.layoutParams as RatioRelativeLayout.LayoutParams
        layoutParams.ratioHeight = 60f
        layoutParams.ratioMarginLeft = 15f
        layoutParams.ratioMarginRight = 15f
        mViewBinding.flNext.layoutParams = layoutParams

        val price = mViewBinding.tvSubDetail.text
        if (TextUtils.isEmpty(price)) {
            mViewBinding.rlv.visible()
        } else {
            showSubBtn()
        }
        UIHelper.runOnUiThreadDelay({
            if (AppTools.isFinishing(this)) {
                return@runOnUiThreadDelay
            }
            mViewBinding.ivClose.visible()
            ViewUtils.alphaShow(mViewBinding.ivClose, 200)
        }, 1000)
        staticsSubImp()

        pushSpm()

        val bannerAdapter = SubBannerAdapter(this, mViewModel.getSubBanners())
        mViewBinding.rvBanner.adapter = bannerAdapter
        mViewBinding.rvBanner.autoSmoothScrollToPosition(1)
        mViewBinding.rvBanner.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (vBanner == null) {
                    vBanner = recyclerView.findViewHolderForAdapterPosition(1)?.itemView
                }
                if (leftBanner == null) {
                    leftBanner = recyclerView.findViewHolderForAdapterPosition(0)?.itemView
                }
                if (rightBanner == null) {
                    rightBanner = recyclerView.findViewHolderForAdapterPosition(2)?.itemView
                }
            }
        })
        UIHelper.runOnUiThreadDelay({
            showSubPanelAnimate()
        }, 100)
    }

    private fun showSubBtn() {
        subViewModel.subsConfigInfoEvent.value?.takeIf { it.experiment }?.let {
            // 触发实验
            Meepo.isInABTest(it.abCode, true)
        }

        mViewBinding.subBtnContainer.visible()

        if (Meepo.isInOnboardingSubEntranceTest()) {
            mViewBinding.llToSubPage.visible()
            mViewBinding.llToSubPage.translationY = 0.dpf

            mViewBinding.ivToSubPageArrow.compatRtl()
        }

        mViewBinding.tvCancel.visible()
        mViewBinding.tvCancelDesc.visible()

        mViewBinding.rlv.gone()
    }

    private fun pushSpm() {
        val model = PositionModel()
        model.pageId = NewOnBoardingActivity::class.java.simpleName
        model.setPageObject(this)
        model.page = "1008_01"
        val position = SPMShare[SpmParamConstant.KEY_SOURCE_CLICK_POSITION]
        val content = SPMShare[SpmParamConstant.KEY_SOURCE_FEATURE_CONTENT]
        if (!TextUtils.isEmpty(position)) {
            model.addExtraInfo(MTAnalyticsConstant.KEY_SOURCE_CLICK_POSITION, position!!)
        }
        if (!TextUtils.isEmpty(content)) {
            model.addExtraInfo(MTAnalyticsConstant.KEY_SOURCE_FEATURE_CONTENT, content!!)
        }
        val func = SPMShare[SpmParamConstant.KEY_PRF_FUNC]
        if (!TextUtils.isEmpty(func)) {
            model.addExtraInfo(SpmParamConstant.KEY_PRF_FUNC, func!!)
        }
        val materials = SPMShare[SpmParamConstant.KEY_MIDS_MATERIAL]
        if (!TextUtils.isEmpty(materials)) {
            model.addExtraInfo(SpmParamConstant.KEY_MIDS_MATERIAL, materials!!)
        }
        val materialTags = SPMShare[SpmParamConstant.KEY_MIDS_MATERIAL_TAG]
        if (!TextUtils.isEmpty(materialTags)) {
            model.addExtraInfo(SpmParamConstant.KEY_MIDS_MATERIAL_TAG, materialTags!!)
        }
        model.addExtraInfo("year_sku_id", subViewModel.mYearlySku ?: "")
        model.addExtraInfo("month_sku_id", subViewModel.mMonthlySku ?: "")
        model.addExtraInfo("year_sku_tag", subViewModel.yearSkuTag.type)
        model.addExtraInfo("month_sku_tag", subViewModel.monthSkuTag.type)
        model.addExtraInfo("sub_user_type", subViewModel.logUserType)
        subViewModel.subsConfigInfoEvent.value?.takeIf { it.isClaimed }?.let { config ->
            model.addExtraInfo("coupon_pop_id", config.id)
            model.addExtraInfo("discount_cutdown", subViewModel.getCouponLeftTime(config.countdown))
        }
        SPMManager.instance.pushSpm(model)
    }

    private fun showSubPanelAnimate() {
        mViewBinding.cbIconContent.alphaDismiss()
        mViewBinding.vShadow.alphaDismiss()
        mViewBinding.videoComponent.hideWithAnim()
        mViewBinding.tvTitle.text =
            ResourcesUtils.getString(R.string.t_onboarding_unleash_creativity)
        mViewBinding.tvContent.text =
            ResourcesUtils.getString(R.string.t_onboarding_gorgeoues_effects)
        mViewBinding.llTextPanel.setSize(height = textPanelHeight)

        val price = mViewBinding.tvSubDetail.text
        if (!TextUtils.isEmpty(price)) {
            val duration = if (Meepo.isInOnboardingSubEntranceTest()) {
                0L
            } else {
                300L
            }
            mViewBinding.llTextPanel.animationTransition(duration = duration) {
                showSubPanelSwitch()
            }
        } else {
            mViewBinding.llTextPanel.animationTransition(duration = 200) {
                mViewBinding.flNext.translationY = 0f
                mViewBinding.llTextContent.alpha = 1f
                mViewBinding.llTextPanel.translationY = 0f
                mViewBinding.textPageView.alpha = 0f
                mViewBinding.textPageView.translationY = (-30).dpf
            }
        }

        if (mViewBinding.llToSubPage.isVisible) {
            if (!mViewBinding.tvCancel.isVisible) {
                mViewBinding.llToSubPage.translationY = 25.dpf
            }
        }

        XAnimator.ofFloat(20.dpf(), 0f)
            .duration(700)
            .setAnimationListener(object : XAnimator.SimpleAnimationListener() {
                override fun onAnimationStart(animation: XAnimator?) {
                }

                override fun onAnimationUpdate(fraction: Float, value: Float) {
                    leftBanner?.translationX = -value
                    rightBanner?.translationX = value

                }

                override fun onAnimationEnd(animation: XAnimator?) {
                    leftBanner?.translationX = 0f
                    rightBanner?.translationX = 0f
                }
            })
            .start()
        mViewBinding.rvBanner.startAutoPlay(3000)
    }

    private fun showSubPanelSwitch() {
        mViewBinding.flNext.translationY = 0f
        mViewBinding.llTextContent.alpha = 1f
        mViewBinding.textPageView.alpha = 0f
        mViewBinding.textPageView.translationY = (-30).dpf
        mViewBinding.llTextPanel.translationY = textPanelTranslateY
        val freeSwitchState = subViewModel.freeSwitchState
        if (freeSwitchState.enable) {
            mViewBinding.flSwitchContent.alpha = 1f
        }
    }

    private fun showSubscribeView() {
        mViewBinding.ivBg.gone()
        mViewBinding.cbIconContent.gone()
        mViewBinding.vShadow.gone()
        mViewBinding.rvBanner.visible()
        mViewBinding.videoComponent.invisible()
    }

    private fun setCancelText() {
        val spannableStringBuilder = SpannableStringBuilder(R.string.v77061_A_1.text() + " ")
        // 获取Drawable对象，并设置其边界大小
        val drawable: Drawable? = R.drawable.ic_subscribe_cancel_black.drawable()
        drawable?.setBounds(0, 0, drawable.intrinsicWidth, drawable.intrinsicHeight)
        // 创建一个ImageSpan，将Drawable传递进去
        val imageSpan = drawable?.let {
            MarginImageSpan(it, DynamicDrawableSpan.ALIGN_BOTTOM, 4.dp(), 6.dp())
        }
        // 将图标添加到SpannableStringBuilder中
        imageSpan?.let {
            spannableStringBuilder.setSpan(
                it,
                spannableStringBuilder.length - 1,
                spannableStringBuilder.length,
                SpannableStringBuilder.SPAN_INCLUSIVE_EXCLUSIVE
            )
        }

        // 创建一个ClickableSpan来响应点击事件
        val clickableSpan = object : ClickableSpan() {
            override fun updateDrawState(ds: TextPaint) {
                ds.isUnderlineText = false
            }

            override fun onClick(widget: View) {
                XDialog {
                    VideoPictureTips {
                        title = R.string.v77127_A_2.text()

                        val subPriceInfo = subViewModel.loadPriceEvent.value
                        var freeTrialDays = 0
                        if ((subPriceInfo?.yearlyFreeTrialPeriod ?: 0) > 0) {
                            freeTrialDays = subPriceInfo?.yearlyFreeTrialPeriod ?: 0
                        }
                        if (freeTrialDays > 0) {
                            content.add(R.string.v77127_A_3.text().format(freeTrialDays))
                        } else {
                            content.add(R.string.v77130_C_1.text())
                        }

                        content.add(R.string.v77127_A_4.text() + "\n" + R.string.v77127_A_5.text())
                        PositiveButton(R.string.v77127_A_1.text()) { dialog ->
                            dialog.dismissAllowingStateLoss()
                        }
                        cancelAble = false
                        cancelOutside = false
                        closeEnable = true
                        popupCenter()
                    }
                }.show(this@NewOnBoardingActivity)
            }
        }

        // 将ClickableSpan添加到SpannableStringBuilder中
        spannableStringBuilder.setSpan(
            clickableSpan,
            spannableStringBuilder.length - 5,
            spannableStringBuilder.length,
            SpannableStringBuilder.SPAN_INCLUSIVE_EXCLUSIVE
        )

        mViewBinding.tvCancel.highlightColor = Color.TRANSPARENT
        mViewBinding.tvCancel.text = spannableStringBuilder
        // 这一步很重要，它允许点击事件被触发
        mViewBinding.tvCancel.movementMethod = LinkMovementMethod.getInstance()

        mViewBinding.tvCancelDesc.text = R.string.v77127_A_6.text() + R.string.v77127_A_5.text()
    }

    private fun logOnboardingIntroAppr() {
        MTAnalyticsAgent.logEvent(
            MTAnalyticsConstant.onboarding_intro_appr,
            "page_id",
            introPageId.toString()
        )
    }

    private fun logOnboardingIntroClick() {
        MTAnalyticsAgent.logEvent(
            MTAnalyticsConstant.onboarding_intro_clk,
            "page_id",
            introPageId.toString()
        )
    }

    private fun logOnboardingIntroType(type: String) {
        MTAnalyticsAgent.logEvent(MTAnalyticsConstant.onboarding_intro_type, "type", type)
    }
}