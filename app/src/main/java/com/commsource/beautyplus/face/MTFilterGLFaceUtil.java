package com.commsource.beautyplus.face;

import android.graphics.PointF;
import android.graphics.RectF;
import android.util.Log;

import com.meitu.core.filtergl.facedata.MTFilterFaceDataJNI;
import com.meitu.core.types.FaceData;
import com.meitu.library.util.Debug.Debug;
import com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFace;
import com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceResult;

import java.util.ArrayList;

/**
 * gl滤镜库的人脸数据结构工具类
 *
 * Created by ZHOU on 2019/3/15.
 */

public class MTFilterGLFaceUtil {

    /*
     * 拍后gl滤镜库人脸数据数据转换
     * libtypes库的人脸数据 -> gl滤镜库的人脸数据
     *
     * 备注：实时预览的时候，不建议用此方法，否则会频繁的开辟内存。
     * */
    public static MTFilterFaceDataJNI convertFaceDataToMTFilterFaceData(FaceData faceData) {
        MTFilterFaceDataJNI filterFaceDataJNI = new MTFilterFaceDataJNI();
        fillMTFilterFaceDataByFaceData(faceData, filterFaceDataJNI);
        return filterFaceDataJNI;
    }

    /*
    * 填充gl滤镜库的人脸数据 （通过libtypes库的人脸数据）
    * */
    public static void fillMTFilterFaceDataByFaceData(FaceData faceData, MTFilterFaceDataJNI filterFaceDataCache) {
        if(filterFaceDataCache == null) {
            Debug.e("MTFilterGLFaceUtil", "filterFaceDataCache is empty");
            return;
        }

        if (faceData != null) {
            // 设置人脸数量
            filterFaceDataCache.setFaceCount(faceData.getFaceCount());

            // 设置
            int nFaceCount = faceData.getFaceCount();
            for (int i = 0; i < nFaceCount; i++) {
                // 设置FaceRect
                RectF rect = faceData.getNormalizedFaceRect(i);
                filterFaceDataCache.setFaceRect(i, rect.left, rect.top, rect.width(), rect.height());
                // 设置人脸点和数量
                float[] pointsArray = faceData.getFaceLandmarkRatioFloatArray(i, FaceData.LANDMARK_TYPE_2D);
                if(pointsArray.length/2 > 0) {
                    filterFaceDataCache.setPointCount2D(i, pointsArray.length/2);
                    filterFaceDataCache.setFacialLandmark2D(i, pointsArray);
                }else {
                    filterFaceDataCache.setPointCount2D(i, 0);
                }

            }
        }
    }

    /*
     * aiEngine人脸数据 -> gl滤镜库的人脸数据
     *
     * @param faceResult       aiEngine的人脸数据(ps：已经是归一化的)
     * @param mtFilterFaceData gl滤镜库的人脸数据（建议中间缓存，不要重复开辟，避免gc没有及时回收导致anr）
     * @param width 图片宽
     * @param height 图片高
     *
     * @note 滤镜库底层需要的是归一化的数据
     * */
    public static MTFilterFaceDataJNI fillMTFilterFaceDataWithAiFaceData(MTFaceResult faceResult,
                                                                         MTFilterFaceDataJNI mtFilterFaceData,
                                                                         int width, int height) {
        if (faceResult != null && faceResult.faces != null && mtFilterFaceData != null
                && width > 0 && height > 0) {
            // 设置人脸数量
            int nFaceCount = faceResult.faces.length;
            mtFilterFaceData.setFaceCount(nFaceCount);

            float[] facePoint2DFloatArray = null;

            for (int i = 0; i < nFaceCount; i++) {
                MTFace mtface = faceResult.faces[i];
                // 设置FaceRect
                mtFilterFaceData.setFaceRect(i, mtface.faceBounds.left, mtface.faceBounds.top,
                        mtface.faceBounds.width(), mtface.faceBounds.height());

                // 设置人脸点数量
                int nPointCount2D = mtface.facePoints.length;
                mtFilterFaceData.setPointCount2D(i, nPointCount2D);

                // 设置人脸点
                if(facePoint2DFloatArray == null || facePoint2DFloatArray.length != mtface.facePoints.length * 2) {
                    facePoint2DFloatArray = new float[mtface.facePoints.length * 2];
                }
                for (int j = 0; j < nPointCount2D; j++) {
                    facePoint2DFloatArray[2 * j] = mtface.facePoints[j].x;
                    facePoint2DFloatArray[2 * j + 1] = mtface.facePoints[j].y;
                }
                mtFilterFaceData.setFacialLandmark2D(i, facePoint2DFloatArray);
            }

            facePoint2DFloatArray = null; // 因为底层是拷贝走的，所以这里用完就尽可能的去让GC释放。

            return mtFilterFaceData;
        }else {
            Log.e("lier_MTFilterGLFaceUtil", " convert face data error, beacause params wrong! ");
            return null;
        }

    }

}
