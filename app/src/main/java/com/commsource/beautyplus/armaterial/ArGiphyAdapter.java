package com.commsource.beautyplus.armaterial;

import android.content.Context;

import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter;
import com.giphy.sdk.core.models.Media;

public class ArGiphyAdapter extends BaseRecyclerViewAdapter {

    private OnGiphyReadyListener onGiphyReadyListener;

    public ArGiphyAdapter(Context context) {
        super(context);
    }

    public OnGiphyReadyListener getOnGiphyReadyListener() {
        return onGiphyReadyListener;
    }

    public void setOnGiphyReadyListener(OnGiphyReadyListener onGiphyReadyListener) {
        this.onGiphyReadyListener = onGiphyReadyListener;
    }

    /**
     * Giphy图片加载成功监听
     */
    public static interface OnGiphyReadyListener {

        void onReady(Media media);

    }
}
