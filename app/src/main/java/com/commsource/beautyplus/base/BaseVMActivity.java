package com.commsource.beautyplus.base;

import android.os.Bundle;

import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.ViewModelProviders;

import com.commsource.beautyplus.BaseActivity;
import com.meitu.library.util.Debug.Debug;

import java.lang.reflect.ParameterizedType;

public abstract class BaseVMActivity<T extends BaseVm> extends BaseActivity {

    protected T mViewModel;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Debug.d("BaseVMActivity", getClass().getSimpleName() + "onCreate");
        super.onCreate(savedInstanceState);
        setContentView(getContentId());
        initVm();
        initView();
        initData();
    }

    @Override
    protected void onStart() {
        super.onStart();
        Debug.d("BaseVMActivity", getClass().getSimpleName() + "onStart");
    }

    @Override
    protected void onResume() {
        super.onResume();
        Debug.d("BaseVMActivity", getClass().getSimpleName() + "onResume");
    }

    @Override
    protected void onPause() {
        super.onPause();
        Debug.d("BaseVMActivity", getClass().getSimpleName() + "onPause");
    }

    @Override
    protected void onStop() {
        super.onStop();
        Debug.d("BaseVMActivity", getClass().getSimpleName() + "onStop");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Debug.d("BaseVMActivity", getClass().getSimpleName() + "onDestroy");
    }

    protected abstract int getContentId();

    private void initVm() {
        try {
            ParameterizedType pt = (ParameterizedType) getClass().getGenericSuperclass();
            // noinspection unchecked
            Class<T> clazz = (Class<T>) pt.getActualTypeArguments()[0];
            mViewModel = ViewModelProviders.of(this).get(clazz);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Lifecycle lifecycle = getLifecycle();
        lifecycle.addObserver(mViewModel);
    }

    protected abstract void initView();

    protected abstract void initData();
}
