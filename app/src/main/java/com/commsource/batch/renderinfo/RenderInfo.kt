package com.commsource.batch.renderinfo

import java.io.Serializable
import java.util.UUID


/**
 *  RenderInfo 对象最终会作为草稿直接存储对象。
 *  请注意区分其属性是否可以被序列化。
 */
abstract class RenderInfo : Serializable, Cloneable {

    abstract val infoType: Int

    // 每一个RenderInfo的Key
    @Transient
    var infoKey: String = UUID.randomUUID().toString()

    @Transient
    var pictureEditEffect: PictureEditEffect? = null

    // 效果需要重新加载
    @Transient
    private var effectNeedReload: Boolean? = true

    // 效果参数变更
    @Transient
    private var effectParamsChange: Boolean? = true

    @Transient
    var isInitialed: Boolean = false

    @Synchronized
    fun isNeedUpdateParams(): Boolean {
        val value = effectParamsChange
        effectParamsChange = null
        return value ?: false
    }

    @Synchronized
    fun setEffectParamsState(isNeedUpdate: Boolean, whenNullCanSet: Boolean = false) {
        if (isNeedUpdate) {
            effectParamsChange = true
        } else {
            if (whenNullCanSet) {
                if (effectParamsChange == null) {
                    effectParamsChange = false
                }
            } else {
                effectParamsChange = false
            }
        }
    }

    @Synchronized
    fun isNeedReloadEffect(): Boolean {
        val value = effectNeedReload
        effectNeedReload = null
        return value ?: false
    }

    @Synchronized
    fun setEffectReloadState(isNeedReload: Boolean, whenNullCanSet: Boolean = false) {
        if (isNeedReload) {
            effectNeedReload = true
            effectParamsChange = true
        } else {
            if (whenNullCanSet) {
                if (effectNeedReload == null) {
                    effectNeedReload = false
                }
            } else {
                effectNeedReload = false
            }
        }
    }


    abstract fun onRelease()

    fun doRelease() {
        onRelease()
        isInitialed = false
        setEffectReloadState(true)
    }


    fun doInitialIfNeed(effect: PictureEditEffect, isWillDisplay: Boolean): Boolean {
        if (isInitialed) return false
        pictureEditEffect = effect
        onInitial(effect, isWillDisplay)
        if (isWillDisplay) {
            isInitialed = true
        }
        return true
    }

    open fun onInitial(effect: PictureEditEffect, isWillDisplay: Boolean) {

    }

    /**
     * 检查有效性。主要是草稿 /作品恢复的时候
     */
    open suspend fun checkValid(): Boolean {
        return true
    }

    /**
     * 拷贝一份数据
     */
    open fun copyInfo(cleanGenEffect: Boolean = false): Any {
        return clone()
    }

    open fun isNeedPro(): Boolean {
        return false
    }

    open fun clearBuildGenEffect() {}


}