package com.commsource.batch.function.crop

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.commsource.batch.BatchEditViewModel
import com.commsource.beautyplus.databinding.FragmentCropBinding
import com.commsource.beautyplus.fragment.BaseFragment
import com.commsource.camera.xcamera.cover.bottomFunction.BaseBottomSubFragment
import com.commsource.widget.FastCenterScrollLayoutManager
import com.commsource.easyeditor.entity.CropEnum
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.studio.effect.FlipFixRotateEnum
import com.commsource.studio.function.composition.CropRotateViewHolder
import com.commsource.studio.function.composition.CropViewModel
import com.commsource.studio.function.composition.FlipFixRotateViewHolder
import com.commsource.studio.function.composition.MultiCropFragment
import com.commsource.studio.sub.PointDivideItemDecoration
import com.commsource.widget.list.XRecyclerViewAdapter

class BatchSubCropFragment : BaseFragment() {

    val viewbinding by lazy { FragmentCropBinding.inflate(layoutInflater) }

    val adapter by lazy { XRecyclerViewAdapter(requireContext(), lifecycleScope) }

    val cropViewModel by lazy { ViewModelProvider(ownerActivity)[BatchCropViewModel::class.java] }

    val batchEditViewModel by lazy {
        ViewModelProvider(ownerActivity)[BatchEditViewModel::class.java]
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return viewbinding.root
    }

    private fun initialUI() {
        viewbinding.rv.addItemDecoration(PointDivideItemDecoration(2))
        viewbinding.rv.layoutManager =
            FastCenterScrollLayoutManager(requireContext())
        viewbinding.rv.adapter = adapter

        // 选中裁剪比例。
        adapter.setOnEntityClickListener(CropEnum::class.java) { position, entity ->
            adapter.currentSelectEntity = entity
            cropViewModel.onSelectCropEnum(entity)
            false
        }
    }

    override fun onResume() {
        super.onResume()
        if (cropViewModel.isInEditCrop()) {
            batchEditViewModel.fetchCurrentEditEffect()?.fetchPicRenderInfo()?.let {
                adapter.currentSelectEntity = CropEnum.fetchCropEnumById(it.cropEnumId)
            }
        }
    }

    private fun initialObserver() {
        batchEditViewModel.picEffectChangeEvent.observe(viewLifecycleOwner) {
            // 重新展示画幅比例
            cropViewModel.initCropEnumList()
        }

        cropViewModel.cropEnumListEvent.observe(viewLifecycleOwner) {
            it?.let {
                adapter.beginTransaction().addItems(it, CropRotateViewHolder::class.java)
                    .commit(withAnim = false) {
                        if (cropViewModel.isInEditCrop()) {
                            batchEditViewModel.fetchCurrentEditEffect()
                                ?.fetchPicRenderInfo()?.let {
                                    adapter.currentSelectEntity = CropEnum.fetchCropEnumById(it.cropEnumId)
                                }
                            adapter.currentPosition.takeIf { it >= 0 }?.let {
                                viewbinding.rv.smoothScrollToPosition(it)
                            }
                        }
                    }
            }
        }
        // 关闭通知
        cropViewModel.showCropAdjustViewEvent.observe(viewLifecycleOwner) {
            if (!it) {
                adapter.currentSelectEntity = null
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        initialUI()
        initialObserver()
    }

}