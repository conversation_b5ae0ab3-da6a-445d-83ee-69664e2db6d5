package com.commsource.videostudio.func.music

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.commsource.beautyplus.R
import com.commsource.beautyplus.util.PathUtil
import com.commsource.repository.LoadState
import com.commsource.repository.child.MusicRepository
import com.commsource.util.ErrorNotifier
import com.commsource.util.LiveUiState
import com.commsource.util.text
import com.commsource.videostudio.util.AmplitudeManager
import com.commsource.widget.mask.MaskType
import com.meitu.common.utils.ToastUtils
import com.meitu.library.util.net.NetUtils
import com.meitu.template.bean.MusicMaterial
import com.meitu.template.bean.MusicTag
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 音乐ViewModel
 */
class MusicViewModel(application: Application) : AndroidViewModel(application) {

    companion object {
        //收藏tag
        const val CollectTagId = "collectTagId"
        const val RecommendTagId = "RecommendTagId"
    }

    var visibleMaterialId: String? = null
    var visibleTagId: String? = null

    lateinit var amplitudeManager: AmplitudeManager

    private val _dataEvent: MutableSharedFlow<MusicUiState> = MutableSharedFlow(replay = 1)
    val dataEvent: StateFlow<MusicUiState> = _dataEvent.stateIn(
        scope = viewModelScope, started = SharingStarted.WhileSubscribed(),
        MusicUiState(MaskType.Loading, emptyList())
    )

    /**
     * 收藏状态更新
     */
    val collectUpdateEvent: MutableLiveData<MusicPageUiState> = MutableLiveData()

    /**
     * 某个素材更新事件
     */
    val materialUpdateEvent: MutableLiveData<MusicMaterial> = MutableLiveData()

    /**
     * 音乐选中状态套用事件
     */
    val applyEvent: MutableLiveData<LiveUiState<MusicMaterial>> = MutableLiveData()

    /**
     * 套用素材事务
     */
    val applyItemTransactionEvent: MutableSharedFlow<MusicItemUiState?> =
        MutableSharedFlow(replay = 0)

    /**
     * 慢动作页面选择
     */
    private val _slomoPageChangeFromUserEvent: MutableSharedFlow<Int> =
        MutableSharedFlow<Int>(replay = 0, 1)

    /**
     * 页面切换
     */
    val slomoPageChangeFromUserEvent: StateFlow<Int?> =
        _slomoPageChangeFromUserEvent.stateIn(viewModelScope, SharingStarted.Eagerly, null)

    val maskTypeEvent = MutableLiveData<String?>()
    var hasOnlineMaterial = false

    /**
     * 加载数据
     */
    suspend fun loadData(
        locationPage: Int = selectPage
    ) =
        withContext(Dispatchers.IO) {
            //二者数据合并返回
            MusicRepository.loadAllMusicTag().zip(MusicRepository.loadAllMusic()) { tags, _items ->
                val items = ArrayList<MusicMaterial>().apply {
                    _items.forEach {
                        if (it.videoEnable()) {
                            add(it)
                        }
                    }
                }
                hasOnlineMaterial = items.isNotEmpty()
                val maskType =
                    if (tags.isEmpty() && items.isEmpty()) MaskType.NetError else null
                MusicUiState(maskType, ArrayList<MusicPageUiState>().apply {
                    //添加收藏页面
                    val collectTag = MusicTag().apply {
                        name = R.string.collect.text()
                        onlineId = CollectTagId
                    }
                    val recommendTag = MusicTag().apply {
                        name = R.string.filter_recommend.text()
                        onlineId = RecommendTagId
                    }
                    add(MusicPageUiState(collectTag, ArrayList<MusicItemUiState>().apply {
                        addAll(items.filter { it.isCollected() }
                            .sortedByDescending { it.collectTime }
                            .map { MusicItemUiState(collectTag, it, null) })
                    }))
                    add(MusicPageUiState(recommendTag, ArrayList<MusicItemUiState>().apply {
                        addAll(items.filter { it.isRecommend() }.sortedBy { it.listFeaturedSort }
                            .map { MusicItemUiState(recommendTag, it, null) })
                    }))
                    //普通tag页面
                    tags.sortedBy { it.sort }.forEach { tag ->
                        if (tag.childs.isNullOrEmpty()) {
                            return@forEach
                        }
                        add(MusicPageUiState(tag, ArrayList<MusicItemUiState>().apply {
                            tag.childs?.forEach { child ->
                                items.find { it.onlineId == child.onlineId }?.let {
                                    add(MusicItemUiState(tag, it, null))
                                }
                            }
                        }))
                    }
                })
            }.zip(MusicRepository.getLinkStateFlow()) { musicUiState, hasLinked ->
                val maskType = when {
                    !hasLinked -> MaskType.NetError
                    else -> when {
                        musicUiState.pages.isEmpty() -> MaskType.Empty
                        else -> null
                    }
                }
                musicUiState.maskType = maskType
                musicUiState
            }.collect {
                if (locationPage >= 0) {
                    selectPage = locationPage
                }

                if (hasOnlineMaterial) {
                    maskTypeEvent.postValue(null)
                } else {
                    if (MusicRepository.isMaterialLoading) {
                        maskTypeEvent.postValue(MaskType.Loading)
                    } else {
                        maskTypeEvent.postValue(MaskType.NetError)
                    }
                }

                _dataEvent.emit(it)
                if (visibleMaterialId != null) {
                    visibleMusic(visibleMaterialId, visibleTagId, amplitudeManager)
                    <EMAIL> = null
                    <EMAIL> = null
                }
            }
        }

    var autoApplyMusicMaterial: MusicItemUiState? = null

    var selectPage: Int = 0

    /**
     * 选中界面
     */
    fun selectPage(page: Int) {
        resetAutoApply()
        this.selectPage = page
        viewModelScope.launch {
            _slomoPageChangeFromUserEvent.emit(page)
        }
    }

    /**
     * 重制自动套用
     */
    fun resetAutoApply() {
        autoApplyMusicMaterial = null
    }

    /**
     * 套用慢动作素材
     */
    fun applyItem(
        musicItemUiState: MusicItemUiState?,
        amplitudeManager: AmplitudeManager,
        isAutoPlay: Boolean = true
    ) {
        when {
            musicItemUiState == null -> {
                applyEvent.value = null
            }
            musicItemUiState.data.isDownloading() -> {
                MusicRepository.cancelDownload(musicItemUiState.data)
            }
            musicItemUiState.data.isDownloaded() -> {
                resetAutoApply()
                if (musicItemUiState.audioVolumeInfo == null) {
                    viewModelScope.launch(Dispatchers.IO) {
                        musicItemUiState.audioVolumeInfo = amplitudeManager.loadAmplitudeInfo(
                            PathUtil.getMusicPath(musicItemUiState.data)
                        )
                        applyEvent.postValue(
                            LiveUiState(
                                musicItemUiState.data,
                                fromUser = isAutoPlay
                            )
                        )
                    }
                } else {
                    applyEvent.value = LiveUiState(musicItemUiState.data, fromUser = isAutoPlay)
                }
            }
            !musicItemUiState.data.isDownloading() -> {
                if (!NetUtils.canNetworking()) {
                    ErrorNotifier.showNetworkErrorToast()
                    return
                }
                autoApplyMusicMaterial = musicItemUiState
                MusicRepository.requestDownload(musicItemUiState.data)
            }
            else -> return
        }
    }

    /**
     * 套用Item
     */
    fun applyItemTransaction(musicItemUiState: MusicItemUiState) {
        viewModelScope.launch {
            applyItemTransactionEvent.emit(musicItemUiState)
        }
    }

    /**
     * 点击更新收藏状态
     */
    suspend fun clickAndUpdateCollect(uiState: MusicItemUiState) {
        uiState.data.collectState = 1 - uiState.data.collectState
        withContext(Dispatchers.Main) {
            if (uiState.data.isCollected()) {
                ToastUtils.showShortToast(R.string.v_favorite_on)
            } else {
                ToastUtils.showShortToast(R.string.v_favorite_off)
            }
        }
        uiState.data.collectTime = System.currentTimeMillis()
        MusicRepository.updateMaterial(uiState.data)//数据库更新
        dataEvent.first().let {
            it.pages.find { it.tag.onlineId == CollectTagId }?.let {
                //更新items
                it.items = ArrayList<MusicItemUiState>(it.items).apply {
                    if (uiState.data.isCollected()) {
                        add(uiState.copy(tag = it.tag))
                    } else {
                        find { it.data == uiState.data }?.let {
                            remove(it)
                        }
                    }
                }.sortedByDescending { it.data.collectTime }
                it.checkMaskState()
                collectUpdateEvent.postValue(it)
            }
        }
        materialUpdateEvent.postValue(uiState.data)
    }

    /**
     * 某个音乐滚动到可见
     */
    var visiblePair: Pair<String, Int>? = null

    /**
     * 曝光某个慢动作音乐素材
     */
    private fun visibleMusic(
        materiaId: String?,
        tagId: String?,
        amplitudeManager: AmplitudeManager
    ) {
        viewModelScope.launch {
            var musicItemUiState: MusicItemUiState? = null
            var tagIndex: Int = 0
            var itemIndex: Int = 0
            kotlin.run {
                val pages = _dataEvent.first().pages
                pages.forEachIndexed { index, musicPageUiState ->
                    musicPageUiState.items.find { it.data.onlineId == materiaId && musicPageUiState.tag.onlineId == tagId }
                        ?.let {
                            tagIndex = index
                            itemIndex = musicPageUiState.items.indexOf(it)
                            musicItemUiState = it
                            return@run
                        }
                }
                pages.forEachIndexed { index, musicPageUiState ->
                    musicPageUiState.items.find { it.data.onlineId == materiaId }
                        ?.let {
                            tagIndex = index
                            itemIndex = musicPageUiState.items.indexOf(it)
                            musicItemUiState = it
                            return@run
                        }
                }
            }
            //找到素材后 滚动到目标
            musicItemUiState?.let {
                //滚动到可见
                visiblePair = Pair(it.tag.onlineId, itemIndex)
                //选中page
                selectPage(tagIndex)
                delay(200)
                //选中当前音乐
                applyItem(it, amplitudeManager, isAutoPlay = false)
            }
        }
    }

    init {
        //首次加载数据
        viewModelScope.launch {
            delay(300)
            MusicRepository.getLinkStateFlow().collect {
                loadData(locationPage = 1)
            }
        }
    }

}