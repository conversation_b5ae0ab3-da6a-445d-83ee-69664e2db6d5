package com.commsource.videostudio.func.subfunc.anim

import com.commsource.beautyplus.R
import com.commsource.repository.MaterialRepository
import com.commsource.repository.OnlineLocalMaterialCompator
import com.commsource.repository.Timing
import com.commsource.repository.VersionControlPoint
import com.commsource.util.text
import com.commsource.videostudio.bean.clips.MaterialAnimType
import com.meitu.common.AppContext
import com.meitu.http.HttpResult
import com.meitu.library.util.net.NetUtils
import com.meitu.room.database.DBHelper
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.suspendCancellableCoroutine


/**
 * 这个类仅仅作为数据管理使用。
 * 不在这里面做缓存啥的。
 */

object AnimRepo : MaterialRepository(1, "AnimRepository") {

    const val materialType = "beautyplus_material_animation"

    val OPENING_CAT_ID = "1"
    val LOOP_CAT_ID = "2"
    val ENDING_CAT_ID = "3"

    private val materialAnimDao by lazy {
        DBHelper.dataBase.materialAnimDao
    }

    private var needLoadOnLineData = true

    private val animComparator by lazy {
        object :
            OnlineLocalMaterialCompator<HttpResult<List<MaterialAnim>>, MaterialAnim, MaterialAnim>(
                materialType, deliverMode = 3
            ) {
            override fun getCompareLocalMaterials(): List<MaterialAnim> {
                return materialAnimDao.loadAllEnableEntity()
            }

            override fun convertRoomEntity(onlineData: List<MaterialAnim>): List<MaterialAnim> {
                val vStickersList = ArrayList<MaterialAnim>()
                onlineData.forEachIndexed { sort, anim ->
                    anim.onlineSort = sort
                    anim.parentId = when (anim.animationType) {
                        MaterialAnimType.ANIM_ENTER -> OPENING_CAT_ID
                        MaterialAnimType.ANIM_EXIT -> ENDING_CAT_ID
                        else -> LOOP_CAT_ID
                    }
                    vStickersList.add(anim)
                }
                return vStickersList
            }

            override fun onCompareResult(
                inserts: List<MaterialAnim>?,
                update: List<MaterialAnim>?,
                remove: List<MaterialAnim>?
            ) {
                inserts?.let { materialAnimDao.insertAll(it.toTypedArray()) }
                update?.let { materialAnimDao.updateAll(it.toTypedArray()) }
                remove?.let { materialAnimDao.deleteAll(it.toTypedArray()) }
            }

            override fun onErrorResponse(throwable: Throwable?) {
                super.onErrorResponse(throwable)
                needLoadOnLineData = true
            }
        }
    }

    override fun getLinkStateFlow(): Flow<Boolean> {
        return animComparator.linkStateEvent
    }

    override fun onBuildVersionControlPoint(list: MutableList<VersionControlPoint>) {
//        list.add(object : VersionControlPoint(1) {
//            override fun onUpdate() {
//                buildInternalGroup().forEach {
//                    materialAnimCatDao.loadEntity(it.id)?.let {
//                        materialAnimCatDao.update(it)
//                    } ?: kotlin.run {
//                        materialAnimCatDao.insert(it)
//                    }
//                }
//            }
//        })
    }

    override fun onLoadTiming(timing: Int) {
        if (timing == Timing.VIDEO_ALBUM_TIMING || timing == Timing.VIDEO_STUDIO_TIMING) {
            loadOnlineData()
        }
    }

    private fun loadOnlineData() {
        if (needLoadOnLineData && NetUtils.canNetworking(AppContext.context)) {
            needLoadOnLineData = false
            animComparator.executeHttpCompare()
        }
    }

    private fun fetchInternalCategory(): List<MaterialAnimCategory> {
        return mutableListOf<MaterialAnimCategory>().apply {
            add(MaterialAnimCategory().apply {
                id = OPENING_CAT_ID
                onlineSort = 1
                name = R.string.v_anim_opening.text()
            })
            add(MaterialAnimCategory().apply {
                id = ENDING_CAT_ID
                onlineSort = 2
                name = R.string.v_anim_ending.text()
            })
            add(MaterialAnimCategory().apply {
                id = LOOP_CAT_ID
                onlineSort = 3
                name = R.string.v_anim_loop.text()
            })
        }
    }


    /**
     * 获取组合完成的贴纸数据。
     */
    suspend fun getCombineMaterialAnimData() =
        suspendCancellableCoroutine<List<MaterialAnimCategory>?> {
            val tempStickers = materialAnimDao.loadAllEnableEntity().sortedBy { it.onlineSort }
            val tempCats = fetchInternalCategory().sortedBy { it.onlineSort }
            // 拼接数据。
            tempCats.forEach { category ->
                val tempList = mutableListOf<MaterialAnim>()
                tempList.addAll(tempStickers.filter { it.parentId == category.id })
                category.anims = tempList
            }
            it.resumeWith(Result.success(tempCats.filter { it.anims?.isNotEmpty() == true }))
        }


    suspend fun queryMaterialAnim(id: String?) = suspendCancellableCoroutine<MaterialAnim?> {
        it.resumeWith(Result.success(materialAnimDao.loadEntity(id)))
    }

    suspend fun updateMaterialAnim(sti: ElementAnim) = suspendCancellableCoroutine<Void> {
        materialAnimDao.update(sti as MaterialAnim)
    }

    override fun checkRelink(): Boolean {
        loadOnlineData()
        return super.checkRelink()
    }
}