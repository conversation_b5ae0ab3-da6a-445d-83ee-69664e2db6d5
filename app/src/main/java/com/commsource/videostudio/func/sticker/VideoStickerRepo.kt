package com.commsource.videostudio.func.sticker

import androidx.lifecycle.MutableLiveData
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.repository.LoadState
import com.commsource.repository.MaterialRepository
import com.commsource.repository.OnlineLocalMaterialCompator
import com.commsource.repository.Timing
import com.commsource.repository.VersionControlPoint
import com.commsource.studio.DecorateConstant
import com.commsource.util.ThreadExecutor
import com.meitu.common.AppContext
import com.meitu.http.HttpResult
import com.meitu.library.util.net.NetUtils
import com.meitu.room.database.DBHelper
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.suspendCancellableCoroutine


/**
 * 这个类仅仅作为数据管理使用。
 * 不在这里面做缓存啥的。
 */

object VideoStickerRepo : MaterialRepository(1, "VStickerRepository") {

    const val VSTICKER_HISTORY_ID = "-8"
    const val VSTICKER_RECOMMEND_ID = "-2"

    const val materialType = "beautyplus_dynamic_sticker_tag"

    private val vStickerDao by lazy {
        DBHelper.dataBase.vStickerDao
    }

    private val vStickerCatDao by lazy {
        DBHelper.dataBase.vStickerCategoryDao
    }

    val loadStateEvent = NoStickLiveData<Int?>()
    var isMaterialLoading = false

    private var needLoadOnLineData = true

    private val vStickerComparator by lazy {
        object :
            OnlineLocalMaterialCompator<HttpResult<List<VStickerCategory>>, VStickerCategory, VSticker>(
                materialType, deliverMode = 3, with = "category"
            ) {
            override fun getCompareLocalMaterials(): List<VSticker> {
                return vStickerDao.loadAllEnableEntity()
            }

            override fun convertRoomEntity(
                onlineData: List<VStickerCategory>,
                category: List<VStickerCategory>
            ): List<VSticker> {
                val vStickersList = ArrayList<VSticker>()
                val sortMap = HashMap<String, HashMap<String, Int>>()
                onlineData.forEachIndexed { categorySort, cat ->
                    cat.onlineSort = categorySort
                    cat.vStickers?.forEachIndexed { index, vSticker ->
                        val map = sortMap[vSticker.id] ?: HashMap(4)
                        map[cat.id] = index
                        vSticker.categoryIds = map
                        sortMap[vSticker.id] = map
                    }
                    cat.vStickers?.let {
                        vStickersList.addAll(it)
                    }
                }
                category.filter { it.subCategory?.isNotEmpty() == true }
                    .forEachIndexed { index, cat ->
                        cat.onlineSort = index
                        cat.subCategory?.forEachIndexed { i, subCat ->
                            subCat.onlineSort = i
                            subCat.catLevel = 1
                            subCat.parentId = cat.id
                        }
                    }
                vStickerCategoryComparator.executeDataCompare(category)
                return vStickersList
            }

            override fun onCompareResult(
                inserts: List<VSticker>?,
                update: List<VSticker>?,
                remove: List<VSticker>?
            ) {
                inserts?.let { vStickerDao.insertAll(it.toTypedArray()) }
                update?.let { vStickerDao.updateAll(it.toTypedArray()) }
                remove?.let { vStickerDao.deleteAll(it.toTypedArray()) }

                isMaterialLoading = false
                loadStateEvent.postValue(LoadState.SUCCEED)
            }

            override fun onErrorResponse(throwable: Throwable?) {
                super.onErrorResponse(throwable)
                needLoadOnLineData = true

                isMaterialLoading = false
                loadStateEvent.postValue(LoadState.FAILED)
            }
        }
    }

    private val vStickerCategoryComparator by lazy {
        object :
            OnlineLocalMaterialCompator<HttpResult<List<VStickerCategory>>, VStickerCategory, VStickerCategory>() {
            override fun getCompareLocalMaterials(): List<VStickerCategory> {
                return vStickerCatDao.loadAllEnableEntity()
            }

            override fun convertRoomEntity(onlineData: List<VStickerCategory>): List<VStickerCategory> {
                val list = ArrayList<VStickerCategory>()
                onlineData.filter { it.subCategory?.isNotEmpty() == true }.forEach {
                    list.add(it)
                    it.subCategory?.let { list.addAll(it) }
                }
                return list
            }

            override fun onCompareResult(
                inserts: List<VStickerCategory>?,
                update: List<VStickerCategory>?,
                remove: List<VStickerCategory>?
            ) {
                inserts?.let { vStickerCatDao.insertAll(it.toTypedArray()) }
                update?.let { vStickerCatDao.updateAll(it.toTypedArray()) }
                remove?.let { vStickerCatDao.deleteAll(it.toTypedArray()) }
            }

            override fun onErrorResponse(throwable: Throwable?) {
                super.onErrorResponse(throwable)
            }
        }
    }


    private fun buildInternalGroup(): List<VStickerCategory> {
        return mutableListOf<VStickerCategory>().apply {
            add(VStickerCategory().apply {
                internalState = DecorateConstant.INTERNAL
                id = VSTICKER_RECOMMEND_ID
                catLevel = 1
                onlineSort = -1
            })
            add(VStickerCategory().apply {
                internalState = DecorateConstant.INTERNAL
                id = VSTICKER_HISTORY_ID
                catLevel = 1
                onlineSort = -2
            })
        }
    }

    override fun getLinkStateFlow(): Flow<Boolean> {
        return vStickerComparator.linkStateEvent
    }

    override fun onBuildVersionControlPoint(list: MutableList<VersionControlPoint>) {
        list.add(object : VersionControlPoint(1) {
            override fun onUpdate() {
                ThreadExecutor.executeSlowTask("insertVideoStickerInner") {
                    buildInternalGroup().forEach {
                        vStickerCatDao.loadEntity(it.id)?.let {
                            vStickerCatDao.update(it)
                        } ?: kotlin.run {
                            vStickerCatDao.insert(it)
                        }
                    }
                }
            }
        })
    }

    override fun onLoadTiming(timing: Int) {
        if (timing == Timing.VIDEO_ALBUM_TIMING || timing == Timing.VIDEO_STUDIO_TIMING) {
            loadOnlineData()
        }
    }

    private fun loadOnlineData() {
        if (needLoadOnLineData && NetUtils.canNetworking(AppContext.context)) {
            needLoadOnLineData = false
            vStickerComparator.executeHttpCompare() {
                isMaterialLoading = true
                loadStateEvent.postValue(LoadState.LOADING)
            }
        }
    }


    /**
     * 获取组合完成的贴纸数据。
     */
    fun getCombineVStickerData(): List<VStickerCategory>? {
        val tempStickers = vStickerDao.loadAllEnableEntity()
        val allCats = vStickerCatDao.loadAllEnableEntity()
        val tempSubCats = allCats.filter { it.catLevel == 1 }
        val sortedCat = tempSubCats.sortedWith { o1, o2 ->
            if (o1.parentId == o2.parentId) {
                o1.onlineSort.compareTo(o2.onlineSort)
            } else {
                val sort1 =
                    if (o1.internalState == 1) o1.onlineSort else allCats.find { it.id == o1.parentId }?.onlineSort
                        ?: 0
                val sort2 =
                    if (o2.internalState == 1) o2.onlineSort else allCats.find { it.id == o2.parentId }?.onlineSort
                        ?: 0
                //比对父分类的顺序。
                sort1.compareTo(sort2)
            }
        }
        // 拼接数据。
        sortedCat.forEach { category ->
            category.vStickers = buildNewList(tempStickers, category.id).toMutableList()
        }

        val result =
            sortedCat.filter { it.internalState == DecorateConstant.INTERNAL || it.vStickers?.isNotEmpty() == true }
        return if (result.size == 2 && result.flatMap { it.vStickers ?: emptyList() }.isEmpty()) {
            null
        } else {
            result
        }
    }


    private fun buildNewList(srcList: List<VSticker>, catId: String): List<VSticker> {
        val tempList = mutableListOf<VSticker>()
        when (catId) {
            VSTICKER_HISTORY_ID -> {
                srcList.filter { it.historyTime > 0 }.sortedByDescending { it.historyTime }.take(20)
                    .forEach {
                        tempList.add(it.clone().apply { useCategory = VSTICKER_HISTORY_ID })
                    }
            }
            VSTICKER_RECOMMEND_ID -> {
                srcList.filter { it.recommendState == DecorateConstant.RECOMMEND }
                    .sortedBy { it.recommendTime }.forEach {
                        tempList.add(it.clone().apply { useCategory = VSTICKER_RECOMMEND_ID })
                    }
            }
            else -> {
                srcList.filter { it.categoryIds?.containsKey(catId) == true }
                    .sortedBy { it.categoryIds?.get(catId) ?: 0 }.apply {
                    forEach {
                        tempList.add(it.clone().apply { useCategory = catId })
                    }
                }
            }
        }
        return tempList
    }

    override fun checkRelink(): Boolean {
        loadOnlineData()
        return super.checkRelink()
    }


    suspend fun updateVSticker(sti: VSticker) = suspendCancellableCoroutine<Unit> {
        it.resumeWith(Result.success(vStickerDao.update(sti)))
    }

    suspend fun queryVSticker(id: String?) = suspendCancellableCoroutine<VSticker?> {
        it.resumeWith(Result.success(vStickerDao.loadEntity(id)))
    }
}