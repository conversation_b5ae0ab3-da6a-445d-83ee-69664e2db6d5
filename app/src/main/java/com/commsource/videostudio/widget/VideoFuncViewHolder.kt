package com.commsource.videostudio.widget

import android.content.Context
import android.view.ViewGroup
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemVideoFuncBinding
import com.commsource.config.BeautyConfig
import com.commsource.studio.sub.SubModuleEnum
import com.commsource.util.SpanBuilder
import com.commsource.util.resColor
import com.commsource.util.text
import com.commsource.videostudio.func.VideoEditTabType
import com.commsource.videostudio.func.beauty.VideoBeautyConfig
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseViewHolder

/**
 * author: admin
 * Date: 2022/11/28
 * Des:
 */
class VideoFuncViewHolder(context: Context, parent: ViewGroup) :
    BaseViewHolder<VideoEditTabType>(context, parent, R.layout.item_video_func) {

    private val binding = ItemVideoFuncBinding.bind(itemView)
    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<VideoEditTabType>,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)
        item.entity.run {
            binding.ivIcon.text = item.entity.iconFont
            binding.tvName.text = item.entity.title
            if (isEnable) {
                binding.tvName.setTextColor(R.color.Gray_label_1.resColor())
                binding.ivIcon.setTextColor(R.color.Gray_label_1.resColor())
            } else {
                binding.tvName.setTextColor(R.color.color_4dEBEBEB.resColor())
                binding.ivIcon.setTextColor(R.color.color_4dEBEBEB.resColor())
            }

            if (this.needShowNewTag()) {
                binding.tvName.text = SpanBuilder.buildImageTextSpan(
                        R.drawable.red_point_radius_3,
                        item.entity.title,
                        4f
                )
            } else {
                binding.tvName.text = item.entity.title
            }
        }
    }

}