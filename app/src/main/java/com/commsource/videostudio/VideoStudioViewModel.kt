package com.commsource.videostudio

import android.app.Application
import android.graphics.Matrix
import android.graphics.PointF
import android.graphics.RectF
import android.view.View
import androidx.annotation.UiThread
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.studio.MatrixBox
import com.commsource.util.UIHelper
import com.commsource.util.dpf
import com.commsource.util.print
import com.commsource.util.safeGet
import com.commsource.videostudio.bean.*
import com.commsource.videostudio.bean.clips.*
import com.commsource.videostudio.func.StudioFunctionViewModel
import com.commsource.videostudio.func.SubFunction
import com.commsource.videostudio.func.VideoEditTabType
import com.commsource.videostudio.layer.VideoStudioContainer
import com.commsource.videostudio.layer.VisibleAreaHelp
import com.commsource.videostudio.timeline.OnTimelineEventListener
import com.commsource.videostudio.viewmodel.EffectViewModel
import com.meitu.library.util.device.DeviceUtils
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch

/**
 * author: admin
 * Date: 2022/11/17
 * Des:
 */
class VideoStudioViewModel(application: Application) : AndroidViewModel(application) {

    companion object {
        const val BEAUTYPLUS_VIDEO_ACTION_EDIT =
            "com.commsource.beautyplus.intent.action.VIDEO_EDIT"
    }

    private val timelineOperateDelegate = VideoStudioOperateDelegate(this)

    /**
     * 时间片段信息管理
     */
    val infoManager = TimelineInfoManager(viewModelScope, timelineOperateDelegate)

    /**
     * 面板状态
     */
    lateinit var functionViewModel: StudioFunctionViewModel

    /**
     * 效果ViewModel
     */
    lateinit var effectViewModel: EffectViewModel

    lateinit var canvasContainer: VideoStudioContainer

    val goProEvent = MutableLiveData<Boolean>()

    @VideoStudioEnterSource.VideoSource
    var comeFromSource: String? = null

    val showLoadingEvent = MutableLiveData<Boolean>()//展示Loading事件。
    var screenTouchEnable = true

    /**
     * 当前选中片段事件
     */
    val selectClipEvent = MutableLiveData<TimelineClipInfo?>()

    /**
     * 返回事件
     */
    val onBackEvent = NoStickLiveData<Boolean>()

    /**
     * 点击banner订阅
     */
    val clickBannerProEvent = MutableLiveData<Boolean>()

    /**
     * 刷新手势层选中片段
     */
    val invalidateGestureLayerSelect = MutableLiveData<Boolean>()

    /**
     * 当前选中转场事件
     */
    val selectTransitionEvent = MutableLiveData<MediaClipInfo?>()

    /**
     * 导入媒体片段
     */
    val importV1MediaEvent = MutableSharedFlow<Boolean>(replay = 0)
    val importV2MediaEvent = MutableSharedFlow<Boolean>(replay = 0)

    /**
     * 气泡展示事件
     */
    val showTipsEvent = MutableLiveData<Pair<RectF, Int>?>()
    val useTriggerSelectEvent = MutableLiveData<TimelineClipInfo?>()

    /**
     * 点击对比事件
     */
    val contrastEvent = MutableLiveData<Boolean>()

    /**
     * 文字内容编辑
     */
    val editTextContentEvent = NoStickLiveData<Boolean>()

    val adjustItemPositionEvent = MutableLiveData<Boolean>()

    /**
     * 预览区域手势事件
     */
    val gestureEvent = NoStickLiveData<Boolean>() // 正在执行手势事件


    /**
     * 时间轴片段交互事件
     */
    private var isNeedCheckSelectClip: Boolean? = null
    val onTimelineEventListener = object : OnTimelineEventListener {

        override fun onTransitionSelect(mediaClipInfo: MediaClipInfo?) {
            selectTransition(mediaClipInfo)
        }

        override fun onClipSelect(clipInfo: TimelineClipInfo?, isInMoving: Boolean) {
            if (selectTransitionEvent.value == null && clipInfo == null && functionViewModel.hasSelectVideoTab()) {
                functionViewModel.selectVideoEditTab(null)
                return
            }
            if (selectTransitionEvent.value == null && clipInfo == null && functionViewModel.subFunctionEvent.value != null) {
                functionViewModel.selectSubFunction(null)
                return
            }
            selectClip(clipInfo)
            if (useTriggerSelectEvent.value != clipInfo) {
                if (isInMoving) {
                    isNeedCheckSelectClip = true
                } else {
                    useTriggerSelectEvent.value = clipInfo
                }
            }
        }

        override fun onPinchTimeline() {
            if (effectViewModel.isPlaying()) {
                effectViewModel.pause()
            }
        }

        override fun onStartClipDrag(clipInfo: TimelineClipInfo) {
            if (effectViewModel.isPlaying()) {
                effectViewModel.pause()
            }
        }

        override fun onTimelineClipPositionChange(
            isSelect: Boolean,
            isPressPreIndicator: Boolean,
            isPressNextIndicator: Boolean,
            clipInfo: TimelineClipInfo
        ) {
            if (isSelect && clipInfo is MediaClipInfo) {
                //如果是主轴视频片段 直接更新裁剪的效果
                if (isPressPreIndicator) {
                    effectViewModel.onTrim(clipInfo, isPre = true)
                } else {
                    effectViewModel.onTrim(clipInfo, isPre = false)
                }
            } else if (clipInfo.effectItem.timelineType != TimeLineType.MAIN_TIME_LINE) {
                effectViewModel.updateEffectTimeDuration(clipInfo.effectItem)
                infoManager.checkTrimOperation()
                refreshFrame()
                effectViewModel.seekTo(effectViewModel.getCurrentPosition())
            }
        }

        override fun onStopClipDrag(clipInfo: TimelineClipInfo, effectNoChange: Boolean) {
            effectViewModel.updateEffect(clipInfo.effectItem)
            infoManager.checkTrimOperation()
            invalidateGestureLayerSelect.value = true
            refreshFrameEvent.value = true
            if (!effectNoChange) {
                effectViewModel.pushCurStateToStack()
            }
        }

        override fun onRestoreAdjustStart(
            clipInfo: MediaClipInfo,
            uiCurrentPosition: Long,
            isDelete: Boolean,
            oldIndex: Int,
            newIndex: Int,
            oldTimelineType: Int,
            newTimelineType: Int
        ) {
            //BugFix:如果选中转场 动态把转场移动到最后一个不可用 直接关闭转场页面
            if (!isDelete && oldTimelineType == newTimelineType) {
                if (infoManager.timelineInfo.mediaClips.lastOrNull() == selectTransitionEvent.value) {
                    selectTransition(null)
                }
            }
        }

        override fun onAdjustEnd(
            clipInfo: MediaClipInfo,
            uiCurrentPosition: Long,
            isDelete: Boolean,
            oldIndex: Int,
            newIndex: Int,
            oldTimelineType: Int,
            newTimelineType: Int
        ) {
            //调整模式变更 直接操作具体的时间轴操作
            viewModelScope.launch {
                if (!isDelete) {//如果是删除状态 不需要这里处理
                    if (oldTimelineType == newTimelineType) {
                        //轨道没有切换 只是index的处理
                        if (newTimelineType == TimeLineType.MAIN_TIME_LINE && oldIndex >= 0 && newIndex >= 0 && oldIndex != newIndex) {
                            //切换主轨道位置
                            effectViewModel.exchangeMediaEffect(
                                infoManager.timelineInfo.mediaClips.map { it.pixVideoMediaItem },//直接给到处理好的效果新顺序
                                oldIndex,
                                newIndex
                            )
                        } else {
                            effectViewModel.updateEffect(clipInfo.effectItem)
                        }
                    } else {
                        //存在切换轨道
                        //检查转场 需要去除转场
                        effectViewModel.adjustMediaItemTrack(
                            clipInfo.pixVideoMediaItem,
                            infoManager.timelineInfo.mediaClips.map { it.pixVideoMediaItem },
                            oldTimelineType = oldTimelineType,
                            newTimelineType = newTimelineType
                        )
                        infoManager.reorderV1Track()
                        //媒体片段轨道处理后 动态选中状态处理
                        if (isSelectClip(clipInfo)) {
                            selectClip(clipInfo) // 轨道变更需要重新触发一遍选中。
                            if (isNeedCheckSelectClip == true) {
                                useTriggerSelectEvent.postValue(clipInfo)
                            }
                            invalidateGestureLayerSelect.postValue(true)
                            refreshFrameEvent.postValue(true)
                        }
                        effectViewModel.seekTo(uiCurrentPosition)
                    }
                }
            }
        }

        override fun onPressClipPreIndicator(clipInfo: TimelineClipInfo) {
            if (effectViewModel.isPlaying()) {
                effectViewModel.pause()
            }
            if (clipInfo is MediaClipInfo) {
                effectViewModel.startTrim(clipInfo, isPre = true)
            }
        }

        override fun onPressClipNextIndicator(clipInfo: TimelineClipInfo) {
            if (effectViewModel.isPlaying()) {
                effectViewModel.pause()
            }
            if (clipInfo is MediaClipInfo) {
                effectViewModel.startTrim(clipInfo, isPre = false)
            }
        }

        override fun onStopIndicatorGesture(
            clipInfo: TimelineClipInfo,
            uiCurrentPosition: Long,
            isPressPreIndicator: Boolean
        ) {
            //如果当前片段的时长小于1000
            //那么取消这个片段前后的转场
            if (clipInfo is MediaClipInfo) {
                effectViewModel.endTrim(clipInfo, isPre = isPressPreIndicator)
            }
            if (infoManager.inV1Track(clipInfo) && clipInfo.duration <= 1000) {
                viewModelScope.launch {
                    clipInfo.takeIf { it is MediaClipInfo }?.let { current ->
                        current as MediaClipInfo
                        current.pixVideoMediaItem.transition.transitionMaterial = null
                        effectViewModel.applyOrReplaceSingleTransition(current.pixVideoMediaItem)
                        effectViewModel.getV1TrackMedia()?.let { list ->
                            list.indexOf(current.pixVideoMediaItem).takeIf { it >= 0 }?.let {
                                list.safeGet(it - 1)?.let {
                                    it.transition.transitionMaterial = null
                                    effectViewModel.applyOrReplaceSingleTransition(it)
                                }
                            }
                        }
                    }
                    infoManager.refreshTimeline()
                }
            }
            //更新目标效果
            effectViewModel.updateEffect(clipInfo.effectItem)
            (effectViewModel.fetchBaseEditor(clipInfo.effectItem.effectType) as? BaseEditor<PixVideoEffectItem>)?.updateAnimationDuration(
                clipInfo.effectItem
            )
            infoManager.checkTrimOperation()
            //标签调整后 timelineSeekTo处理
            effectViewModel.seekTo(uiCurrentPosition)
            effectViewModel.pushCurStateToStack()
        }

        override fun onDeleteClipInfo(clipInfo: TimelineClipInfo) {
            //这是手势拖拽删除 已经是动画结束状态 直接删除效果
            viewModelScope.launch {
                effectViewModel.deleteItem(clipInfo.effectItem)
//                effectViewModel.pushCurStateToStack()
            }
        }

        override fun onClickImport() {
            //发起插入媒体片段处理
            effectViewModel.pause()
            viewModelScope.launch {
                importV1MediaEvent.emit(true)
            }
        }

        override fun onTimelineAdsorbTime(
            adsorbTime: Long,
            needDirectAdsorb: Boolean,
            isRightAdsorb: Boolean
        ) {
            //为了效果更加精准吸附 做的处理
            if (effectViewModel.inGestureTracking) {
                if (needDirectAdsorb) {
                    //右侧吸附为了效果问题 吸附-1毫秒
                    effectViewModel.onGestureTracking(if (isRightAdsorb) adsorbTime - 1 else adsorbTime + 1)
                } else {
                    effectViewModel.onGestureTracking(adsorbTime)
                }
            }
            refreshFrame()
        }

    }


    /**
     * 选中文字效果，且在当前帧中可见
     */
    fun isTextItemActive(): Boolean {
        val effectItem = selectClipEvent.value?.effectItem ?: return false
        return effectItem.effectType == EffectType.TEXT && effectViewModel.canVisible(effectItem)
    }

    fun getSelectTextItem(): PixVideoTextItem? {
        return selectClipEvent.value?.effectItem as? PixVideoTextItem
    }

    /**
     * 片段选中
     */
    fun selectClip(clipInfo: TimelineClipInfo?) {
        selectClipEvent.value?.takeIf { it is TextClipInfo }?.let { oldInfo ->
            oldInfo.effectItem.effectID?.let { effectId ->
                effectViewModel.effectEditor?.getAREffect(effectId)?.setSelected(false)
            }
        }

        // 选中特效
        clipInfo?.takeIf { it is TextClipInfo }?.let { newInfo ->
            newInfo.effectItem.effectID?.let { effectId ->
                effectViewModel.effectEditor?.getAREffect(effectId)?.setSelected(true)
            }
        }
        selectClipEvent.value = clipInfo
        checkVideoTabTypeAndClipState()
    }

    /**
     * 通过clipInfo判断是否选中相同片段
     */
    fun isSelectClip(clipInfo: TimelineClipInfo?): Boolean {
        if (clipInfo == null) {
            return false
        }
        return selectClipEvent.value == clipInfo || isSelectClip(clipInfo.effectItem.parentKey)
    }

    /**
     * 通过parentKey判断是否选中相同的片段
     */
    fun isSelectClip(parentKey: String?): Boolean {
        if (parentKey == null) {
            return false
        }
        return selectClipEvent.value?.effectItem?.parentKey == parentKey
    }

    /**
     * 转场选中
     */
    fun selectTransition(mediaClipInfo: MediaClipInfo?) {
        if (selectTransitionEvent.value !== mediaClipInfo) {
            selectTransitionEvent.value = mediaClipInfo
        }
        if (selectTransitionEvent.value == null && functionViewModel.isShowSubFunction(SubFunction.Transition)) {
            functionViewModel.selectSubFunction(null)
        } else if (selectTransitionEvent.value != null) {
            functionViewModel.selectSubFunction(SubFunction.Transition)
        }
    }

    val updateSelectEvent = NoStickLiveData<Boolean>()

    fun updateSelectEffect() {
        if (UIHelper.isMainThread()) {
            updateSelectEvent.value = true
        } else {
            updateSelectEvent.postValue(true)
        }
    }

    /**
     * 尝试更新选中的滤镜滑杆
     */
    @UiThread
    fun updateSelectFilterProgress(progress: Int) {
        selectClipEvent.value?.takeIf { it is FilterClipInfo }?.let {
            it as FilterClipInfo
            it.pixVideoEffectItem.alpha = progress / 100f
            effectViewModel.updateEffect(it.pixVideoEffectItem)
        }
    }

    fun getTimelineInfoByKey(key: String?): TimelineClipInfo? {
        return key?.let { infoManager.getClipInfo(it) }
    }


    fun getLayerName(item: PixVideoEffectItem): String? {
        return when {
            item.effectType == EffectType.SPECIAL_EFFECT -> "特效图层"
            item.effectType == EffectType.FILTER -> "滤镜图层"
            item.effectType == EffectType.ADJUST -> "调色图层"
            item.effectType == EffectType.MEDIA -> "媒体图层"
            item.effectType == EffectType.TEXT -> "文字图层"
            item.effectType == EffectType.MUSIC && item.subEffectType == SubEffectType.MUSIC -> "音乐图层"
            item.effectType == EffectType.MUSIC && item.subEffectType == SubEffectType.SOUND -> "音效图层"
            item.effectType == EffectType.STICKER -> "贴纸图层"
            else -> null
        }

    }

    fun focusFace(
        faceRect: RectF,
        canvasInitMatrix: MatrixBox = canvasContainer.finalCanvasInitMatrix,
        validRectF: RectF = canvasContainer.validRect
    ) {
        "focusFace faceRect = ${faceRect}".print("yyj2")
        "focusFace validRectF = ${validRectF}".print("yyj2")
        "focusFace canvasInitMatrix = ${canvasInitMatrix.matrixFloat.contentToString()}".print("yyj")
        canvasInitMatrix.matrix.mapRect(faceRect)
        val dstRect = RectF(validRectF)
        dstRect.top = DeviceUtils.getStatusHeight() + 44.dpf()
        dstRect.inset(dstRect.width() / 4, dstRect.height() / 4)
        val matrix = Matrix()
        matrix.setRectToRect(faceRect, dstRect, Matrix.ScaleToFit.CENTER)
        val matrixBox = MatrixBox(matrix)
        canvasContainer.canvasGestureController.setValidViewPortRectF(
            validRectF,
            canvasContainer.finalViewPort
        )
        val result = canvasContainer.canvasGestureController.generateValidMatrix(
            matrixBox,
            PointF(dstRect.centerX(), dstRect.centerY())
        )
        "focusFace gesture matrix before = ${canvasContainer.canvasGestureController.canvasGestureMatrix.matrixFloat.contentToString()}".print(
            "yyj2"
        )
        canvasContainer.canvasGestureController.animateMatrix(result)
        "focusFace gesture matrix after = ${result.matrixFloat.contentToString()}".print("yyj2")

    }

    val visibleAreaHelp = VisibleAreaHelp()

    /**
     * 刷新选中框
     */
    val refreshFrameEvent = MutableLiveData<Boolean>()

    fun refreshFrame() {
        refreshFrameEvent.value = true
    }

    fun addObstacleView(vararg obstacleView: View) {
        visibleAreaHelp.addObstacleView(*obstacleView)
        refreshFrameEvent.postValue(true)
    }

    fun removeObstacleView(vararg views: View) {
        visibleAreaHelp.removeObstacleView(*views)
        refreshFrameEvent.postValue(true)
    }

    /**
     * ui位置变化，需要刷新
     */
    fun updateObstacleRects() {
        visibleAreaHelp.updateObstacleRects()
        refreshFrameEvent.postValue(true)
    }

    fun setGlobalLimitArea(
        left: Int = visibleAreaHelp.globalLimitRect.left,
        top: Int = visibleAreaHelp.globalLimitRect.top,
        right: Int = visibleAreaHelp.globalLimitRect.right,
        bottom: Int = visibleAreaHelp.globalLimitRect.bottom
    ) {
        visibleAreaHelp.globalLimitRect.set(left, top, right, bottom)
    }

    /**
     * 动态检查面板状态和片段是否
     */
    fun checkVideoTabTypeAndClipState() {
        val clipInfo = selectClipEvent.value
        when {
            //在转场面板 如果当前转场被删除 或者不可用与选中转场 关闭转场界面
            functionViewModel.isShowSubFunction(SubFunction.Transition) && selectTransitionEvent.value != null -> {
                selectTransitionEvent.value?.let { selectTransition ->
                    if (clipInfo != null || !infoManager.inV1Track(selectTransition) || infoManager.timelineInfo.mediaClips.lastOrNull() == selectTransition) {
                        selectTransition(null)
                    }
                }
            }
            //检查二级面板点击空
            //选中状态和 面板不符合 收起面板
            (functionViewModel.isSelectVideoTab(VideoEditTabType.Adjust) && clipInfo !is AdjustClipInfo)
                    || (functionViewModel.isSelectVideoTab(VideoEditTabType.SpecialEffect) && clipInfo !is EffectClipInfo)
                    || (functionViewModel.isSelectVideoTab(VideoEditTabType.Filter) && clipInfo !is FilterClipInfo)
                    || (functionViewModel.isSelectVideoTab(VideoEditTabType.Sticker) && clipInfo !is StickerClipInfo)
                    || (functionViewModel.isSelectVideoTab(VideoEditTabType.Text) && clipInfo !is TextClipInfo)
                    || (functionViewModel.isSelectVideoTab(VideoEditTabType.SoundEffect) && clipInfo !is MusicClipInfo) -> {
                functionViewModel.selectVideoEditTab(null)
            }
        }
    }

    /**
     * 显示弹窗
     */
    val showLoading = Runnable {
        screenTouchEnable = false
        showLoadingEvent.postValue(true)
    }

    /**
     * 显示loading
     */
    fun showLoading(delay: Long = 300) {
        UIHelper.runOnUiThreadDelay(showLoading, delay)
    }

    /**
     * 关闭loading
     */
    fun dismissLoading() {
        screenTouchEnable = true
        UIHelper.removeCallbacks(showLoading)
        showLoadingEvent.postValue(false)
    }
}