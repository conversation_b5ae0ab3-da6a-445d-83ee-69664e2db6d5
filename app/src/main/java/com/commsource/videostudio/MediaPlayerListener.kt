package com.commsource.videostudio

import com.meitu.library.mtmediakit.constants.MTMediaPlayerStatus
import com.meitu.library.mtmediakit.listener.MTMediaPlayerListener
import com.meitu.media.mtmvcore.MTPerformanceData

open class MediaPlayerListener : MTMediaPlayerListener {


    override fun onPlayerInfoStateChange(state: MTMediaPlayerStatus?) {

    }

    override fun onPlayerProgressUpdate(currPos: Long, totalDuration: Long, startSelection: Long, endSelection: Long) {

    }

    override fun onPlayerProgressUpdateInSelectedClipWithId(clipId: Long, filePosition: Long, fileDuration: Long) {
    }

    override fun onPlayerWarn(errorType: Int, errorCode: Int) {
    }

    override fun onPlayerFailed(errorType: Int, errorCode: Int) {
    }

    override fun onPlayerSaveFailed(errorType: Int, errorCode: Int) {
    }

    override fun onPlayerPerformanceData(data: MTPerformanceData?) {

    }

    override fun onPlayerDropRate(rate: Float, mayStagnate: Boolean) {

    }

    override fun onPlayerSaveStart() {

    }

    override fun onPlayerSaveComplete() {

    }

    override fun onPlayerSaveCancel() {

    }

    override fun onPlayerSaveProgressUpdate(currPos: Long, totalDuration: Long) {

    }

    override fun onPlayerSaveWarn(errorType: Int, errorCode: Int) {
    }

    override fun onOffScreenRenderProgressUpdate(isRenderComplete: Boolean, progree: Float) {

    }

    override fun onViewSizeChange(width: Int, height: Int) {

    }

    override fun onSeekComplete() {

    }

    override fun onVideoReverseStart() {

    }

    override fun onVideoReverseProgressUpdate(currPos: Long, totalDuration: Long) {

    }

    override fun onVideoReverseComplete() {

    }

    override fun onVideoReverseCancel() {

    }

    override fun onPlayViewCreated() {

    }

    override fun onSetup() {

    }

    override fun onReleaseTimeline() {

    }
}