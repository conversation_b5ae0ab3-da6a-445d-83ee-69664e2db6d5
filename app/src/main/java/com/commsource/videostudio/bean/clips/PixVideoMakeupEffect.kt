package com.commsource.videostudio.bean.clips

/**
 * 美妆效果
 */
class PixVideoMakeupEffect(var items: List<PixVideoMakeupItem> = emptyList()) :
    PixVideoBeautyEffect() {

    override fun copy(): PixVideoMakeupEffect {
        return (super.copy() as PixVideoMakeupEffect).apply {
            items = ArrayList<PixVideoMakeupItem>().apply {
                <EMAIL> {
                    add(it.copy())
                }
            }
        }
    }
}