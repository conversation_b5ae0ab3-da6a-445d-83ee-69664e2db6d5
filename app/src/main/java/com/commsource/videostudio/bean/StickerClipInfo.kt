package com.commsource.videostudio.bean

import com.commsource.beautyplus.R
import com.commsource.util.resColor
import com.commsource.videostudio.bean.clips.PixVideoStickerItem

/**
 * 贴纸片段
 */
class StickerClipInfo(val pixVideoStickerItem: PixVideoStickerItem) :
    FocusClipInfo(pixVideoStickerItem) {

    init {
        subjectColor = R.color.color_track_sticker.resColor()
    }

    /**
     * 拉伸循环
     */
    override var loopFeedbackDurationRange: Long
        get() = pixVideoStickerItem.stickerDuration
        set(value) {}
}