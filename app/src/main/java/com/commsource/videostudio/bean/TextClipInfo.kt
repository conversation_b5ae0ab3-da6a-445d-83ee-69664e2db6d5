package com.commsource.videostudio.bean

import com.commsource.beautyplus.R
import com.commsource.util.resColor
import com.commsource.util.text
import com.commsource.videostudio.bean.clips.PixVideoTextItem

/**
 * 文字片段
 */
class TextClipInfo(val pixVideoTextItem: PixVideoTextItem) : FocusClipInfo(pixVideoTextItem) {

    init {
        subjectColor = R.color.color_track_text.resColor()
    }

    private val defaultText = R.string.t_enter_characters.text()

    override fun getDisplayName(): String {
        return pixVideoTextItem.getSelectParam()?.uiContent
            ?: pixVideoTextItem.getSelectParam()?.content
            ?: defaultText
    }
}