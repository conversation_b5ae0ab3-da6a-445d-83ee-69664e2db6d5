package com.commsource.studio

import android.content.Context
import android.opengl.GLES20
import com.commsource.easyeditor.utils.opengl.*
import com.commsource.studio.shader.ImageProgram

/**
 * 放大镜实现。
 */
class MagnifyGLView(context: Context) : GLTextureView(context), AbsEglRenderer {

    private var imageProgram: ImageProgram? = null

    /**
     * 图片FBO。
     */
    var imageFBO: FBOEntity? = null

    /**
     * 放大镜显示区域。
     */
    var magnifyRect = FloatArray(8)


    init {
        setRenderer(this)
//        eglThread.setOnSwapBufferRunnable {
//            // https://zhuanlan.zhihu.com/p/147322501 大神的解决方案，可以缓解TextureView渲染卡顿的问题，暂时在放大镜中试一下。
//            isOpaque = false;
//            isOpaque = true;
//        }
    }

    /**
     * 更新放大镜的显示区域。
     */
    fun updateMagnifyLocation(viewPortPointF: FloatArray, viewPortWidth: Float, viewPortHeight: Float, scale: Float) {
        if (width == 0 || height == 0) {
            return
        }
        // x, y 坐标换算成纹理位置
        val textureX = viewPortPointF[0] / viewPortWidth
        val textureY = 1 - viewPortPointF[1] / viewPortHeight

        // 通过x,y坐标构建正方形纹理数组，注意：FBO中纹理需要翻转
        val lengthWidth: Float = width / viewPortWidth / scale
        val lengthHeight: Float = height / viewPortHeight / scale

//        textureX = MathUtil.clamp(textureX, lengthWidth/2, 1.0f - lengthWidth/2)
//        textureY = MathUtil.clamp(textureY, lengthHeight/2, 1.0f - lengthHeight/2)

        magnifyRect[0] = textureX - lengthWidth / 2
        magnifyRect[1] = 1.0f - (textureY - lengthHeight / 2)
        magnifyRect[2] = textureX + lengthWidth / 2
        magnifyRect[3] = 1.0f - (textureY - lengthHeight / 2)
        magnifyRect[4] = textureX - lengthWidth / 2
        magnifyRect[5] = 1.0f - (textureY + lengthHeight / 2)
        magnifyRect[6] = textureX + lengthWidth / 2
        magnifyRect[7] = 1.0f - (textureY + lengthHeight / 2)
        requestRender()
    }

    override fun onAttachProvider(eglProvider: EGLProvider?) {
    }


    override fun onCreate() {
        imageProgram = ImageProgram()
        imageProgram!!.onGlResourceInit()
    }

    @GlThread
    override fun onDrawFrame() {
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, 0)
        // 清空屏幕。
        clearCanvas()
        imageFBO?.let {
            drawFboToScreen(it)
        }
    }

    override fun onSurfaceChanged(width: Int, height: Int) {
    }

    override fun onSurfaceCreated() {
    }

    override fun onDestroy() {
        imageProgram?.onGlResourceRelease()
    }

    private fun clearCanvas() {
        GLES20.glClearColor(1f, 1f, 1f, 1f)
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT or GLES20.GL_DEPTH_BUFFER_BIT)
    }

    private fun drawFboToScreen(fboEntity: FBOEntity) {
        // 底层GL可能会改视口，这边要改回来。
        GLES20.glViewport(0, 0, width, height)
        // 绘制在屏幕上。
        imageProgram?.drawTexture(fboEntity, magnifyRect)
    }

}