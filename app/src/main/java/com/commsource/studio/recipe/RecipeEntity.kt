package com.commsource.studio.recipe

import androidx.annotation.NonNull
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

//@Entity(tableName = "RECIPE_ENTITY")
class RecipeEntity {

    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "recipeId")
    var recipeId: String = ""

    /**

     * 配方所使用的Json。
     */
    @ColumnInfo(name = "json")
    var recipeJson: String = ""

    @Ignore
    constructor()

    constructor(recipeId: String, recipeJson: String) {
        this.recipeId = recipeId
        this.recipeJson = recipeJson
    }


}