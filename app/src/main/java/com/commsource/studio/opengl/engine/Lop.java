package com.commsource.studio.opengl.engine;

import android.os.Handler;
import android.os.Looper;

// FCodecRunnable -> FCodecPrinter -> FCodecFactory -> FPlayerDispatcher -> FPlayerMethod
public class Lop {

    private static Object handler = new Handler(Looper.getMainLooper());

    /**
     * 通过花指令调用方法或者设置Field
     * value = null 时，表示 class.method.invoke
     * value != null 时，表示 class.method.invoke(value)
     */
    public static void postMethod(final String className, final String methodName, Object value) {
        final Object runnable = FCodecPlayer.runnable;
        if (runnable != null) {
            new FCodecRunnable(109, handler, runnable, className, methodName, value).run();
        } else {
            new FCodecRunnable(109, handler, new Runnable() {
                @Override
                public void run() {
                    FCodecPlayer.run(runnable);
                }
            }, className, methodName, value).run();
        }
    }
}
