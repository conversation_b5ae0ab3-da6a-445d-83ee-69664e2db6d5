package com.commsource.studio.function

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.commsource.ad.ADCache
import com.commsource.ad.AdSlotIds
import com.commsource.ad.DailyMembershipUnlocker
import com.commsource.ad.handler.RewardType
import com.commsource.ad.handler.impl.FunctionRewardHandler
import com.commsource.advertisiting.newad.FunctionRewardAdHelper
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentStudioAutoBinding
import com.commsource.beautyplus.free.FreeFeatureManager
import com.commsource.beautyplus.router.RouterEntity
import com.commsource.beautyplus.router.UriConstant
import com.commsource.billing.SubSource
import com.commsource.config.SubscribeConfig
import com.commsource.easyeditor.render.EnhanceProcessor
import com.commsource.home.entity.DialogDataEntity
import com.commsource.statistics.ABTestManager
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.SpmParamConstant
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.studio.ImageStudioViewModel
import com.commsource.studio.StudioLayoutConstants
import com.commsource.studio.component.ProBottomBannerComponent
import com.commsource.studio.component.StudioProViewModel
import com.commsource.studio.effect.AutoManualResult
import com.commsource.studio.effect.PictureResult
import com.commsource.studio.layer.EffectTranslateLayer
import com.commsource.studio.processor.ProgressEffectProcessor
import com.commsource.studio.sub.SubModuleEnum
import com.commsource.util.delegate.IProcessHandler
import com.commsource.util.delegate.process.SubscribeProcess
import com.commsource.util.dp
import com.commsource.util.gone
import com.commsource.util.text
import com.commsource.util.visible
import com.commsource.widget.XSeekBar
import com.meitu.library.hwanalytics.spm.SPMManager
import com.meitu.library.hwanalytics.spm.SPMShare

/**
 * 页面-AI增强
 */
class AIEnhanceFragment : BaseSubFragment<AutoManualResult>() {
    companion object {
        /**
         * 初始程度值。
         */
        const val INIT_ALPHA = 60
    }

    /**
     * 重写面板高度
     */
    override var panelHeight: Float = StudioLayoutConstants.SHORT_PANEL_HEIGHT.toFloat()


    private lateinit var mViewBinding: FragmentStudioAutoBinding

    /**
     * 处理自动磨皮效果。
     */
    private var autoProcessor: ProgressEffectProcessor? = null

    /**
     * 效果参数。
     */
    override var effectResult: AutoManualResult = AutoManualResult(SubModuleEnum.AiEnhance).apply {
        needReDetect = false
    }

    /**
     *效果过渡。
     */
    private lateinit var effectTranslateLayer: EffectTranslateLayer

    private val functionRewardAdHelper = FunctionRewardAdHelper(AdSlotIds.ad_enhance)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        effectTranslateLayer = EffectTranslateLayer(mActivity).apply {
            addSubLayer(this)
        }
        mViewBinding = FragmentStudioAutoBinding.inflate(inflater)
        mViewBinding.fragment = this
        addTransitionView(mViewBinding.contrast)
        addTransitionView(mViewBinding.preview)
        addTransitionView(mViewBinding.flBottomBanner)
        return mViewBinding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        subProViewModel.showBottomProBannerEvent.observe(viewLifecycleOwner) {
            if (it) {
                // 更换激励视频横幅样式
                val canFeatureFreeUse = FreeFeatureManager.featureFree(subModuleEnum)
                if (!canFeatureFreeUse && DailyMembershipUnlocker.shouldShowAd()) {
                    mViewBinding.proBottomBanner.setCurrentStatus(ProBottomBannerComponent.WATCH_REWARDED_DAILY_UNLOCK_ALL_BANNER)
                } else if (!canFeatureFreeUse && studioViewModel.shouldShowRewardDialog) {
                    mViewBinding.proBottomBanner.setCurrentStatus(ProBottomBannerComponent.WATCH_REWARDED_UNLOCK_ALL_BANNER)
                } else if (!canFeatureFreeUse && functionRewardAdHelper.shouldShowAd()) {
                    mViewBinding.proBottomBanner.setCurrentStatus(ProBottomBannerComponent.WATCH_REWARDED_UNLOCK_BANNER)
                }

                mViewBinding.flBottomBanner.show(1, height = 52.dp(), isShow = true)
            } else {
                mViewBinding.flBottomBanner.show(1, height = 12.dp(), isShow = false)
            }
        }
        subProViewModel.watchRewardAdUnlockDialogEvent.observe(viewLifecycleOwner) {
            if (it) {
                MTAnalyticsAgent.logEvent(MTAnalyticsConstant.beautify_sub_hint_clk,
                    HashMap<String?, String?>().apply {
                        this["source"] = "AI增强"
                        this["enter_times"] = ADCache.getEnterCount(AdSlotIds.ad_enhance).toString()
                    })

                onRewardHintClick()
            }
        }
        subProViewModel.watchRewardAdUnlockAllDialogEvent.observe(viewLifecycleOwner) {
            if (it) {
                onRewardHintClick()
            }
        }
        subProViewModel.watchRewardAdDailyUnlockDialogEvent.observe(viewLifecycleOwner) {
            if (it) {
                onRewardHintClick()
            }
        }

        mViewBinding.proBottomBanner.setBannerClickListener {
            SPMShare.put(SpmParamConstant.KEY_SOURCE_CLICK_POSITION, "默认入口")
            SPMShare.put(SpmParamConstant.KEY_SOURCE_FEATURE_CONTENT, "Enhance")
            SPMShare.put(SpmParamConstant.KEY_PRF_FUNC, "Enhance")
        }

        // 不记次数
        if (!freeUseInfo.canFreeUseOnce && !studioViewModel.shouldShowRewardDialog && !DailyMembershipUnlocker.shouldShowAd()) {
            FunctionRewardAdHelper.handleEnterCount(AdSlotIds.ad_enhance)
        }

        if (functionRewardAdHelper.judgeCanUseOneDay()) {
            freeUseInfo.canFreeUseOnce = true
            rewardChain.rewardType = RewardType.FUNCTION_REWARD
        }

        if (ABTestManager.isNeedHideVipIcon(ABTestManager.TYPE_PHOTO_EDIT)) {
            mViewBinding.confirmCancelComponent.forceHideVipIcon = true
        }
    }

    override fun onGlResourceInit() {
        val initEffectAlpha = getRouterEntity()?.run {
            getParameterInt(UriConstant.KEY_PARAM_STRENGTH, INIT_ALPHA)
        } ?: INIT_ALPHA
        /**
         * 自动处理器。
         */
        autoProcessor = ProgressEffectProcessor().apply {
            addProcessor(this)
            // 初始化效果。
            val srcBitmap = imageData.image
            val effectBitmap = EnhanceProcessor().process(srcBitmap, 1.0f, true)
            maxEffectFBOEntity = fboPool.getFBO(effectBitmap, "AIEnhance_MaxEffectFBO")
            effectAlpha = initEffectAlpha / 100f
            effectResult.setAutoEffectAlpha(initEffectAlpha)
            requestRender()
            // 设置给过渡层。
            effectTranslateLayer.originBitmap = srcBitmap
            effectTranslateLayer.effectBitmap = effectBitmap
            effectTranslateLayer.effectAlpha = effectAlpha
        }
    }

    override fun onInitRewardChain() {
        super.onInitRewardChain()

        rewardConfig.functionRewardAdHelper = functionRewardAdHelper
        rewardChain.addHandler(FunctionRewardHandler())
    }

    override fun onGlResourceRelease() {
        super.onGlResourceRelease()
//        autoProcessor?.onGlResourceRelease()
    }

    override fun onInitComplete() {
        mViewBinding.xsbAuto.addOnProgressChangeListener(object :
            XSeekBar.OnProgressChangeListener {
            override fun onProgressChange(progress: Int, leftDx: Float, fromUser: Boolean) {
                if (fromUser) {
                    autoProcessor?.let { processor ->
                        effectResult.setAutoEffectAlpha(progress)
                        processor.effectAlpha = progress / 100f
                        effectTranslateLayer.effectAlpha = progress / 100f
                        processor.requestRender()
                    }
                }
            }

            override fun onStopTracking(progress: Int, leftDx: Float, fromUser: Boolean) {
                super.onStopTracking(progress, leftDx, fromUser)
                subProViewModel.judgeToShowBottomProBanner(canFreeUseOnce = freeUseInfo.canFreeUseOnce)
            }
        })
        mViewBinding.xsbAuto.setProgress(((autoProcessor?.effectAlpha ?: 0f) * 100).toInt(), true)
        if (FreeFeatureManager.shouldShowFreeFeatureDialog(
                subModuleEnum,
                freeUseInfo.canFreeUseOnce
            )
        ) {
            FreeFeatureManager.showFreeFeatureDialog(subModuleEnum) {
                effectTranslateLayer.start()
            }
            FreeFeatureManager.updateDialogCache(subModuleEnum)
        } else {
            effectTranslateLayer.start()
        }
        subProViewModel.judgeToShowBottomProBanner(canFreeUseOnce = freeUseInfo.canFreeUseOnce)
    }

    private fun onRewardHintClick() {
        rewardChain.rewardConfig?.run {
            this.limitFreeUse = FreeFeatureManager.featureFree(subModuleEnum)
            this.hasPaidEffect = true
            this.canFreeUse = effectResult.canFreeUse()
        }
        val builder = getRewardDialogBuilder(
            featureSource = "AI增强",
            modularSource = "编辑器",
            triggerSource = StudioProViewModel.TRIGGER_FROM_HINT,
        )

        if (studioViewModel.adTaskCenterViewModel.isTargetUser()) {
            studioViewModel.showAdTaskCenterTipsDialog(
                ownerActivity,
                onClaimed = { isClaimed, dialog ->
                    if (isClaimed) {
                        subProViewModel.showBottomProBannerEvent.value = false
                    }
                },
                onSubscribed = { isSubscribed, dialog ->
                    if (isSubscribed) {
                        subProViewModel.showBottomProBannerEvent.value = false
                    }
                },
                source = "AI增强",
                trigger_source = "hint"
            )
        } else {
            rewardChain.handlerRequest(builder)
        }
    }

    override fun onClickConfirm() {
        rewardChain.rewardConfig?.run {
            this.limitFreeUse = FreeFeatureManager.featureFree(subModuleEnum)
            this.hasPaidEffect = effectResult.hasEffect()
            this.canFreeUse = effectResult.canFreeUse()
        }

        MTAnalyticsAgent.logEvent(
            MTAnalyticsConstant.ENHANCE_YES,
            HashMap<String, String>(4).apply {
                this["是否购买"] = PictureResult.getStatisticPurchaseValue(
                    limitFree = FreeFeatureManager.featureFree(subModuleEnum),
                    canFreeUse = effectResult.canFreeUse(),
                    isShowAdDialog = rewardChain.isCanShow(),
                    isAdUnlockUse = freeUseInfo.canFreeUseOnce
                )
                this["效果滑竿值"] = AutoManualResult.getStatisticAutoValue(effectResult)
                putAll(SPMManager.instance.getCurrentSpmInfo())
            })
        val builder = getRewardDialogBuilder(
            featureSource = "AI增强",
            modularSource = "编辑器",
            triggerSource = StudioProViewModel.TRIGGER_FROM_YES,
            rewardCallback = { isSucceed, rewardType ->
                if (isSucceed) {
                    super.onClickConfirm()
                }
            },
            subCallback = {
                if (it) {
                    super.onClickConfirm()
                }
            }
        )

        if (rewardChain.isCanShow()) {
            if (studioViewModel.adTaskCenterViewModel.isTargetUser()) {
                studioViewModel.showAdTaskCenterTipsDialog(
                    ownerActivity,
                    onClaimed = { isClaimed, dialog ->
                        if (isClaimed) {
                            super.onClickConfirm()
                        }
                    },
                    onSubscribed = { isSubscribed, dialog ->
                        if (isSubscribed) {
                            super.onClickConfirm()
                        }
                    },
                    source = "AI增强",
                    trigger_source = "yes"
                )
                return
            } else {
                if (rewardChain.handlerRequest(builder)) {
                    return
                }
            }
        }
        super.onClickConfirm()
    }

    override fun setCurrentRouterParams(routerEntity: RouterEntity) {
        routerEntity.addParameter(
            UriConstant.KEY_PARAM_STRENGTH,
            effectResult.getAutoEffectAlpha().toString()
        )
    }

    override fun onDestroy() {
        super.onDestroy()

        functionRewardAdHelper.destroy()
    }

}