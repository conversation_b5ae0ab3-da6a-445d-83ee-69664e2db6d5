package com.commsource.studio.function.ar_new.effect

import com.pixocial.business.duffle.repo.ar.ARMaterial
import java.util.concurrent.ConcurrentHashMap

object AREffectCache {
    private val effectMap = ConcurrentHashMap<String, AREffectParam>()

    fun getEffectParam(key: String?): AREffectParam? {
        if (key.isNullOrEmpty()) {
            return null
        }
        return effectMap.getOrPut(key) { AREffectParam() }
    }

    fun getEffectParam(material: ARMaterial?): AREffectParam? {
        val key = material?.materialId
        if (key.isNullOrEmpty()) {
            return null
        }
        return effectMap.getOrPut(key) {
            AREffectParam().apply { this.material = material }
        }
    }
}
