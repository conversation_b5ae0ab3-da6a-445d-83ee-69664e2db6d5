package com.commsource.studio.function.removebg

import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Matrix
import android.graphics.Rect
import android.graphics.RectF
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.commsource.ad.DailyMembershipUnlocker
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentRemoveBgBinding
import com.commsource.beautyplus.free.FreeFeatureManager
import com.commsource.beautyplus.start.AWRemoteConfig
import com.commsource.billing.SubSource
import com.commsource.camera.util.animationTransition
import com.commsource.comic.widget.ProgressDialog
import com.commsource.config.ApplicationConfig
import com.commsource.config.BeautyConfig
import com.commsource.config.SubscribeConfig
import com.commsource.home.entity.DialogDataEntity
import com.commsource.statistics.ABTestManager
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.SpmParamConstant
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.studio.BrushDebugConfig
import com.commsource.studio.ImageStudioActivity
import com.commsource.studio.ImageStudioViewModel
import com.commsource.studio.MatrixBox
import com.commsource.studio.StudioConfig
import com.commsource.studio.StudioLayoutConstants
import com.commsource.studio.bean.PictureLayerInfo
import com.commsource.studio.component.ContrastComponent
import com.commsource.studio.component.PaintSelectComponent
import com.commsource.studio.component.ProBottomBannerComponent
import com.commsource.studio.component.StudioProViewModel
import com.commsource.studio.component.UndoRedoComponent
import com.commsource.studio.effect.PictureResult
import com.commsource.studio.effect.SegmentResult
import com.commsource.studio.function.BaseSubFragment
import com.commsource.studio.function.defocus.********************
import com.commsource.studio.layer.ContrastLayer
import com.commsource.studio.layer.DeFocusLayer
import com.commsource.studio.sticker.CustomStickerViewModel
import com.commsource.util.ErrorNotifier
import com.commsource.util.ResourcesUtils
import com.commsource.util.common.MathUtil
import com.commsource.util.delegate.IProcessHandler
import com.commsource.util.delegate.process.SubscribeProcess
import com.commsource.util.dp
import com.commsource.util.dpf
import com.commsource.util.gone
import com.commsource.util.text
import com.commsource.util.visible
import com.commsource.widget.TableLayoutManager
import com.commsource.widget.dialog.delegate.NegativeButton
import com.commsource.widget.dialog.delegate.PositiveButton
import com.commsource.widget.dialog.delegate.Title
import com.commsource.widget.dialog.delegate.VideoPictureTips
import com.commsource.widget.dialog.delegate.XDialog
import com.commsource.widget.dialog.delegate.popupCenter
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.meitu.common.AppContext
import com.meitu.common.utils.ToastUtils
import com.meitu.http.exception.HttpException
import com.meitu.library.hwanalytics.spm.SPMManager
import com.meitu.library.hwanalytics.spm.SPMShare
import com.meitu.library.util.bitmap.BitmapUtils
import com.meitu.library.util.device.DeviceUtils
import com.meitu.library.util.net.NetUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.collections.set

/**
 * 编辑器-去背景（抠图）
 * <AUTHOR> meitu - 2021/8/30
 */
class RemoveBgFragment : BaseSubFragment<SegmentResult>() {

    companion object {
        var source = ""
    }

    private lateinit var viewBinding: FragmentRemoveBgBinding


    override var effectResult = SegmentResult()

    override var panelHeight: Float = StudioLayoutConstants.HIGH_PANEL_HEIGHT.toFloat()

    /**
     * tab的adapter。
     */
    private val paintModeAdapter by lazy { BaseRecyclerViewAdapter(mActivity) }

    /**
     * 抠图界面VM。
     */
    private val customStickerViewModel by lazy {
        getViewModel(CustomStickerViewModel::class.java).apply {
            init(
                this@RemoveBgFragment
            )
        }
    }

    /**
     * 在线抠图处理ViewModel
     */
    private val aiSegmentViewModel by lazy { getViewModel(RemoveBgViewModel::class.java) }

    /**
     * 绘制Mask的Layer。
     */
    private val maskLayer: DeFocusLayer by lazy { DeFocusLayer(mActivity) }

    /**
     * 显示对比图的Layer。
     */
    private val contrastLayer: ContrastLayer by lazy { ContrastLayer(mActivity) }

    private var progressDialog: ProgressDialog? = null

    private var addPen =
        PaintSelectComponent.PenMode(
            DeviceUtils.getScreenWidth() * 0.02f,
            DeviceUtils.getScreenWidth() * 0.08f,
            50
        )
    private var aiAddPen =
        PaintSelectComponent.PenMode(
            DeviceUtils.getScreenWidth() * 0.02f,
            DeviceUtils.getScreenWidth() * 0.08f,
            50
        )
    private var aiRemovePen =
        PaintSelectComponent.PenMode(
            DeviceUtils.getScreenWidth() * 0.02f,
            DeviceUtils.getScreenWidth() * 0.08f,
            50
        )
    private var eraserPen =
        PaintSelectComponent.PenMode(
            DeviceUtils.getScreenWidth() * 0.02f,
            DeviceUtils.getScreenWidth() * 0.08f,
            50
        )

    // 硬度参数
    private var eraserHardness = 50
    private var penHardness = 50

    /**
     * 是否支持在线抠图
     */
    fun isEnableOnlineSegment(): Boolean {
        return NetUtils.canNetworking(AppContext.context)
    }


    private var removeProcessor: RemoveBgProcessor? = null

    /**
     * 裁剪的结果矩形。
     */
    private val segmentResultRect = Rect()

    /**
     * 图片的初始尺寸。
     */
    private val originRect = Rect()

    /**
     * 加载dialog
     */
    var loadingDialog: XDialog? = null
    var netErrorDialog: XDialog? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        viewBinding = FragmentRemoveBgBinding.inflate(inflater)
        viewBinding.fragment = this
        addSubLayer(maskLayer)
        addSubLayer(contrastLayer)
        return viewBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addTransitionView(viewBinding.contrast)
        addTransitionView(viewBinding.undoRedo)
        addTransitionView(viewBinding.flBottomBanner)
        viewBinding.contrast.setPreviewMode()
        // 手动模式下的Tab选中。
        viewBinding.rvManualTab.apply {
            layoutManager = TableLayoutManager()
            paintModeAdapter.apply {
                setOnEntityClickListener(DeFocusLayer.PaintMode::class.java) { position, entity ->
                    onSelectPaintMode(entity)
                    true
                }
                setSingleItemEntities(
                    //check：欧盟国家不显示在线分割
                    arrayListOf(
                        DeFocusLayer.PaintMode.Add,
                        DeFocusLayer.PaintMode.Eraser
                    ), ********************::class.java, true
                )
            }
            adapter = paintModeAdapter
        }

        aiSegmentViewModel.errorEvent.observe(viewLifecycleOwner, Observer {
            dismissDialog()
            if (it is HttpException || it is NullPointerException) {
                ToastUtils.showShortToast(R.string.t_remove_bg_server_error.text())
                onSelectPaintMode(
                    DeFocusLayer.PaintMode.Add
                )
            } else {
                showNetWorkErrorDialog()
            }
        })

        aiSegmentViewModel.aiMaskResultEvent.observe(viewLifecycleOwner, Observer {
            it?.let { path ->
                studioViewModel.currentFocusPictureInfo?.removeBgMask = path
                studioViewModel.layerInfoManager.layerInfoChain.filter {
                    it is PictureLayerInfo && (it.parentKey == studioViewModel.currentFocusPictureInfo?.parentKey
                            || it.texturePath == studioViewModel.currentFocusPictureInfo?.texturePath)
                }.forEach {
                    (it as? PictureLayerInfo)?.removeBgMask = path
                }
                studioViewModel.getCurStep()?.layerInfos?.filter {
                    it is PictureLayerInfo && (it.parentKey == studioViewModel.currentFocusPictureInfo?.parentKey
                            || it.texturePath == studioViewModel.currentFocusPictureInfo?.texturePath)

                }?.forEach {
                    (it as? PictureLayerInfo)?.removeBgMask = path
                }
            }
        })

        //监听图片结果返回
        aiSegmentViewModel.AiSegmentResultBitmapEvent.observe(viewLifecycleOwner, Observer {
            it?.let {
                //套用在线的mask图片
                onSelectPaintMode(
                    DeFocusLayer.PaintMode.Add
                )
                netErrorDialog?.dismiss()
                applyAiSegmentMask(it)
            }
        })

        viewBinding.limitFreeBanner.bannerClickListener = {
            SPMShare.put(SpmParamConstant.KEY_SOURCE_CLICK_POSITION, "限免横幅")
            SPMShare.put(SpmParamConstant.KEY_SOURCE_FEATURE_CONTENT, "Cutout")
            SPMShare.put(SpmParamConstant.KEY_PRF_FUNC, "Cutout")
        }
        getViewModel(UndoRedoComponent.UndoRedoViewModel::class.java)?.updateStateEvent?.observe(
            viewLifecycleOwner
        ) {
            subProViewModel.judgeToShowBottomProBanner(
                hasEffect = removeProcessor?.canUndo() ?: false,
                canFreeUseOnce = freeUseInfo.canFreeUseOnce
            )
        }
        subProViewModel.showBottomProBannerEvent.observe(viewLifecycleOwner) {
            if (it) {
                // 更换激励视频横幅样式
                val canFeatureFreeUse = FreeFeatureManager.featureFree(subModuleEnum)
                if (!canFeatureFreeUse && DailyMembershipUnlocker.shouldShowAd()) {
                    viewBinding.proBottomBanner.setCurrentStatus(ProBottomBannerComponent.WATCH_REWARDED_DAILY_UNLOCK_ALL_BANNER)
                } else if (!canFeatureFreeUse && studioViewModel.shouldShowRewardDialog) {
                    viewBinding.proBottomBanner.setCurrentStatus(ProBottomBannerComponent.WATCH_REWARDED_UNLOCK_ALL_BANNER)
                }

                viewBinding.flBottomBanner.show(1, height = 52.dp(), isShow = true)
            } else {
                viewBinding.flBottomBanner.show(1, height = 12.dp(), isShow = false)
            }
        }
        subProViewModel.watchRewardAdUnlockAllDialogEvent.observe(viewLifecycleOwner) {
            if (it) {
                onRewardHintClick()
            }
        }
        subProViewModel.watchRewardAdDailyUnlockDialogEvent.observe(viewLifecycleOwner) {
            if (it) {
                onRewardHintClick()
            }
        }

        viewBinding.proBottomBanner.setBannerClickListener {
            SPMShare.put(SpmParamConstant.KEY_SOURCE_CLICK_POSITION, "默认入口")
            SPMShare.put(SpmParamConstant.KEY_SOURCE_FEATURE_CONTENT, "Cutout")
            SPMShare.put(SpmParamConstant.KEY_PRF_FUNC, "Cutout")
        }

        if (ABTestManager.isNeedHideVipIcon(ABTestManager.TYPE_PHOTO_EDIT)) {
            viewBinding.confirmCancel.forceHideVipIcon = true
        }
    }

    private fun onRewardHintClick() {
        rewardChain.rewardConfig?.run {
            this.limitFreeUse = FreeFeatureManager.featureFree(subModuleEnum)
            this.hasPaidEffect = true
        }
        val builder = getRewardDialogBuilder(
            modularSource = "编辑器",
            triggerSource = StudioProViewModel.TRIGGER_FROM_HINT,
        )
        rewardChain.handlerRequest(builder)
    }

    override fun onGlResourceInit() {
        super.onGlResourceInit()
        originRect.set(
            0,
            0,
            studioViewModel.imageData.getWidth(),
            studioViewModel.imageData.getHeight()
        )
        removeProcessor = RemoveBgProcessor().apply {
            addProcessor(this)
        }
    }

    override fun onInitComplete() {
        super.onInitComplete()
        removeProcessor?.addObserver(this)

        aiSegmentViewModel.progressEvent.observe(this) {
            it?.let { updateLoadingState(it) }
        }

        // 对比
        ViewModelProvider(this).get(ContrastComponent.ContrastViewModel::class.java).apply {
            contrastEvent.observe(this@RemoveBgFragment, Observer {
                if (it) {
                    contrastLayer.setBitmap(
                        studioViewModel.imageData.image,
                        removeProcessor?.getSegmentMask()
                    )
                } else {
                    contrastLayer.dismiss()
                }
            })
        }
        removeProcessor?.singleFingerScrollEvent?.observe(this, Observer {
            setOperatorState(it)
        })

        // 初始化GL放大镜。
        viewBinding.manify.initMagnifyTexture(
            studioViewModel.glRenderer.context.eglProvider!!.shareContext,
            subEffectFBOWithMask!!
        )

        viewBinding.xsbPaint.usePaintMode(addPen)

        // 效果调试逻辑
        studioViewModel.debugBrushOkEvent.observe(viewLifecycleOwner) {
            if (it == true) {
                BrushDebugConfig.penSize?.apply {
                    aiAddPen = PaintSelectComponent.PenMode(this[0].dpf(), this[1].dpf(), 50)
                    eraserPen = PaintSelectComponent.PenMode(this[0].dpf(), this[1].dpf(), 50)
                    removeProcessor?.optMode?.let {
                        onSelectPaintMode(it)
                    }
                }
            }
        }

        if (
            FreeFeatureManager.shouldShowFreeFeatureDialog(
                subModuleEnum,
                freeUseInfo.canFreeUseOnce
            )
        ) {
            FreeFeatureManager.showFreeFeatureDialog(subModuleEnum) {
                init()
            }
            FreeFeatureManager.updateDialogCache(subModuleEnum)
        } else {
            init()
        }
    }

    private fun init() {
        //首次toast提示
        if (!isEnableOnlineSegment()) {
            onSelectPaintMode(
                DeFocusLayer.PaintMode.Add
            )
            if (!BeautyConfig.isUsedRemoveBg()) {
                BeautyConfig.setUseRemoveBg()
                ToastUtils.showShortToast(R.string.t_scrawl_to_keep)
            }
            lifecycleScope.launch(Dispatchers.IO) {
                studioViewModel.currentFocusPictureInfo?.let {
                    val cacheBitmap =
                        if (!TextUtils.isEmpty(it.removeBgMask)) BitmapUtils.loadBitmapFromSDcard(it.removeBgMask) else null
                    if (BitmapUtils.isAvailableBitmap(cacheBitmap)) {
                        aiSegmentViewModel.AiSegmentResultBitmapEvent.postValue(cacheBitmap)
                    }
                }
            }
        } else {
            startOnlineSegment()
        }
    }

    /**
     * 选中笔模式
     */
    private fun onSelectPaintMode(paintMode: DeFocusLayer.PaintMode) {
        maskLayer.gestureEnable = true
        maskLayer.showMaskAlpha = 1f
        removeProcessor?.optMode = paintMode
        paintModeAdapter.currentSelectEntity = paintMode
        when (paintMode) {
            DeFocusLayer.PaintMode.Eraser -> {
                viewBinding.hardnessSeekbar.setProgress(eraserHardness)
                Pair(aiRemovePen, true)
            }

            DeFocusLayer.PaintMode.AiRemove -> Pair(aiRemovePen, true)
            DeFocusLayer.PaintMode.AiAdd -> Pair(aiAddPen, false)
            else -> {
                viewBinding.hardnessSeekbar.setProgress(penHardness)
                Pair(eraserPen, false)
            }
        }.let {
            viewBinding.xsbPaint.usePaintMode(it.first, false)
            viewBinding.xsbPaint.showIndicatorThumb(it.second)
        }
    }

    private fun setOperatorState(isAnimIn: Boolean) {
        if (isAnimIn) {
            viewBinding.undoRedo.run {
                animationTransition {
                    translationY = DeviceUtils.dip2fpx(51f)
                    alpha = 0f
                }
            }
            viewBinding.contrast.run {
                animationTransition {
                    translationY = DeviceUtils.dip2fpx(51f)
                    alpha = 0f
                }
            }
        } else {
            viewBinding.undoRedo.run {
                animationTransition {
                    translationY = DeviceUtils.dip2fpx(0f)
                    alpha = 1f
                }
            }
            viewBinding.contrast.run {
                animationTransition {
                    translationY = DeviceUtils.dip2fpx(0f)
                    alpha = 1f
                }
            }
        }
    }

    override fun onClickConfirm() {
        rewardChain.rewardConfig?.run {
            this.limitFreeUse = FreeFeatureManager.featureFree(subModuleEnum)
            this.hasPaidEffect = removeProcessor?.canUndo() == true
        }

        if (ownerActivity is ImageStudioActivity) {
            MTAnalyticsAgent.logEvent(
                MTAnalyticsConstant.CUTOUT_YES,
                HashMap<String, String>().apply {
                    this["是否购买"] = PictureResult.getStatisticPurchaseValue(
                        limitFree = FreeFeatureManager.featureFree(subModuleEnum),
                        canFreeUse = effectResult.canFreeUse(),
                        isShowAdDialog = rewardChain.isCanShow(),
                        isAdUnlockUse = freeUseInfo.canFreeUseOnce
                    )
                    this["source"] = source
                    putAll(SPMManager.instance.getCurrentSpmInfo())
                })
        }
        SPMShare.put(SpmParamConstant.KEY_SOURCE_CLICK_POSITION, "打勾确认")
        //BugFix:涂抹类效果 如果撤销堆栈 不能撤销 判断为没有效果
        if ((ownerActivity is ImageStudioActivity) && removeProcessor?.canUndo() != true) {
            studioViewModel.pushCurrentLayerInfoToStack()
            exit()
            return
        }
        val task = Runnable {
            removeProcessor?.getSegmentMask()?.apply {
                queueEventAndLoading {
                    var result: Pair<Bitmap, Rect>? = null
                    try {
                        result = customStickerViewModel
                            .cropSticker(
                                studioViewModel.imageData.image,
                                this,
                                (ownerActivity is ImageStudioActivity)
                            )
                    } catch (ignore: Exception) {
                        ignore.printStackTrace()
                    }
                    if (result != null && BitmapUtils.isAvailableBitmap(result.first)) {
                        val bitmap = result.first
                        segmentResultRect.set(result.second)
                        confirmEffect(effectResult, bitmap, inWorkThread = true)
                    }
                }
            }
        }
        if ((ownerActivity is ImageStudioActivity) && !SubscribeConfig.isSubValid() && !FreeFeatureManager.featureFree(
                subModuleEnum
            ) && !freeUseInfo.canFreeUseOnce
        ) {
            val builder = getRewardDialogBuilder(
                modularSource = "编辑器",
                triggerSource = StudioProViewModel.TRIGGER_FROM_YES,
                rewardCallback = { isSucceed, rewardType ->
                    if (isSucceed) {
                        task.run()
                    }
                },
                subCallback = {
                    if (it) {
                        task.run()
                    }
                }
            )
            if (rewardChain.handlerRequest(builder)) {
                return
            }
            subProViewModel.toPro(
                ownerActivity,
                subModuleEnum,
                effectResult
            ) { isSubscribe, isCanFreeUse ->
                if (isSubscribe || isCanFreeUse) {
                    task.run()
                }
            }
        } else {
            if (!SubscribeConfig.isSubValid() && !freeUseInfo.canFreeUseOnce) {
                // 因为限免，进入的此分支，要扣除次数
                checkWhetherUseLimitFree()
            }
            task.run()
        }
    }

    override fun prepareExitStartMatrix(isCancel: Boolean, validRectF: RectF): MatrixBox {
        if (isCancel) {
            return super.prepareExitStartMatrix(isCancel, validRectF)
        } else {
            // 裁剪后的变换位置。
            return MatrixBox(Matrix().apply {
                //整体canvas的变化
                //结果图的偏移位置
                //原始图片的矩阵位置
                postTranslate(segmentResultRect.left.toFloat(), segmentResultRect.top.toFloat())
                postConcat(
                    MathUtil.generateInscribeMatrix(
                        validRectF,
                        originRect.width(),
                        originRect.height()
                    )
                )
                postConcat(canvasContainer.canvasGestureChangeMatrix.matrix)
            })
        }
    }

    override fun beforeAnimateOut() {
        super.beforeAnimateOut()
        maskLayer.isEnable = false
    }

    /**
     * 开启在线分割
     */
    private fun startOnlineSegment() {
        //开启在线图片分割
        //发动在线处理
        if (aiSegmentViewModel.isNeedAiSegment()) {
            if (!NetUtils.canNetworking(AppContext.context)) {
                showNetWorkErrorDialog()
                return
            }
            //后台开关关闭，提示升级弹窗
            if (StudioConfig.needShowRemoveBgUpdate()) {
                onSelectPaintMode(DeFocusLayer.PaintMode.Add)
                showUpdateDialog()
                return
            }
            // 如果为空尝试拉取RemoveBg的key
            if (TextUtils.isEmpty(ApplicationConfig.getRemoveBgKey())) {
                AWRemoteConfig.loadRemoteConfig()
                onSelectPaintMode(DeFocusLayer.PaintMode.Add)
                return
            }
            lifecycleScope.launch(Dispatchers.IO) {
                studioViewModel.currentFocusPictureInfo?.let {
                    val cacheBitmap =
                        if (!TextUtils.isEmpty(it.removeBgMask)) BitmapUtils.loadBitmapFromSDcard(it.removeBgMask) else null
                    if (BitmapUtils.isAvailableBitmap(cacheBitmap)) {
                        aiSegmentViewModel.AiSegmentResultBitmapEvent.postValue(cacheBitmap)
                    } else {
                        withContext(Dispatchers.Main) { showLoadingDialog() }
                        aiSegmentViewModel.aiSegment(
                            studioViewModel.imageData.image,
                            maskLayer.viewPortWidth.toInt(),
                            maskLayer.viewPortHeight.toInt()
                        )
                    }
                }
            }

        } else {
            //直接使用已经存在的mask图片作为masklayer叠加效果
            aiSegmentViewModel.AiSegmentResultBitmapEvent.value?.let {
                applyAiSegmentMask(it)
            }
        }
    }

    /**
     * 套用AiSemgent的mask图片
     */
    fun applyAiSegmentMask(bgMask: Bitmap) {
        dismissDialog()
        removeProcessor?.applyAiSegmentMask(bgMask)
    }


    fun showLoadingDialog() {
        if (progressDialog == null) {
            progressDialog = ProgressDialog(ownerActivity, R.style.updateDialog)
            progressDialog?.setCancelListener {
                // 取消请求
                aiSegmentViewModel.cancelRequest()
                onSelectPaintMode(
                    DeFocusLayer.PaintMode.Add
                )
            }
            progressDialog?.show()
            updateLoadingState(0)
        }
        progressDialog?.setCancelable(false)
        progressDialog?.setCanceledOnTouchOutside(false)
    }

    private fun updateLoadingState(progress: Int) {
        progressDialog?.takeIf { it.isShowing }?.let {
            progressDialog?.updateProgress(progress)
            val text = when {
                progress in 0..29 -> {
                    R.string.t_detecting.text()
                }

                progress in 30..79 -> {
                    R.string.t_processing.text()
                }

                else -> {
                    R.string.t_finish_processing.text()
                }
            }
            progressDialog?.updateDownloadText(text)
        }
    }

    private fun dismissDialog() {
        progressDialog?.takeIf {
            it.isShowing
        }?.dismiss()
        progressDialog = null
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    fun setSourceFeatureContent(content: String) {
        effectResult.source_feature_content = content
    }


    private fun showNetWorkErrorDialog() {
        netErrorDialog = ErrorNotifier.showNetworkRetryDialog(onPositiveClick = {
            startOnlineSegment()
        }, onNegativeClick = {
            onSelectPaintMode(
                DeFocusLayer.PaintMode.Add
            )
        })
    }

    private fun showUpdateDialog() {
        XDialog {
            VideoPictureTips {
                closeEnable = false
                Title(
                    String.format(
                        ResourcesUtils.getString(R.string.t_version_update),
                        "GooglePlay"
                    )
                )
                PositiveButton(R.string.t_goto_update.text()) {
                    it.dismissAllowingStateLoss()
                    try {
                        val intent = Intent()
                        intent.action = Intent.ACTION_VIEW
                        intent.data =
                            Uri.parse(ResourcesUtils.getString(R.string.software_grade_url))
                        if (context !is Activity) {
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        }
                        startActivity(intent)
                    } catch (ignore: Exception) {
                        ToastUtils.showLongToastSafe(R.string.open_failed)
                    }
                }
                NegativeButton(R.string.t_later_text.text()) {
                    it.dismissAllowingStateLoss()
                }
                popupCenter()
            }
        }.show()
    }


}