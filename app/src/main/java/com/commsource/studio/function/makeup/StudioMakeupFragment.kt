package com.commsource.studio.function.makeup

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.commsource.ad.DailyMembershipUnlocker
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentStudioMakeupBinding
import com.commsource.beautyplus.router.content
import com.commsource.beautyplus.router.style
import com.commsource.camera.makeup.MakeupHelper
import com.commsource.camera.newrender.recognize.DL3DData
import com.commsource.camera.newrender.recognize.HairMaskData
import com.commsource.camera.newrender.renderproxy.ARKernelUtils
import com.commsource.camera.newrender.renderproxy.ArRenderProxy
import com.commsource.camera.newrender.renderproxy.arpart.ArSegmentPart
import com.commsource.camera.newrender.renderproxy.arpart.BeautyMakeupPart
import com.commsource.camera.newrender.renderproxy.arpart.DL3DPart
import com.commsource.camera.newrender.renderproxy.arpart.Face3DPart
import com.commsource.camera.param.MakeupType
import com.commsource.camera.util.XCollectionUtils
import com.commsource.camera.xcamera.cover.bottomFunction.BaseBottomSubFragment
import com.commsource.widget.FastCenterScrollLayoutManager
import com.commsource.camera.xcamera.cover.bottomFunction.effect.makeup.*
import com.commsource.camera.xcamera.cover.rightFunction.makupStyle.MakeupVerticalFragment
import com.commsource.camera.xcamera.cover.tips.TipsViewModel
import com.commsource.config.SubscribeConfig
import com.commsource.easyeditor.utils.opengl.FBOEntity
import com.commsource.easyeditor.utils.opengl.GlThread
import com.commsource.repository.LoadState
import com.commsource.repository.child.makeup.MakeupGroup
import com.commsource.repository.child.makeup.MakeupMaterialRepository
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.SpmParamConstant
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.studio.StudioLayoutConstants
import com.commsource.studio.component.ProBottomBannerComponent
import com.commsource.studio.component.SeekComponent
import com.commsource.studio.component.StudioProViewModel
import com.commsource.studio.effect.MakeupResult
import com.commsource.studio.function.BaseSubFragment
import com.commsource.studio.layer.MultiFaceSelectLayer
import com.commsource.studio.processor.MultiFaceEffectProcessor
import com.commsource.util.*
import com.commsource.util.common.ProcessUtil
import com.commsource.widget.ProView
import com.commsource.widget.mask.DataMask
import com.commsource.widget.mask.MaskType
import com.commsource.widget.recyclerview.AdapterDataBuilder
import com.meitu.common.utils.ToastUtils
import com.meitu.library.hwanalytics.spm.SPMManager
import com.meitu.library.hwanalytics.spm.SPMShare
import com.meitu.library.util.device.DeviceUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * @Desc : 图片编辑中美妆界面美妆界面
 * <AUTHOR> Bear - 2020/8/17
 */
class StudioMakeupFragment : BaseSubFragment<MakeupResult>() {

    override var panelHeight: Float = StudioLayoutConstants.HIGH_PANEL_HEIGHT.toFloat()

    val makeupViewModel by lazy { ViewModelProvider(this)[MakeupViewModel::class.java] }

    val tipsViewModel by lazy { ViewModelProvider(this)[TipsViewModel::class.java] }

    val mViewBinding by lazy { FragmentStudioMakeupBinding.inflate(layoutInflater, null, false)!! }

    val mGroupAdapter by lazy { MakeupAdapter(ownerActivity, makeupViewModel) }

    val mGroupLayoutManager by lazy {
        FastCenterScrollLayoutManager(
            context,
            LinearLayoutManager.HORIZONTAL,
            false
        )
    }

    val mVpAdapter by lazy { MakeupChildPageAdapter(this) }

    var groups: List<MakeupGroup>? = null

    var faceSelectLayer: MultiFaceSelectLayer? = null

    val itemDecoration by lazy { MakeupTitleItemDecoration() }

    /**
     * 目前底部Fragment
     */
    var mCurrentSubFragment: BaseBottomSubFragment? = null

    /**
     * 当前选中的group
     */
    var group: MakeupGroup? = null

    var curposition = 0

    val fm: FragmentManager by lazy { childFragmentManager }

    /**
     * 美妆多人脸渲染处理的Processor
     */
    val makeupProcessor by lazy {

        object :
            MultiFaceEffectProcessor<ArRenderProxy>(ArRenderProxy(ARKernelUtils.ArFrameType.PhotoEdit).apply {
                setNeedPremultiplyAlpha(true).setNeedAlphaCutout(true)
                addFunctionPart(BeautyMakeupPart())
                    .addFunctionPart(ArSegmentPart())
                    .addFunctionPart(Face3DPart())
                    .addFunctionPart(DL3DPart())
            }) {
            override fun updateParamsToRenderProxy(
                renderProxy: ArRenderProxy,
                faceIndex: Int,
                lastEffectFBOEntity: FBOEntity
            ) {
                // 找到对应人脸的美妆配置
                val materials =
                    makeupViewModel.makeupEffect.getMakeupMaterialsByFaceIndex(faceIndex)
                val colorMaterials =
                    makeupViewModel.makeupEffect.getMakeupColorMaterialByFaceIndex(faceIndex)
                if (materials == null) {
                    renderProxy.beginExecute().clearAllEffect()
                } else {
                    //颜色素材清除
                    renderProxy.clearMakeupStyleColor()
                    colorMaterials?.let {
                        for (i in 0 until it.size()) {
                            val keyAt = it.keyAt(i)
                            val valueAt = it.valueAt(i)
                            renderProxy.saveMakeupStyleColor(
                                keyAt,
                                MakeupUtils.trans2RGBA(valueAt.getColorConfig())
                            )
                        }
                    }
                    renderProxy.beginExecute()
                        .setMakeupParams(
                            MakeupHelper.transToMultiFaceMakeupParams(
                                faceIndex,
                                materials,
                                colorMaterials
                            )
                        )
                        .commit(true, null)
                }
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        if (studioViewModel.detectData.faceDataBox.faceCount > 1) {
            faceSelectLayer = MultiFaceSelectLayer(mActivity).apply {
                isEnableFocus = true
                addSubLayer(this)
                layerView.alpha = 0f
            }
        }
        mViewBinding.fragment = this
        return mViewBinding.root
    }

    private var clickFromUser = false

    override fun needAutoFocusFace(): Boolean {
        return studioViewModel.detectData.faceDataBox.faceCount == 1
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addTransitionView(mViewBinding.contrast)
        addTransitionView(mViewBinding.preview)
        addTransitionView(mViewBinding.flBottomBanner)

        mViewBinding.maskContainer.maskContainerHelper.newBuilder()
            .addMaskGroup(DataMask.getShortPanelGroup(DataMask.DataMaskTheme.Light().apply {
                backgroundRadius = 16.dpf()
            }))
            .bindView(MaskType.NetError, R.id.tv_action) {
                if (ProcessUtil.isProcessing()) {
                    return@bindView
                }

                // 加载数据
                MakeupMaterialRepository.executeCompare()
            }.build()

        mViewBinding.maskContainer.maskContainerHelper.newBuilder()
            .addMaskGroup(DataMask.getShortPanelGroup(DataMask.DataMaskTheme.Light().apply {
                backgroundRadius = 16.dpf()
            }))
            .bindView(MaskType.NetError, R.id.tv_action) {
                if (ProcessUtil.isProcessing()) {
                    return@bindView
                }

                // 加载数据
                MakeupMaterialRepository.executeCompare()
            }.build()

        onPreviewChange = {
            if (it) {
                mViewBinding.flRight.gone()
                faceSelectLayer?.layerView?.gone()
            } else {
                mViewBinding.flRight.visible()
                faceSelectLayer?.layerView?.visible()
            }
        }

        mViewBinding.sc.onTransitionYChange = {
            mViewBinding.flSeekContainer.translationY = it
        }
//        mViewBinding.sc.onTargetTransitionYChange = {
//            updatePanelHeight(StudioLayoutConstants.HIGH_PANEL_HEIGHT.toFloat() - it, false)
//        }

        // 初始化设置非付费状态
        studioViewModel.setProStateEvent.value = Pair(first = false, second = false)

        mViewBinding.rvGroup.layoutManager = mGroupLayoutManager
        mViewBinding.rvGroup.addItemDecoration(itemDecoration)
        mViewBinding.rvGroup.adapter = mGroupAdapter

        mViewBinding.vp.offscreenPageLimit = 2
        mViewBinding.vp.isUserInputEnabled = false
        mViewBinding.vp.adapter = mVpAdapter

        //如果是实验组情况下 存在右边栏的情况 需要监听对应变化
        makeupViewModel.expandEvent.observe(viewLifecycleOwner, Observer {
            if (it == null || makeupViewModel.getApplyMakeup(it.makeupType) == null || !makeupViewModel.isExpandHasColor()) {
                show(null)
                showMakeupVerticalScroll(false)
            } else {
                show(it)
                showMakeupVerticalScroll(true)
            }
        })

        makeupViewModel.selectChildEvent.observe(viewLifecycleOwner, Observer {
            if (it == null) {
                show(null)
                showMakeupVerticalScroll(false)
            } else {
                if (it.isPreset() || !makeupViewModel.isExpandHasColor()) {
                    show(null)
                    showMakeupVerticalScroll(false)
                } else {
                    show(makeupViewModel.expandEvent.value)
                    showMakeupVerticalScroll(true)
                }
            }
        })

        makeupViewModel.showMakeupVerticalGuideEvent.observe(viewLifecycleOwner, Observer {
            it?.let {
                if (!it) {
                    MakeupConfig.setShowMakeupVerticalGuide(false)
                }
                showMakeupVerticalScroll(it)
            }
        })

        makeupViewModel.applyMakeupColorEvent.observe(viewLifecycleOwner, Observer {
            it?.let {
                //保存素材
                makeupViewModel.makeupEffect.saveMakeupColorMaterials(
                    makeupProcessor.selectFaceIndex,
                    it
                )
                if (!makeupViewModel.applyMakeupColorEvent.isFromUser) {
                    return@Observer
                }
                makeupViewModel.expandEvent.value?.let { group ->
                    //如果颜色是主tab的情况下，那么选中的素材就是颜色素材
                    if (MakeupUtils.isMainColorTab(group.makeupType)) {
                        makeupViewModel.selectMakeupChild(it[group.makeupType])
                    }
                }
                for (i in 0 until it.size()) {
                    val makeupType = it.keyAt(i)
                    val material = it.valueAt(i)
                    val color = MakeupUtils.trans2RGBA(material.getColorConfig())
                    makeupProcessor.renderProxy.saveMakeupStyleColor(makeupType, color)
                    makeupProcessor.renderProxy.findPlistDataByMakeupType(makeupType)?.let { jni ->
                        var alpha = if (MakeupUtils.isMainColorTab(makeupType)) {
                            material.getFaceAlpha(makeupProcessor.selectFaceIndex)
                        } else {
                            makeupViewModel.getApplyMakeup(makeupType)?.let {
                                it.getFaceAlpha(makeupProcessor.selectFaceIndex)
                            }

                        }
                        makeupProcessor.renderProxy.findColorByMakeupType(makeupType)
                            .takeIf { alpha != null }?.let {
                                queueEvent {
                                    ARKernelUtils.setMakeupRGBValue(
                                        makeupProcessor.renderProxy.arKernelInterface,
                                        jni,
                                        makeupType,
                                        it.toFloatRed(),
                                        it.toFloatGreen(),
                                        it.toFloatBlue(),
                                        alpha!! / 100f
                                    )
                                }
                            }
                    }
                    makeupProcessor.requestRender()
                }
                // 动画已经执行完毕
                if (studioViewModel.screenTouchEnable && !studioViewModel.hasMultiFace()) {
                    //显示订阅状态
                    showPro(
                        if (makeupViewModel.isApplyNeedPay()) {
                            ProView.Mode.PROBANNER
                        } else {
                            ProView.Mode.NONE
                        }
                    )
                    studioViewModel.setProStateEvent.value =
                        Pair(first = makeupViewModel.isApplyNeedPay(), second = true)
                }
                subProViewModel.judgeToShowBottomProBanner(
                    hasEffect = makeupViewModel.isApplyNeedPay(),
                    canFreeUseOnce = freeUseInfo.canFreeUseOnce
                )
            }
        })

        //page监听
        mViewBinding.vp.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {

            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                groups?.takeIf { XCollectionUtils.inRange(groups, position) }?.let {
                    var isLeft = position > curposition
                    val group = it.get(position)
                    mGroupAdapter.currentSelectEntity = group
//                    mViewBinding.lineSelect.setSelectIndex(position)
                    //选中某个组别
                    makeupViewModel.selectMakeupGroup(group, clickFromUser)
                    mViewBinding.rvGroup.smoothScrollToPosition(position)
                    if (makeupViewModel.isApply(group.makeupType) && !makeupViewModel.isApplyPreset(
                            group.makeupType
                        )
                    ) {
                        if (MakeupUtils.isMainColorTab(group.makeupType)) {
                            makeupViewModel.getApplyMakeupColor(group.makeupType)
                        } else {
                            makeupViewModel.getApplyMakeup(group.makeupType)
                        }?.let {
                            tipsViewModel.showFloatingTips(
                                it.getMakeupName(),
                                isLeftToRight = isLeft
                            )
                        }
                    }
                    curposition = position
                }
            }
        })

        //点击
        mGroupAdapter.setOnEntityClickListener(MakeupGroup::class.java) { position: Int, entity: MakeupGroup? ->
            if (makeupViewModel.isExpand(entity)) {
                return@setOnEntityClickListener false
            }
            //多人脸染发提示
            entity?.takeIf { it.makeupType == MakeupType.TYPE_HAIR && studioViewModel.detectData.faceDataBox.faceCount > 1 }
                ?.let {
                    ToastUtils.showShortToastSafe(mActivity.getString(R.string.hair_no_support))
                }
            clickFromUser = true
            mViewBinding.vp.setCurrentItem(position, true)
            false
        }

        // 多人脸选中监听。
        studioViewModel.selectFaceEvent.observe(
            viewLifecycleOwner,
            object : NoStickLiveData.CustomObserver<Int?>() {
                override fun onAccept(faceIndex: Int?) {
                    faceIndex?.let {
                        //保存人脸位置
                        makeupViewModel.makeupEffect.selectFaceIndex = faceIndex
                        makeupProcessor.selectFaceIndex = faceIndex
                        //在renderProxy清除所有颜色素材的标记
                        makeupProcessor.renderProxy.clearMakeupStyleColor()
                        //人脸改变时 读取选中对应人脸的数据 会直接更新颜色数据和renderProxy中的颜色
                        makeupViewModel.onFaceChange(faceIndex, makeupViewModel.makeupEffect)
                        //单独上当前人脸效果
                        queueEvent {
                            //选择切换人脸先清理旧处理器中的颜色数据 重新上颜色数据
                            val colorMaterials =
                                makeupViewModel.makeupEffect.getMakeupColorMaterialByFaceIndex(
                                    faceIndex
                                )
                            makeupProcessor.renderProxy.clearMakeupStyleColor()
                            colorMaterials?.let {
                                for (i in 0 until colorMaterials.size()) {
                                    makeupProcessor.renderProxy.saveMakeupStyleColor(
                                        colorMaterials.keyAt(
                                            i
                                        ),
                                        MakeupUtils.trans2RGBA(
                                            colorMaterials.valueAt(i).getColorConfig()
                                        )
                                    )
                                }
                            }
                            MakeupHelper.transToMultiFaceMakeupParams(
                                makeupProcessor.selectFaceIndex,
                                makeupViewModel.applyMakeupMaterials,
                                makeupViewModel.applyMakeupColorMaterials
                            ).apply {
                                if (isEmpty()) {
                                    makeupProcessor.renderProxy.beginExecute().clearAllEffect()
                                } else {
                                    makeupProcessor.renderProxy.beginExecute()
                                        .setMakeupParams(this)
                                        .commit(true, null)
                                }
                            }
                            makeupProcessor.requestRender()
                        }
                    }
                }
            })
        //加载数据
        makeupViewModel.dataEvent.observe(viewLifecycleOwner, Observer {
            it?.let { groups ->
                this.groups = groups
                mGroupAdapter.updateItemEntities(
                    AdapterDataBuilder.create()
                        .addEntities(groups, MakeupGroupTitleViewHolder::class.java)
                        .build()
                )
                itemDecoration.calculateGroups(groups)
                mVpAdapter.notifyDataSetChanged()
                if (getRouterEntity() == null) {
                    clickFromUser = false
                    mViewBinding.vp.setCurrentItem(0, true)
                } else {
                    //获取协议内容 确认协议
                    runOnInitComplete {
                        UIHelper.runOnIdleTiming {
                            if (AppTools.isFinishing(ownerActivity)) {
                                return@runOnIdleTiming
                            }
                            getRouterEntity()?.let {
                                when {
                                    !TextUtils.isEmpty(it.content) -> {
                                        it.content?.let { id ->
                                            makeupViewModel.findMakeupMaterialByOnlineId(id)
                                                ?.let { material ->
                                                    makeupViewModel.findMakeupGroupByMakeupType(
                                                        material.getCurrentMakeupType()
                                                    )?.let { group ->
                                                        this.groups?.indexOf(group)?.let {
                                                            mViewBinding.vp.setCurrentItem(it, true)
                                                        }
                                                        makeupViewModel.selectMakeupGroup(
                                                            group,
                                                            true
                                                        )
                                                    }
                                                    makeupViewModel.clickMakeupWrapper(
                                                        -1,
                                                        material,
                                                        false,
                                                        isFromProtocol = true
                                                    )
                                                    //对应颜色素材
                                                    it.style?.let { rightId ->
                                                        makeupViewModel.findMakeupMaterialByOnlineId(
                                                            material.getCurrentMakeupType(),
                                                            rightId
                                                        )?.let {
                                                            makeupViewModel.clickMakeupWrapper(
                                                                -1,
                                                                it,
                                                                isFromProtocol = true
                                                            )
                                                        }
                                                    }
                                                }
                                        }
                                    }

                                    !TextUtils.isEmpty(it.style) -> {
                                        it.style?.let { rightId ->
                                            makeupViewModel.findMakeupMaterialByOnlineId(
                                                rightId
                                            )?.let { material ->
                                                if (MakeupUtils.isMainColorTab(material.makeupType)) {
                                                    makeupViewModel.findMakeupGroupByMakeupType(
                                                        material.getCurrentMakeupType()
                                                    )?.let { group ->
                                                        this.groups?.indexOf(group)?.let {
                                                            mViewBinding.vp.setCurrentItem(it, true)
                                                        }
                                                        makeupViewModel.selectMakeupGroup(
                                                            group,
                                                            true
                                                        )
                                                    }
                                                    //BugFix：协议配置 只配置rightId
                                                    makeupViewModel.findMakeupMaterialByOnlineId(
                                                        MakeupConfig.getNewMakeupColorDefaultId(
                                                            material.makeupType
                                                        )
                                                    )?.let {
                                                        makeupViewModel.clickMakeupWrapper(
                                                            -1,
                                                            it,
                                                            false,
                                                            isFromProtocol = true
                                                        )
                                                    }
                                                    makeupViewModel.clickMakeupWrapper(
                                                        -1,
                                                        material,
                                                        false,
                                                        isFromProtocol = true
                                                    )
                                                } else {
                                                    makeupViewModel.findMakeupGroupByMakeupType(
                                                        material.getCurrentMakeupType()
                                                    )?.let { group ->
                                                        this.groups?.indexOf(group)?.let {
                                                            mViewBinding.vp.setCurrentItem(it, true)
                                                        }
                                                        makeupViewModel.selectMakeupGroup(
                                                            group,
                                                            true
                                                        )
                                                    }
                                                    makeupViewModel.clickMakeupWrapper(
                                                        -1,
                                                        material,
                                                        false,
                                                        isFromProtocol = true
                                                    )
                                                }
                                            }
                                        }
                                    }

                                    else -> {

                                    }
                                }
                            }
                        }
                    }
                }
            }
        })

        //选中组别时 回调
        makeupViewModel.expandEvent.observe(viewLifecycleOwner, Observer {
            it?.let {
                if (makeupViewModel.expandEvent.isFromUser) {
                    MTAnalyticsAgent.logEvent(
                        MTAnalyticsConstant.BEAUTY_CLK_MAKEUP_CATEGORY, "分类名称",
                        MakeupUtils.getMakeupStatisticValue(it.makeupType)
                    )
                }
            }
        })

        //选中某个子素材
        makeupViewModel.selectChildEvent.observe(viewLifecycleOwner, Observer {
            if (it == null || it.unableAdjustAlpha) {
                mViewBinding.sc.updateSeekWrappers(null)
            } else {
                mViewBinding.sc.updateSeekWrappers(arrayListOf(SeekComponent.SeekWrapper().apply {
                    progress = if (it.getCurrentMakeupType() == MakeupType.TYPE_HAIR) {
                        //染发效果大家一起用 直接使用0位置人脸数据
                        it.getFaceAlpha(0)
                    } else {
                        it.getFaceAlpha(makeupProcessor.selectFaceIndex)
                    }

                    defaultProgress = it.getDefaultResetAlpha()

                    onStartTracking = {

                    }

                    onProgressChange = { progress, fromUser ->
                        if (fromUser) {
                            //当前选中的子素材
                            makeupViewModel.selectChildEvent.value?.let { makeupWrapper ->
                                //保存人脸中的数据
                                if (makeupWrapper.getCurrentMakeupType() == MakeupType.TYPE_HAIR) {
                                    makeupWrapper.saveFaceAlpha(0, progress)
                                } else {
                                    makeupWrapper.saveFaceAlpha(
                                        makeupProcessor.selectFaceIndex,
                                        progress
                                    )
                                }
                                //修改对应美妆效果
                                makeupProcessor.renderProxy.beautyMakeupPart?.let {
                                    it.setMakeupAlpha(
                                        makeupWrapper.getCurrentMakeupType(),
                                        progress / 100f
                                    )
                                }
                                //渲染
                                makeupProcessor.requestRender(false)
                            }
                        }
                    }
                    onStopTracking = {
//                        subProViewModel.judgeToShowBottomProBanner()
                        makeupProcessor.requestRender()
                    }
                }))
            }
        })

        tipsViewModel.floatingTipsEvent.observe(viewLifecycleOwner, Observer {
            it?.let {
                showFloatingTips(it, tipsViewModel.floatingTipsEvent.isLeftToRight)
            }
        })

        //如果是特殊的协议跳转的素材 下载成功
        MakeupMaterialRepository.downloadObserver.successEvent.observe(viewLifecycleOwner) {
            it?.let {
                if (it == makeupViewModel.protocolMakeupMaterial) {//如果是协议跳转的下载素材
                    //重置其他素材的自动套用
                    makeupViewModel.resetAutoApplyMakeupWrapper()
                    makeupViewModel.protocolMakeupMaterial = null
                    if (makeupViewModel.clickMakeupWrapper(0, it, false, true)) {
                        tipsViewModel.showFloatingTips(it.getMakeupName(), isLeftToRight = true)
                    }
                }
            }
        }
        //套用素材 请求渲染
        makeupViewModel.applyMakeupEvent.observe(viewLifecycleOwner, Observer {
            if (!makeupViewModel.applyMakeupEvent.isFromUser) {
                return@Observer
            }
            makeupViewModel.expandEvent.value?.let { group ->
                val makeupWrapper = it[group.makeupType]
                if (MakeupUtils.isMainColorTab(group.makeupType)) {
                    if (!makeupViewModel.isApply(group.makeupType)) {
                        makeupViewModel.selectMakeupChild(null)
                    } else {
                        makeupViewModel.selectMakeupChild(makeupViewModel.applyMakeupColorMaterials[group.makeupType])
                    }
                } else {
                    if (makeupWrapper == null) {
                        makeupViewModel.selectMakeupChild(null)
                    } else {
                        makeupViewModel.selectMakeupChild(makeupWrapper)
                    }
                }
            }
            //保存该人脸的美妆数据
            makeupViewModel.makeupEffect.saveMakeupMaterials(makeupProcessor.selectFaceIndex, it)
            queueEvent {
                //上对应人脸的妆容
                makeupProcessor.renderProxy.beginExecute()
                    .setMakeupParams(
                        MakeupHelper.transToMultiFaceMakeupParams(
                            makeupProcessor.selectFaceIndex,
                            it,
                            makeupViewModel.applyMakeupColorMaterials
                        )
                    )
                    .commit(true, null)
                //检测件数据更新
                detectIfNeed()
            }
            //请求渲染
            makeupProcessor.requestRender()
            // 动画已经执行完毕
            if (studioViewModel.screenTouchEnable && !studioViewModel.hasMultiFace()) {
                //显示订阅状态
                showPro(
                    if (makeupViewModel.isApplyNeedPay()) {
                        ProView.Mode.PROBANNER
                    } else {
                        ProView.Mode.NONE
                    }
                )
                studioViewModel.setProStateEvent.value =
                    Pair(first = makeupViewModel.isApplyNeedPay(), second = true)
            }
            subProViewModel.judgeToShowBottomProBanner(
                hasEffect = makeupViewModel.isApplyNeedPay(),
                canFreeUseOnce = freeUseInfo.canFreeUseOnce
            )
        })

        makeupViewModel.maskTypeEvent.observe(viewLifecycleOwner) {
            if (it.isNullOrEmpty()) {
                mViewBinding.maskContainer.hideAll()
                mViewBinding.maskContainer.gone()
            } else {
                mViewBinding.maskContainer.showMask(it)
            }
        }

        MakeupMaterialRepository.loadStateEvent.observe(
            viewLifecycleOwner,
            object : NoStickLiveData.CustomObserver<Int?>() {
                override fun onAccept(it: Int?) {
                    var needRefresh = !makeupViewModel.hasOnlineMakeupMaterials
                    if (needRefresh && it == LoadState.LOADING) {
                        makeupViewModel.maskTypeEvent.value = MaskType.Loading
                    } else {
                        kotlin.run {
                            makeupViewModel.dataEvent.value?.forEach { makeupGroup ->
                                makeupGroup.makeupMaterials.forEach {
                                    if (!it.isInsideMaterial() && it.url.isNullOrEmpty()) {
                                        needRefresh = true
                                        return@run
                                    }
                                }
                            }
                        }
                        if (needRefresh && (it == LoadState.SUCCEED || it == LoadState.FAILED)) {
                            lifecycleScope.launch(Dispatchers.IO) {
                                makeupViewModel.initMakeup(mode = MakeupViewModel.ImageMakeup)
                            }
                        }
                    }
                }
            }
        )

        subProViewModel.showBottomProBannerEvent.observe(viewLifecycleOwner) {
            if (it) {
                // 更换激励视频横幅样式
                if (DailyMembershipUnlocker.shouldShowAd()) {
                    mViewBinding.proBottomBanner.setCurrentStatus(ProBottomBannerComponent.WATCH_REWARDED_DAILY_UNLOCK_ALL_BANNER)
                } else if (studioViewModel.shouldShowRewardDialog) {
                    mViewBinding.proBottomBanner.setCurrentStatus(ProBottomBannerComponent.WATCH_REWARDED_UNLOCK_ALL_BANNER)
                }

                mViewBinding.flBottomBanner.show(1, height = 52.dp(), isShow = true)
            } else {
                mViewBinding.flBottomBanner.show(1, height = 12.dp(), isShow = false)
            }
        }
        subProViewModel.watchRewardAdUnlockAllDialogEvent.observe(viewLifecycleOwner) {
            if (it) {
                onRewardHintClick()
            }
        }
        subProViewModel.watchRewardAdDailyUnlockDialogEvent.observe(viewLifecycleOwner) {
            if (it) {
                onRewardHintClick()
            }
        }

        lifecycleScope.launch {
            makeupViewModel.initMakeup(mode = MakeupViewModel.ImageMakeup)
        }
    }

    private fun onRewardHintClick() {
        rewardChain.rewardConfig?.run {
            this.limitFreeUse = false
            this.hasPaidEffect = true
        }
        val builder = getRewardDialogBuilder(
            modularSource = "编辑器",
            triggerSource = StudioProViewModel.TRIGGER_FROM_HINT,
        )
        rewardChain.handlerRequest(builder)
    }

    /*
    * 更新检测数据，会根据ar配置是否需要才检测，且只检测一次
    * */
    @GlThread
    private fun detectIfNeed() {
        // 头发分割
        if (makeupProcessor.renderProxy.isNeedRecognizeData(HairMaskData::class.java)) {
            makeupProcessor.apply {
                detectData.generateHairMask()       // 同一张图，只会检测一次。
            }
        }
        // Dl3D
        if (makeupProcessor.renderProxy.isNeedRecognizeData(DL3DData::class.java)
            || makeupProcessor.renderProxy.isNeedRecognizeData(DL3DData.DL3DNetData::class.java)
        ) {
            makeupProcessor.apply {
                detectData.generateDL3D()           // 同一张图，只会检测一次。
            }
        }
    }


    override fun onGlResourceInit() {
        addProcessor(makeupProcessor)
        makeupProcessor.renderProxy.setEglProvider(null)
    }

    override fun onGlResourceRelease() {
        super.onGlResourceRelease()
//        makeupProcessor.onGlResourceRelease()
    }

    /**
     * ViewPager适配器
     */
    inner class MakeupChildPageAdapter(fragment: Fragment) : FragmentStateAdapter(fragment) {

        override fun getItemCount(): Int {
            return groups?.size ?: 0
        }

        override fun createFragment(position: Int): Fragment {
            return StudioMakeupChildFragment.newInstance(position)
        }
    }

    /**
     * 效果参数。
     */
    override var effectResult: MakeupResult
        get() = makeupViewModel.makeupEffect
        set(value) {}


    override fun afterAnimateIn() {
        super.afterAnimateIn()
        if (makeupViewModel.isApplyMakeup() && !studioViewModel.hasMultiFace()) {
            // 协议跳转走这里提示
            showPro(
                if (makeupViewModel.isApplyNeedPay()) {
                    ProView.Mode.PROBANNER
                } else {
                    ProView.Mode.NONE
                }
            )
            studioViewModel.setProStateEvent.value =
                Pair(first = makeupViewModel.isApplyNeedPay(), second = true)
        }
        faceSelectLayer?.layerView?.alpha = 1f
    }

    override fun onClickConfirm() {
        val map = MakeupResult.generateStatisticMap(effectResult)
        if (map.isNotEmpty()) {
            map.putAll(SPMManager.instance.getCurrentSpmInfo())
            MTAnalyticsAgent.logEvent(MTAnalyticsConstant.BEAUTY_MAKEUP_MATERIAL_YES, map)
        }

        if (makeupViewModel.isApplyNeedPay() && !SubscribeConfig.isSubValid() && !freeUseInfo.canFreeUseOnce) {
            rewardChain.rewardConfig?.run {
                this.limitFreeUse = false
                this.hasPaidEffect = true
            }
            val builder = getRewardDialogBuilder(
                modularSource = "编辑器",
                triggerSource = StudioProViewModel.TRIGGER_FROM_YES,
                rewardCallback = { isSucceed, rewardType ->
                    if (isSucceed) {
                        confirmEffect(makeupViewModel.makeupEffect)
                    }
                },
                subCallback = {
                    if (it) {
                        confirmEffect(makeupViewModel.makeupEffect)
                    }
                }
            )
            if (rewardChain.handlerRequest(builder)) {
                return
            }

            SPMShare.put(SpmParamConstant.KEY_SOURCE_CLICK_POSITION, "打勾确认")
            subProViewModel.toPro(
                ownerActivity,
                subModuleEnum,
                makeupViewModel.makeupEffect
            ) { isSubscribe, isCanFreeUse ->
                if (isSubscribe) {
                    showPro(ProView.Mode.NONE)
                }
                if (isSubscribe || isCanFreeUse) {
                    confirmEffect(makeupViewModel.makeupEffect)
                }
            }
        } else {
            confirmEffect(makeupViewModel.makeupEffect)
        }
    }

    /**
     * 显示对应分组
     */
    private fun show(makeupGroup: MakeupGroup?) {
        if (group == makeupGroup) {
            return
        }
        group = makeupGroup
        hideAllRight()
        makeupGroup?.takeIf { it.makeupColorMaterials.isNotEmpty() }?.let { group ->
            var tag = "Makeup_style" + group.makeupType
            val fg = createOrFind(MakeupVerticalFragment::class.java, tag)
            fg?.let {
                showFragment(fg, tag, group.makeupType)
            }
        }
    }

    /**
     * 显示对应Fragment
     */
    private fun showFragment(
        baseBottomSubFragment: BaseBottomSubFragment?,
        tag: String,
        makeupType: Int
    ) {
        baseBottomSubFragment?.let {
            it.arguments = Bundle().apply {
                putInt("MakeupType", makeupType)
                putBoolean("isCamera", false)
            }
            mCurrentSubFragment = it
            if (mCurrentSubFragment!!.isAdded) {
                fm.beginTransaction()
                    .setCustomAnimations(
                        R.anim.slide_right_in,
                        R.anim.slide_right_out
                    )
                    .show(mCurrentSubFragment!!)
                    .commitAllowingStateLoss()
            } else {
                fm.beginTransaction()
                    .setCustomAnimations(
                        R.anim.slide_right_in,
                        R.anim.slide_right_out
                    )
                    .add(R.id.fl_right, mCurrentSubFragment!!, tag)
                    .commitAllowingStateLoss()
            }
        }
    }

    /**
     * 隐藏所有Bottom
     */
    private fun hideAllRight() {
        mCurrentSubFragment?.let {
            fm!!.beginTransaction()
                .setCustomAnimations(
                    R.anim.slide_right_in,
                    R.anim.slide_right_out
                )
                .hide(mCurrentSubFragment!!)
                .commitAllowingStateLoss()
            mCurrentSubFragment = null
        }
    }

    /**
     * 寻找或者创建对应状态的SubFragment
     *
     * @param function
     * @return
     */
    private fun createOrFind(
        fgClass: Class<out BaseBottomSubFragment?>,
        tag: String
    ): BaseBottomSubFragment? {
        var fg: Fragment? = fm.findFragmentByTag(tag)
        if (fg == null) {
            try {
                fg = fgClass.newInstance()
            } catch (e: IllegalAccessException) {
                e.printStackTrace()
            } catch (e: InstantiationException) {
                e.printStackTrace()
            }
        }
        return fg as BaseBottomSubFragment?
    }

    /**
     * 显示悬浮Tips
     */
    private fun showFloatingTips(
        tips: String?,
        leftToRight: Boolean = true,
        colorStr: String? = null
    ) {
        tips?.let {
            if (TextUtils.isEmpty(colorStr)) {
                mViewBinding.flColor.gone()
            } else {
                mViewBinding.flColor.visible()
                mViewBinding.flColor.delegate.backgroundColor = Color.parseColor(colorStr)
            }
            mViewBinding.rlFloating.animate().setStartDelay(0).setListener(null).cancel()
            mViewBinding.rlFloating.visible()
            mViewBinding.rlFloating.alpha = 0f
            mViewBinding.tvFloating.text = tips
            mViewBinding.rlFloating.translationX = if (leftToRight) {
                (-DeviceUtils.dip2px(30f)).toFloat()
            } else {
                (DeviceUtils.dip2px(30f)).toFloat()
            }
            mViewBinding.rlFloating.animate()
                .alpha(1f)
                .setStartDelay(0)
                .translationX(0f)
                .setInterpolator(DecelerateInterpolator())
                .setDuration(1000)
                .setListener(object : AnimatorListenerAdapter() {

                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        mViewBinding.rlFloating.animate().setListener(null)
                            .alpha(0f)
                            .translationX(
                                if (leftToRight) {
                                    (DeviceUtils.dip2px(30f)).toFloat()
                                } else {
                                    (-DeviceUtils.dip2px(30f)).toFloat()
                                }
                            )
                            .setInterpolator(AccelerateInterpolator())
                            .setDuration(1000)
                            .start()
                    }
                })
                .start()
        }
    }

    var isShowMakeupGuide = false

    fun showMakeupVerticalScroll(isShow: Boolean) {
        if (isShowMakeupGuide == isShow) {
            return
        }
        this.isShowMakeupGuide = isShow
        if (isShowMakeupGuide) {
            //不再显示引导
            if (!MakeupConfig.isShowMakeupVerticalGuide()) {
                return
            }
        }
        mViewBinding.llMakeupScrollTips.animate().cancel()
        if (this.isShowMakeupGuide) {
            mViewBinding.llMakeupScrollTips.setAlpha(0f)
            mViewBinding.llMakeupScrollTips.pivotY =
                0.5f * mViewBinding.llMakeupScrollTips.height.toFloat()
            mViewBinding.llMakeupScrollTips.pivotX = mViewBinding.llMakeupScrollTips.width.toFloat()
            mViewBinding.llMakeupScrollTips.setScaleX(0f)
            mViewBinding.llMakeupScrollTips.setScaleY(0f)
            mViewBinding.llMakeupScrollTips.animate().alpha(1f).setDuration(200).scaleX(1f)
                .scaleY(1f).start()
            mViewBinding.llMakeupScrollTips.postDelayed(Runnable {
                showMakeupVerticalScroll(false)
                MakeupConfig.setShowMakeupVerticalGuide(false)
            }, 3000)
        } else {
            mViewBinding.llMakeupScrollTips.animate().alpha(0f).setDuration(200).scaleX(0f)
                .scaleY(0f).start()
        }
    }

    override fun focusFaceImmediatelyOnConfigurationChanged(): Boolean {
        return true
    }
}