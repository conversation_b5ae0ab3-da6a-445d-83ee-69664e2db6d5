package com.commsource.studio.function.plump

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.commsource.beautyplus.free.FreeFeatureManager
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.studio.effect.PictureResult
import com.commsource.studio.function.plump.model.AUTO_PLUMP_LOG_KEY
import com.commsource.studio.function.plump.model.PlumpEffectWrapper
import com.commsource.studio.function.plump.model.logKey
import com.meitu.library.hwanalytics.spm.SPMManager

class FacePlumpViewModel(application: Application) : AndroidViewModel(application) {
    private val _plumpLiveData = MutableLiveData<PlumpEffectWrapper>()
    val plumpLiveData: LiveData<PlumpEffectWrapper> get() = _plumpLiveData

    private val _faceMapLiveData = MutableLiveData<HashMap<Int, Int>>()
    val faceMapLiveData: LiveData<HashMap<Int, Int>> get() = _faceMapLiveData

    private var isFocusFace = false
    private var isShowBling = false

    fun updateEffect(effect: PlumpEffectWrapper) {
        _plumpLiveData.value = effect
    }

    fun updateFaceMap(faceIndex: Int, plumpIndex: Int) {
        val faceMap = _faceMapLiveData.value ?: hashMapOf()
        faceMap[faceIndex] = plumpIndex
        _faceMapLiveData.value = faceMap
    }

    fun setFocusFace() {
        isFocusFace = true
    }

    fun isFocusFace(): Boolean {
        return isFocusFace
    }

    fun setShowBling() {
        isShowBling = true
    }

    fun isShowBling(): Boolean {
        return isShowBling
    }

    fun logClickEvent(value: String) {
        MTAnalyticsAgent.logEvent(MTAnalyticsConstant.BEAUTY_PLUMP_CLK, "子功能", value)
    }

    fun logConfirmEvent(wrapper: PlumpEffectWrapper, buyString: String) {
        val map = hashMapOf<String, String>()
        if (wrapper.autoMode) {
            map[AUTO_PLUMP_LOG_KEY] = "100"
        }
        wrapper.plumpList.filter { it.progress > 0 }.forEach {
            map[it.plumpEffectEnum.logKey()] = it.progress.toString()
        }
        map["是否购买"] = buyString
        map.putAll(SPMManager.instance.getCurrentSpmInfo())
        MTAnalyticsAgent.logEvent(MTAnalyticsConstant.PLUMP_YES, map)
    }
}