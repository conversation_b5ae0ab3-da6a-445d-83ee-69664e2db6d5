package com.commsource.studio.function.mosaic

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.text.TextPaint
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.forEach
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.airbnb.lottie.LottieCompositionFactory
import com.airbnb.lottie.LottieDrawable
import com.commsource.ad.DailyMembershipUnlocker
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentMosaicBinding
import com.commsource.beautyplus.router.RouterEntity
import com.commsource.beautyplus.router.UriConstant
import com.commsource.beautyplus.start.AWRemoteConfig
import com.commsource.camera.util.animationTransition
import com.commsource.comic.widget.ProgressDialog
import com.commsource.config.ApplicationConfig
import com.commsource.config.SubscribeConfig
import com.commsource.easyeditor.utils.opengl.EglThread
import com.commsource.statistics.ABTestManager
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.SpmParamConstant
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.studio.BrushDebugConfig
import com.commsource.studio.ImageStudioViewModel
import com.commsource.studio.MatrixBox
import com.commsource.studio.SVGTools
import com.commsource.studio.StudioConfig
import com.commsource.studio.component.ConfirmCancelComponent
import com.commsource.studio.component.ProBottomBannerComponent
import com.commsource.studio.component.StudioProViewModel
import com.commsource.studio.component.UndoRedoComponent
import com.commsource.studio.doodle.ColorPickerLayer
import com.commsource.studio.effect.MosaicResult
import com.commsource.studio.effect.PictureResult
import com.commsource.studio.function.BaseSubFragment
import com.commsource.studio.function.removebg.RemoveBgViewModel
import com.commsource.studio.layer.PaintMaskLayer
import com.commsource.studio.processor.MosaicProcessor
import com.commsource.util.BPLocationUtils
import com.commsource.util.ErrorNotifier
import com.commsource.util.Function
import com.commsource.util.ResourcesUtils
import com.commsource.util.UIHelper
import com.commsource.util.XFunctionFragmentHelper
import com.commsource.util.dp
import com.commsource.util.dpf
import com.commsource.util.gone
import com.commsource.util.isVisible
import com.commsource.util.processSize
import com.commsource.util.reverseMask
import com.commsource.util.safeGet
import com.commsource.util.setSize
import com.commsource.util.text
import com.commsource.util.visible
import com.commsource.widget.ProView
import com.commsource.widget.dialog.common.ViewPagerDialog
import com.commsource.widget.dialog.delegate.CloseButton
import com.commsource.widget.dialog.delegate.Content
import com.commsource.widget.dialog.delegate.NegativeButton
import com.commsource.widget.dialog.delegate.PositiveButton
import com.commsource.widget.dialog.delegate.ProButton
import com.commsource.widget.dialog.delegate.Title
import com.commsource.widget.dialog.delegate.VideoPictureTips
import com.commsource.widget.dialog.delegate.XDialog
import com.commsource.widget.dialog.delegate.config.VideoConfig
import com.commsource.widget.dialog.delegate.popupCenter
import com.meitu.common.AppContext
import com.meitu.common.utils.ToastUtils
import com.meitu.http.exception.HttpException
import com.meitu.library.hwanalytics.spm.SPMManager
import com.meitu.library.hwanalytics.spm.SPMShare
import com.meitu.library.util.device.DeviceUtils
import com.meitu.library.util.net.NetUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.collections.set

/**
 * 马赛克页面
 * Created on 2020/8/26
 * <AUTHOR>
 */
class MosaicFragment : BaseSubFragment<MosaicResult>() {
    /**
     * 效果参数。
     */
    override var effectResult = MosaicResult()

    private val viewBinding: FragmentMosaicBinding by lazy {
        FragmentMosaicBinding.inflate(
            layoutInflater
        )
    }

    private var mosaicProcessor: MosaicProcessor? = null

    private val scrawlLayer: PaintMaskLayer by lazy {

        object : PaintMaskLayer(requireContext()) {
            override fun onCanvasMatrixChange(canvasMatrix: MatrixBox, isRefreshNow: Boolean) {
                super.onCanvasMatrixChange(canvasMatrix, isRefreshNow)
                mosaicProcessor?.penSizeScale = canvasMatrix.getScale()
            }

            override fun onCanvasGestureMatrixChange(matrixBox: MatrixBox) {
                super.onCanvasGestureMatrixChange(matrixBox)
                mosaicProcessor?.viewScale = matrixBox.getScale()
            }

        }.apply {
            needRecordMaskStack = true
            withHardness = true
            isNeedFitScale = false
        }
    }

    /**
     * 颜色取色
     */
    private val colorPickerLayer: ColorPickerLayer by lazy {
        ColorPickerLayer(requireContext()).apply {
            colorChangeListener = object : ColorPickerLayer.ColorChangeListener {

                override fun onColorChange(color: Int) {
                    mViewModel.colorPickerEvent.value?.onColorChange(color)
                }

                override fun onColorSelected(color: Int) {
                    mViewModel.colorPickerEvent.value?.onColorSelected(color)
                }
            }
        }
    }

    private val mViewModel by lazy { ViewModelProvider(this).get(MosaicViewModel::class.java) }
    private val removeBgViewModel by lazy { ViewModelProvider(this).get(RemoveBgViewModel::class.java) }

    private var progressDialog: ProgressDialog? = null
    private var netErrorDialog: XDialog? = null

    override var panelHeight: Float = (177 + 48).dpf

    /**
     * 是否第一次加载素材
     */
    private var isFirstLoadMaterial = true

    /**
     * 素材和颜色列表
     */
    private val functionHelper by lazy {
        XFunctionFragmentHelper(
            childFragmentManager, R.id.fl_content, false, null
        )
    }

    /**
     * 撤销重做VM
     */
    private val undoRedoViewModel by lazy { ViewModelProvider(this).get(UndoRedoComponent.UndoRedoViewModel::class.java) }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        addSubLayer(scrawlLayer)
        addSubLayer(colorPickerLayer)
        colorPickerLayer.isEnable = false
        return viewBinding.apply {
            fragment = this@MosaicFragment
            executePendingBindings()
        }.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        undoRedoViewModel  // 在fragment创建时先创建好此VM。
        addTransitionView(viewBinding.contrast)
        addTransitionView(viewBinding.preview)
        addTransitionView(viewBinding.undoRedo)
        addTransitionView(viewBinding.flBottomBanner)
        // 初始化设置非付费状态
        setupBackgroundBtn()
        setupPaintEraser()
        setupShapeContainer()
        if (StudioConfig.isFirstEnterNewMosaic) {
            StudioConfig.setNotFirEnterMosaic()
            showMosaicHelpDialog()
        }
        //动态设置宽度
        val paint = TextPaint().apply {
            textSize = 15.dpf
        }
        val validWidth = ((paint.measureText(R.string.t_elimination_mode_classic.text())
            .coerceAtLeast(paint.measureText(R.string.t_color.text()))) * 2 + 20.dpf)
            .coerceAtLeast(226.dpf)
        viewBinding.xsbg.setSize(width = validWidth.toInt())

        studioViewModel.setProStateEvent.value = Pair(first = false, second = false)

        viewBinding.xsbg.setSelected(0)
        viewBinding.xsbg.setOnSelectListener { position, fromUser ->
            if (fromUser) {
                mViewModel.colorPickerEvent.value = null
                mViewModel.selectPage(position)
            }
        }

        lifecycleScope.launch {
            mViewModel.selectPageEvent.collect {
                viewBinding.xsbg.selectPosition(it)
                when (it) {
                    0 -> {
                        functionHelper.showFunction(
                            Function(
                                "Material", MosaicMaterialFragment::class.java, isFromUser = true
                            )
                        )
                    }

                    else -> {
                        functionHelper.showFunction(
                            Function(
                                "Color", MosaicColorFragment::class.java, isFromUser = true
                            )
                        )
                    }
                }
                //根据模式的切换 尝试切换素材
                runOnInitComplete {
                    when (it) {
                        0 -> mosaicProcessor?.applyMaterialMode = MosaicProcessor.ClassicMode
                        else -> mosaicProcessor?.applyMaterialMode = MosaicProcessor.ColorMode
                    }
                }
            }
        }

        studioViewModel.gestureEvent.observe(viewLifecycleOwner) {

            viewBinding.root.animationTransition {
                if (it == true) {
                    viewBinding.undoRedo.alpha = 0f
                    viewBinding.preview.alpha = 0f
                    viewBinding.contrast.alpha = 0f
                } else {
                    viewBinding.undoRedo.alpha = 1f
                    viewBinding.preview.alpha = 1f
                    viewBinding.contrast.alpha = 1f
                }
            }
        }
        undoRedoViewModel.updateStateEvent.observe(viewLifecycleOwner) {
            val useProMaterial = mosaicProcessor?.mosaicEntityStack?.getList()?.find {
                it.needPaid()
            } != null
            subProViewModel.judgeToShowBottomProBanner(
                hasEffect = useProMaterial,
                canFreeUse = !useProMaterial,
                canFreeUseOnce = freeUseInfo.canFreeUseOnce
            )
        }
        subProViewModel.showBottomProBannerEvent.observe(viewLifecycleOwner) {
            if (it) {
                // 更换激励视频横幅样式
                if (DailyMembershipUnlocker.shouldShowAd()) {
                    viewBinding.proBottomBanner.setCurrentStatus(ProBottomBannerComponent.WATCH_REWARDED_DAILY_UNLOCK_ALL_BANNER)
                } else if (studioViewModel.shouldShowRewardDialog) {
                    viewBinding.proBottomBanner.setCurrentStatus(ProBottomBannerComponent.WATCH_REWARDED_UNLOCK_ALL_BANNER)
                }

                viewBinding.flBottomBanner.show(1, height = 52.dp(), isShow = true)
            } else {
                viewBinding.flBottomBanner.show(1, height = 12.dp(), isShow = false)
            }
        }
        subProViewModel.watchRewardAdUnlockAllDialogEvent.observe(viewLifecycleOwner) {
            if (it) {
                onRewardHintClick()
            }
        }
        subProViewModel.watchRewardAdDailyUnlockDialogEvent.observe(viewLifecycleOwner) {
            if (it) {
                onRewardHintClick()
            }
        }

        viewBinding.proBottomBanner.setBannerClickListener {
            mosaicProcessor?.run {
                effectResult.hasPaint = undoRedoViewModel.canUndo()
                effectResult.mosaicMaterials.clear()
                effectResult.mosaicMaterials.addAll(mosaicEntityStack.getList())
            }

        }
    }

    private fun onRewardHintClick() {
        rewardChain.rewardConfig?.run {
            this.limitFreeUse = false
            this.hasPaidEffect = true
        }
        val builder = getRewardDialogBuilder(
            modularSource = "编辑器",
            triggerSource = StudioProViewModel.TRIGGER_FROM_HINT,
        )
        rewardChain.handlerRequest(builder)
    }

    override fun onGlResourceInit() {
        mosaicProcessor = MosaicProcessor().apply {
            addProcessor(this)
            onProState = {
                UIHelper.runOnUiThread {
                    showPro(
                        if (it) {
                            studioViewModel.proBannerTipEvent.value =
                                arrayListOf(AppContext.context.getString(R.string.t_join_unlock))
                            ProView.Mode.PROBANNER
                        } else {
                            ProView.Mode.NONE
                        }
                    )
                }
            }
            undoRedoClick = {
                mViewModel.colorPickerEvent.postValue(null)
            }

            onGestureStateChange = {
                studioViewModel.gestureEvent.postValue(it)
            }
        }
        // 初始化GL放大镜。
        viewBinding.manify.initMagnifyTexture(
            (studioViewModel.glRenderer.context.eglProvider as EglThread).shareContext,
            subEffectFBOEntity!!
        )
    }

    override fun onGlResourceRelease() {
        super.onGlResourceRelease()
        undoRedoViewModel.clearStackAction()
    }

    override fun onInitComplete() {
        initViewModel()
        mosaicProcessor?.addObserver(this)
    }

    /**
     * 历史模块，在素材加载完后在执行协议。
     */
    private fun onFirstMaterialLoad(materials: List<MosaicMaterial>) {
        if (!isFirstLoadMaterial) {
            return
        }
        isFirstLoadMaterial = false
        val routerEntity = getRouterEntity()
        routerEntity?.apply {
            getParameter(UriConstant.KEY_PARAM_SIZE)?.let {
                viewBinding.paintEraser.setProgress(it.toInt())
            }
        }
        val mosaicId = routerEntity?.getParameter(UriConstant.KEY_PARAM_CONTENT)
        var entity: MosaicMaterial? = null
        run {
            materials.forEach {
                if (mosaicId == it.id) {
                    entity = it
                    return@run
                }
            }
        }
        (entity ?: materials.safeGet(0))?.let {
            handleProtocol(it)
        }
    }

    private fun handleProtocol(mosaicBean: MosaicMaterial) {
        if (mosaicBean.needDownload()) {
            if (!NetUtils.canNetworking(AppContext.context)) {
                ErrorNotifier.showNetworkErrorToast()
                return
            }
            mViewModel.shouldApplyMaterial = mosaicBean
            MosaicRepository.download(mosaicBean, needProgressDialog = true)
        } else {
            mViewModel.applyMaterialEvent.value = mosaicBean
        }
    }

    override fun go2Pro(fromProView: Boolean) {
        mViewModel.colorPickerEvent.value = null
        // 更新马赛克使用情况
        mosaicProcessor?.let {
            effectResult.mosaicMaterials.clear()
            effectResult.mosaicMaterials.addAll(it.mosaicEntityStack.getList().filter {
                it.id != MosaicProcessor.BG_FUNC_ID && it.id != MosaicProcessor.COLOR_ID
            })
        }
        super.go2Pro(fromProView)
    }

    override fun onClickConfirm() {
        rewardChain.rewardConfig?.run {
            this.limitFreeUse = false
            this.hasPaidEffect = mosaicProcessor?.mosaicEntityStack?.getList()?.find {
                it.needPaid()
            } != null
        }

        mosaicProcessor?.let { mosaicProcessor ->
            effectResult.hasPaint = undoRedoViewModel.canUndo()
            effectResult.mosaicMaterials.clear()
            effectResult.mosaicMaterials.addAll(mosaicProcessor.mosaicEntityStack.getList())
            MTAnalyticsAgent.logEvent(MTAnalyticsConstant.EVENT_MOSAICYES,
                HashMap<String, String>().apply {
                    var idString = ""
                    effectResult.mosaicMaterials.filter { it.id != MosaicProcessor.COLOR_ID }
                        .distinct().forEach {
                            idString += it.id + ","
                        }
                    val noColorMaterials =
                        effectResult.mosaicMaterials.filter { it.id == MosaicProcessor.COLOR_ID }
                            .isEmpty()
                    if (!TextUtils.isEmpty(idString)) {
                        this["马赛克素材id"] = idString.dropLast(1)
                    }
                    this["纯色"] = if (noColorMaterials) "0" else "1"

                    this["是否购买"] = PictureResult.getStatisticPurchaseValue(
                        limitFree = false,
                        canFreeUse = false,
                        isShowAdDialog = rewardChain.isCanShow(),
                        isAdUnlockUse = freeUseInfo.canFreeUseOnce
                    )
                    putAll(SPMManager.instance.getCurrentSpmInfo())
                })
        }
        mViewModel.shouldApplyMaterial = null

        val builder = getRewardDialogBuilder(
            modularSource = "编辑器",
            triggerSource = StudioProViewModel.TRIGGER_FROM_YES,
            rewardCallback = { isSucceed, rewardType ->
                if (isSucceed) {
                    super.onClickConfirm()
                }
            },
            subCallback = {
                if (it) {
                    super.onClickConfirm()
                }
            }
        )
        if (rewardChain.handlerRequest(builder)) {
            return
        }
        super.onClickConfirm()
    }

    override fun onClickExit() {
        super.onClickExit()
        mViewModel.shouldApplyMaterial = null
    }

    private fun initViewModel() {

        //马赛克素材
        mViewModel.applyMaterialEvent.observe(viewLifecycleOwner) { mosaic ->
            mosaic?.let {
                if (mosaicProcessor?.isEraserMode() == true) {
                    viewBinding.paintEraser.selectPen()
                }
                applyMosaic(it)
            }
        }

        //套用的颜色素材
        mViewModel.applyColorEvent.observe(viewLifecycleOwner) {
            it?.let {
                if (mosaicProcessor?.isEraserMode() == true) {
                    viewBinding.paintEraser.selectPen()
                }
                mosaicProcessor?.setPenSize(viewBinding.paintEraser.getSize(false))
                mosaicProcessor?.applyColor(it)
            }
        }

        studioViewModel.debugBrushOkEvent.observe(viewLifecycleOwner) {
            if (it == true) {
                BrushDebugConfig.brushParams?.apply {
                    mosaicProcessor?.setPenParams(this[0] / 100f, this[1] / 100f, this[2] / 100f)
                }
            }
        }

        mViewModel.colorPickerEvent.observe(viewLifecycleOwner) {
            if (it == null) {
                mosaicProcessor?.isGestureEnable = true
                scrawlLayer.isEnable = mosaicProcessor?.isBgMode() != true
                colorPickerLayer.isEnable = false
            } else {
                mosaicProcessor?.requestRender {
                    val currentEffectBitmap = mosaicProcessor?.disFBOEntity?.generateBitmap()
                    colorPickerLayer.setBitmap(currentEffectBitmap)
                    UIHelper.runOnUiThread(requireContext()) {
                        colorPickerLayer.isEnable = true
                        mosaicProcessor?.isGestureEnable = false
                        scrawlLayer.isEnable = false
                    }
                }
            }
        }

        //转发
        mosaicProcessor?.uiApplyColorEvent?.observe(viewLifecycleOwner) {
            mViewModel.uiApplyColorEvent.value = it
        }
        mosaicProcessor?.uiApplyMaterialEvent?.observe(viewLifecycleOwner) {
            mViewModel.uiApplyMaterialEvent.value = it
        }

        getViewModel(
            ConfirmCancelComponent.ConfirmCancelViewModel::class.java
        ).apply {
            helpIconClickEvent.observe(viewLifecycleOwner) {
                showMosaicHelpDialog()
            }
        }

        //马赛克数据源
        lifecycleScope.launch {
            mViewModel.dataEvent.collect {
                runOnInitComplete {
                    onFirstMaterialLoad(it.materials)
                }
            }
        }
    }

    private fun applyMosaic(mosaicBean: MosaicMaterial) {
        val isEraser = mosaicBean.isEraserMaterial()
        mosaicProcessor?.setPenSize(viewBinding.paintEraser.getSize(isEraser))
        scrawlLayer.updateSize(viewBinding.paintEraser.getSize(isEraser))
        mosaicProcessor?.applyMosaicMaterial(mosaicBean)

    }

    override fun setCurrentRouterParams(routerEntity: RouterEntity) {
        routerEntity.addParameter(
            UriConstant.KEY_PARAM_CONTENT, mViewModel.applyMaterialEvent.value?.id
        )
        routerEntity.addParameter(
            UriConstant.KEY_PARAM_STRENGTH, viewBinding.paintEraser.getSize(true).toString()
        )
    }


    /**
     * 初始化形状视图
     */
    fun setupShapeContainer() {
        // 加载SVG 图形内容
        lifecycleScope.launch(Dispatchers.IO) {
            listOf(
                SVGTools.SHAPE_RECTANGLE,
                SVGTools.SHAPE_TRIANGLE,
                SVGTools.SHAPE_OVAL,
                SVGTools.SHAPE_HEART
            ).map {
                SVGTools.getSVGData(SVGTools.SVG(it, ""))
            }.let {
                withContext(Dispatchers.Main) {
                    viewBinding.shapeContainer.setData(it)
                }
            }
        }
        // shape 点击回调
        viewBinding.shapeContainer.apply {
            onShapeClick = {
                changeMode(MosaicProcessor.ScrawlModeShape, svgData = it)
            }
        }

        // 展开图形选择组件回调
        viewBinding.shapeContainer.onTriggerExpend = {
            changeMode(MosaicProcessor.ScrawlModeShape, svgData = it)
        }
    }

    private fun getExpendSize(): Int {
        val lrMargin = 32.dp()
        val peShrinkWidth = 84.dp()
        val iconPadding = if (viewBinding.bgEffect.isVisible) 24.dp() else 12.dp()
        val bgIconWidth = if (viewBinding.bgEffect.isVisible) 36.dp() else 0
        // 动态计算shapeContainer 展开可以分配到的大小 . // 要适配机型。
        return DeviceUtils.getScreenWidth() - lrMargin - iconPadding - peShrinkWidth - bgIconWidth
    }


    private fun setupPaintEraser() {
        // 尺寸变更回调
        viewBinding.paintEraser.onSizeChangeListener = { state, isFromUser, size ->
            if (state == 0 && isFromUser) {
                scrawlLayer.showPenInCenter(true)
            } else if (state == 2) {
                scrawlLayer.showPenInCenter(false)
            }
            scrawlLayer.updateSize(size)
            mosaicProcessor?.setPenSize(size)
        }
        // 画笔组件选中变更回调
        viewBinding.paintEraser.onSelectListener = {
            // 选中画笔
            if (it) {
                changeMode(MosaicProcessor.ScrawlModePen)
            } else {
                changeMode(MosaicProcessor.ScrawlModeEraser)
            }
        }
    }

    /**
     *  初始化操作栏
     */
    private fun setupBackgroundBtn() {
        viewBinding.bgEffect.onTriggerExpand = {
            when {
                StudioConfig.needShowRemoveBgUpdate() -> showUpdateDialog()
                !SubscribeConfig.isSubValid() && !freeUseInfo.canFreeUseOnce -> {
                    rewardChain.rewardConfig?.run {
                        this.limitFreeUse = false
                        this.hasPaidEffect = true
                    }
                    val builder = getRewardDialogBuilder(
                        modularSource = "编辑器",
                        triggerSource = StudioProViewModel.TRIGGER_FROM_YES,
                        rewardCallback = { isSucceed, rewardType ->
                            if (isSucceed) {
                                startOnlineSegment()
                            }
                        },
                        subCallback = {
                            if (it) {
                                startOnlineSegment()
                            }
                        }
                    )
                    if (!rewardChain.handlerRequest(builder)) {
                        showBackgroundEffectProDialog()
                    }
                }

                else -> startOnlineSegment()
            }
        }

        viewBinding.bgEffect.onOffsetXChange = {
            viewBinding.premiumIcon.translationX = it
        }

        removeBgViewModel.apply {
            // 错误监听
            errorEvent.observe(viewLifecycleOwner, Observer {
                dismissDialog()
                if (it is HttpException || it is NullPointerException) {
                    ToastUtils.showShortToast(R.string.t_remove_bg_server_error.text())
                } else {
                    showNetWorkErrorDialog()
                }
            })
            // 进度
            progressEvent.observe(viewLifecycleOwner) {
                it?.let { updateLoadingState(it) }
            }
            // 结果
            AiSegmentResultBitmapEvent.observe(viewLifecycleOwner, Observer {
                it?.let {
                    dismissDialog()
                    netErrorDialog?.dismiss()
                    it.reverseMask(2).also {
                        changeMode(MosaicProcessor.ScrawlModeBg, bgMask = it)
                    }
                }
            })
        }

        if (ABTestManager.isNeedHideVipIcon(ABTestManager.TYPE_PHOTO_EDIT)) {
            viewBinding.premiumIcon.gone()
        }
    }

    private fun startOnlineSegment() {
        //开启在线图片分割
        //发动在线处理
        if (removeBgViewModel.isNeedAiSegment()) {
            if (!NetUtils.canNetworking(AppContext.context)) {
                showNetWorkErrorDialog()
                return
            }
            //后台开关关闭，提示升级弹窗
            if (StudioConfig.needShowRemoveBgUpdate()) {
                showUpdateDialog()
                return
            }
            // 如果为空尝试拉取RemoveBg的key
            if (TextUtils.isEmpty(ApplicationConfig.getRemoveBgKey())) {
                AWRemoteConfig.loadRemoteConfig()
                return
            }
            showLoadingDialog()
            val inputBitmap = studioViewModel.imageData.image.processSize(1024)
            removeBgViewModel.aiSegment(inputBitmap, inputBitmap.width, inputBitmap.height)
        } else {
            //直接使用已经存在的mask图片作为masklayer叠加效果
            changeMode(MosaicProcessor.ScrawlModeBg)
        }
    }

    private var currentMode = 0

    /**
     * 修改模式
     */
    private fun changeMode(
        mode: Int, svgData: SVGTools.SVGData? = null, bgMask: Bitmap? = null
    ) {
        currentMode = mode
        when (mode) {
            MosaicProcessor.ScrawlModeShape, MosaicProcessor.ScrawlModeBg -> {
                mViewModel.colorPickerEvent.value = null
            }
        }
        when (mode) {
            MosaicProcessor.ScrawlModePen -> {
                // 画笔模式的UI样式硬度固定1.0f
                scrawlLayer.updateHardness(1.0f, false)
                // 处于收缩状态
                if (viewBinding.paintEraser.isShrink) {
                    // 执行展开动画
                    viewBinding.shapeContainer.toggleViewState(true)
                    viewBinding.bgEffect.toggleViewState(true)
                    viewBinding.paintEraser.expend()
                } else if (viewBinding.paintEraser.translationX < 0) {
                    // 处于展开状态,并且展示硬度滑杆展示了。
                    viewBinding.hardSeekView.animate().alpha(0f).setDuration(300).start()
                    viewBinding.toolBar.forEach {
                        it.animate().translationX(0f).setDuration(300).start()
                    }
                }
            }

            MosaicProcessor.ScrawlModeEraser -> {
                // 执行动画
                if (viewBinding.paintEraser.isShrink) {
                    viewBinding.shapeContainer.toggleViewState(true)
                    viewBinding.bgEffect.toggleViewState(true)
                    viewBinding.paintEraser.expend()
                }
            }

            MosaicProcessor.ScrawlModeBg -> {
                val peShrinkWidth = 84.dp()
                viewBinding.bgEffect.setExpendWidth(getExpendSize())
                viewBinding.bgEffect.toggleViewState(false)
                viewBinding.shapeContainer.toggleViewState(true)
                viewBinding.paintEraser.shrink(peShrinkWidth)
            }

            MosaicProcessor.ScrawlModeShape -> {
                if (viewBinding.shapeContainer.isShrink) {
                    // 展示Lottie 引导动画
                    showGuideViewIfNeed()
                    // 执行偏移动画
                    val peShrinkWidth = 84.dp()
                    val sizeW = getExpendSize()
                    viewBinding.shapeContainer.setExpendWidth(sizeW)
                    viewBinding.shapeContainer.toggleViewState(false)
                    viewBinding.bgEffect.toggleViewState(true)
                    viewBinding.paintEraser.shrink(peShrinkWidth)
                }
            }
        }
        scrawlLayer.isEnable = mode != MosaicProcessor.ScrawlModeBg
        scrawlLayer.gestureEnable = mode != MosaicProcessor.ScrawlModeBg
        scrawlLayer.penState = mode != MosaicProcessor.ScrawlModeShape
        mosaicProcessor?.changeMosaicMode(mode, svgData, bgMask, materialUpdate = true)
    }


    var mBackgroundEffectProDialog: XDialog? = null

    /**
     * 展示背景效果弹窗
     */
    private fun showBackgroundEffectProDialog() {
        mBackgroundEffectProDialog = XDialog {
            VideoPictureTips {
                videoConfig = VideoConfig(
                    com.pixocial.videokit.PlaySource.AssetsPlaySource(
                        if (BPLocationUtils.isJapan(AppContext.context) || BPLocationUtils.isThailand(
                                AppContext.context
                            ) || BPLocationUtils.isKorea(AppContext.context) || BPLocationUtils.isChina(
                                AppContext.context
                            ) || BPLocationUtils.isHongKong(AppContext.context)
                        ) {
                            "mosaicMaterial/guide/as_mosaic_premium_ba.mp4"
                        } else {
                            "mosaicMaterial/guide/na_mosaic_premium_ba.mp4"
                        }
                    ), ratio = 1f, clickContainerNeedDismiss = false
                )
                Title(R.string.t_mosaic_bgCover_subs_title.text())
                Content(R.string.t_mosaic_bgCover_subs_content.text())
                // 免费试用天数
                val days = SubscribeConfig.getYearlyFreeTrialPeriod()
                ProButton(
                    if (days > 0) {
                        AppContext.context.getString(R.string.get_a_free_trial, days)
                    } else {
                        R.string.t_continue.text()
                    }
                ) {
                    // 跳转订阅页面
                    SPMShare.put(
                        SpmParamConstant.KEY_SOURCE_FEATURE_CONTENT, "mosaic_background_hiding"
                    )
                    SPMShare.put(SpmParamConstant.KEY_SOURCE_CLICK_POSITION, "弹窗")
                    SPMShare.put(SpmParamConstant.KEY_PRF_FUNC, "mosaic_background_hiding")
                    effectResult.mosaicMaterials.clear()
                    subProViewModel.toPro(
                        ownerActivity,
                        subModuleEnum,
                        effectResult
                    ) { isSubscribe, canFreeUse ->
                        if (isSubscribe || canFreeUse) {
                            startOnlineSegment()
                        }
                    }
                    it.dismiss()
                }
                popupCenter()
                CloseButton(true) {
                    dismiss()
                }
            }
            isCancelable = false
        }
        mBackgroundEffectProDialog?.show(ownerActivity)
    }

    private fun isShowingBackgroundEffectProDialog(): Boolean {
        return mBackgroundEffectProDialog?.isShowing ?: false
    }

    /**
     * 展示新手引导视图
     */
    @SuppressLint("ClickableViewAccessibility")
    private fun showGuideViewIfNeed() {
        if (!StudioConfig.hasShowMosaicAnim() && StudioConfig.mosaicAnimCount < 2) {
            StudioConfig.setShowMosaicAnim()
            StudioConfig.increaseMosaicAnimCount()
            lifecycleScope.launch(Dispatchers.IO) {
                // 加载Lottie
                val task = LottieCompositionFactory.fromAsset(
                    AppContext.context, "mosaicMaterial/guide/mosica_anim.json"
                )
                withContext(Dispatchers.Main) {
                    // 触摸任意位置隐藏
                    viewBinding.guideViewBg.setOnTouchListener { v, event ->
                        viewBinding.guiderView.cancelAnimation()
                        viewBinding.guiderView.gone()
                        viewBinding.guideViewBg.gone()
                        viewBinding.promptText.gone()
                        false
                    }
                    viewBinding.promptText.visible()
                    viewBinding.guideViewBg.visible()
                    viewBinding.guiderView.visible()
                    task.addListener { result ->
                        result?.let {
                            viewBinding.guiderView.setComposition(it)
                            viewBinding.guiderView.repeatCount = LottieDrawable.INFINITE
                            viewBinding.guiderView.playAnimation()
                        }
                    }
                }
            }
        }
    }

    override fun onClickContrast(isContrast: Boolean) {
        mViewModel.colorPickerEvent.value = null
        super.onClickContrast(isContrast)
    }

    override fun onClickPreview(isPreview: Boolean) {
        mViewModel.colorPickerEvent.value = null
        super.onClickPreview(isPreview)
    }

    /**
     *  展示马赛克帮助弹窗
     */
    private fun showMosaicHelpDialog() {
        val imageRes = listOf(
            R.drawable.mosaic_guide_01, R.drawable.mosaic_guide_02, R.drawable.mosaic_guide_03
        )
        val contextRes = listOf(
            R.string.t_mosaic_bgCover_subs_title.text(),
            R.string.t_mosaic_banner_title_2.text(),
            R.string.t_mosaic_banner_title_3.text()
        )

        val dialog = ViewPagerDialog.Builder().setContent(contextRes).setImgRes(imageRes)
            .setShowCloseBtn(true).setAutoPlay(true).build()
        dialog.show()
    }


    private fun showNetWorkErrorDialog() {
        ErrorNotifier.showNetworkRetryDialog(onPositiveClick = {
            startOnlineSegment()
        }).also { netErrorDialog = it }
    }

    private fun showUpdateDialog() {
        XDialog {
            VideoPictureTips {
                closeEnable = false
                Title(
                    String.format(
                        ResourcesUtils.getString(R.string.t_version_update), "GooglePlay"
                    )
                )
                PositiveButton(R.string.t_goto_update.text()) {
                    it.dismissAllowingStateLoss()
                    try {
                        val intent = Intent()
                        intent.action = Intent.ACTION_VIEW
                        intent.data =
                            Uri.parse(ResourcesUtils.getString(R.string.software_grade_url))
                        if (context !is Activity) {
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        }
                        startActivity(intent)
                    } catch (ignore: Exception) {
                        ToastUtils.showLongToastSafe(R.string.open_failed)
                    }
                }
                NegativeButton(R.string.t_later_text.text()) {
                    it.dismissAllowingStateLoss()
                }
                popupCenter()
            }
        }.show(ownerActivity)
    }

    fun showLoadingDialog() {
        if (progressDialog == null) {
            progressDialog = ProgressDialog(ownerActivity, R.style.updateDialog)
            progressDialog?.setCancelListener { removeBgViewModel.cancelRequest() }
        }
        updateLoadingState(0)
        progressDialog?.setCancelable(false)
        progressDialog?.setCanceledOnTouchOutside(false)
        progressDialog?.show()
    }

    private fun updateLoadingState(progress: Int) {
        progressDialog?.takeIf { it.isShowing }?.let {
            progressDialog?.updateProgress(progress)
            val text = when {
                progress in 0..29 -> {
                    R.string.t_detecting.text()
                }

                progress in 30..79 -> {
                    R.string.t_processing.text()
                }

                else -> {
                    R.string.t_finish_processing.text()
                }
            }
            progressDialog?.updateDownloadText(text)
        }
    }

    override fun beforeAnimateOut() {
        viewBinding.guiderView.gone()
        viewBinding.guideViewBg.gone()
        viewBinding.promptText.gone()
        super.beforeAnimateOut()
    }

    private fun dismissDialog() {
        progressDialog?.takeIf {
            it.isShowing
        }?.dismiss()
        progressDialog = null
    }


    override fun onBackPress(): Boolean {
        if (viewBinding.guiderView.isVisible) {
            viewBinding.guiderView.gone()
            viewBinding.guideViewBg.gone()
            viewBinding.promptText.gone()
            return true
        }
        return false
    }

    override fun onScreenSizeConfigurationChanged() {
        if (isAdded && isShowingBackgroundEffectProDialog()) {
            mBackgroundEffectProDialog?.dismissImmediately()
            showBackgroundEffectProDialog()
        }

        when (currentMode) {
            MosaicProcessor.ScrawlModeBg -> {
                viewBinding.bgEffect.setExpendWidth(getExpendSize(), true)
                viewBinding.bgEffect.post {
                    viewBinding.bgEffect.updateOffset()
                }
            }

            MosaicProcessor.ScrawlModeShape -> {
                viewBinding.shapeContainer.setExpendWidth(getExpendSize(), true)
            }
        }
    }

}