package com.commsource.studio.function

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.beautyplus.databinding.FragmentStudioAutoBinding
import com.commsource.easyeditor.utils.opengl.FBOEntity
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.studio.StudioLayoutConstants
import com.commsource.studio.effect.AutoManualResult
import com.commsource.studio.layer.MultiFaceSelectLayer
import com.commsource.studio.processor.MultiFaceEffectProcessor
import com.commsource.studio.render.FaceFeaturesProxy
import com.commsource.studio.sub.SubModuleEnum
import com.commsource.widget.XSeekBar
import com.meitu.library.hwanalytics.spm.SPMManager

class FaceFeaturesFragment : BaseSubFragment<AutoManualResult>() {

    companion object {
        /**
         * 五官立体配置。
         */
        const val CONFIG_PATH_SHADOW_LIGHT = "rt_effect_config/shadow_light/rt_shadowlight.plist"
    }

    /**
     * 重写面板高度
     */
    override var panelHeight: Float = StudioLayoutConstants.SHORT_PANEL_HEIGHT.toFloat()

    private lateinit var mViewBinding: FragmentStudioAutoBinding

    /**
     * 处理自动磨皮效果。
     */
    private var autoProcessor: MultiFaceEffectProcessor<FaceFeaturesProxy>? = null

    /**
     * 磨皮效果参数。
     */
    override var effectResult = AutoManualResult(SubModuleEnum.Countouring)

    /**
     * 多人脸选择。
     */
    private var faceSelectLayer: MultiFaceSelectLayer? = null


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        if (studioViewModel.detectData.faceDataBox.faceCount > 1) {
            faceSelectLayer = MultiFaceSelectLayer(mActivity).apply {
                isEnableFocus = true
                addSubLayer(this)
                layerView.alpha = 0f
            }
        }
        mViewBinding = FragmentStudioAutoBinding.inflate(inflater)
        mViewBinding.fragment = this
        // 初始效果参数设置。
        if (!noFace()) {
            effectResult.setAutoEffectAlpha(50, studioViewModel.selectFaceIndex)
            mViewBinding.xsbAuto.setProgress(effectResult.getAutoEffectAlpha(studioViewModel.selectFaceIndex))
        }
        return mViewBinding.root
    }

    override fun needAutoFocusFace(): Boolean {
        return studioViewModel.detectData.faceDataBox.faceCount == 1
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addTransitionView(mViewBinding.contrast)
        addTransitionView(mViewBinding.preview)
        mViewBinding.xsbAuto.addOnProgressChangeListener(object : XSeekBar.OnProgressChangeListener {
            override fun onProgressChange(progress: Int, leftDx: Float, fromUser: Boolean) {
                if (fromUser) {
                    autoProcessor?.let { processor ->
                        effectResult.setAutoEffectAlpha(progress, processor.selectFaceIndex)
                        processor.renderProxy.effectAlpha = progress / 100f
                        processor.requestRender()
                    }
                }
            }
        })
        // 多人脸选中监听。
        studioViewModel.selectFaceEvent.observe(viewLifecycleOwner, object : NoStickLiveData.CustomObserver<Int?>() {
            override fun onAccept(faceIndex: Int?) {
                faceIndex?.let {
                    autoProcessor?.selectFaceIndex = faceIndex
                    mViewBinding.xsbAuto.setProgress(effectResult.getAutoEffectAlpha(faceIndex))
                }
            }
        })
    }


    override fun onGlResourceInit() {
        /**
         * 自动处理器。
         */
        autoProcessor = object : MultiFaceEffectProcessor<FaceFeaturesProxy>(FaceFeaturesProxy(CONFIG_PATH_SHADOW_LIGHT)) {
            /**
             * 子类要实现参数和渲染器之前的使用方式。
             */
            override fun updateParamsToRenderProxy(renderProxy: FaceFeaturesProxy, faceIndex: Int, lastEffectFBOEntity: FBOEntity) {
                renderProxy.effectAlpha = effectResult.getAutoEffectAlpha(faceIndex) / 100f
            }

        }.apply {
            addProcessor(this)
            // 默认效果。
            if (!noFace()) {
                renderProxy.effectAlpha = effectResult.getAutoEffectAlpha(studioViewModel.selectFaceIndex) / 100f
                requestRender()
            }
        }
    }


    override fun onClickConfirm() {
        MTAnalyticsAgent.logEvent(MTAnalyticsConstant.BEAUCONTOURINGYES, HashMap<String, String>().apply {
            this["五官立体滑竿值"] = AutoManualResult.getStatisticAutoValue(effectResult)
            putAll(SPMManager.instance.getCurrentSpmInfo())
        })
        confirmEffect(effectResult)
    }


    override fun afterAnimateIn() {
        super.afterAnimateIn()
        faceSelectLayer?.layerView?.alpha = 1f
    }

    override fun focusFaceImmediatelyOnConfigurationChanged(): Boolean {
        return true
    }
}