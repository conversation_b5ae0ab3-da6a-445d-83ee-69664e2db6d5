package com.commsource.studio.function.expression

import android.content.Context
import android.os.Parcelable
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemHolderRefaceBinding
import com.commsource.statistics.ABTestManager
import com.commsource.studio.ImageStudioViewModel
import com.commsource.util.LOGV
import com.commsource.util.gone
import com.commsource.util.visible
import com.commsource.widget.recyclerview.AdapterDataBuilder
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.commsource.widget.recyclerview.BaseViewHolder
import com.meitu.library.util.app.ResourcesUtils
import com.pixocial.androidx.core.extension.dp

class ReFaceViewHolder(context: Context, parent: ViewGroup) :
    BaseViewHolder<ReFace>(context, parent, R.layout.item_holder_reface) {

    val viewBinding = ItemHolderRefaceBinding.bind(itemView)

    private val recyclerViewStates = mutableMapOf<Int, Parcelable>()

    private val mAdapter = BaseRecyclerViewAdapter(context).apply {
        setOnEntityClickListener(Portrait::class.java) { pos, entity ->
            if (entity.isExpand) {
                refreshExpand()
            } else {
                val isRetry = currentPosition == pos
                setCurrentSelectPosition(pos)
                (<EMAIL> as ExpressionAdapter).onPortraitClickListener?.invoke(
                    pos,
                    entity,
                    isRetry
                )
            }
            return@setOnEntityClickListener true
        }

        setOnItemPressChangeListener { isPress, position, item ->
            if (isPress && item.entity is Portrait) {
                if ((item.entity as Portrait).isExpand) return@setOnItemPressChangeListener
                if (item.isSelect) {
                    return@setOnItemPressChangeListener
                }

                viewBinding.portraitList.layoutManager?.findViewByPosition(position)?.let {
                    val targetViewLocation = IntArray(2)
                    it.getLocationOnScreen(targetViewLocation)
                    (<EMAIL> as ExpressionAdapter).onPortraitLongClickListener?.invoke(
                        position,
                        item.entity as Portrait,
                        targetViewLocation[0],
                        targetViewLocation[1]
                    )
                }
            }
        }

        setOnEntityClickListener(String::class.java) { pos, entity ->
            refreshExpand()
            return@setOnEntityClickListener true
        }
    }

    private fun refreshExpand() {
        item.entity.expand = !item.entity.expand
        item.entity.contentChanged = true
        val position = bindingAdapterPosition
        (<EMAIL> as ExpressionAdapter).notifyItemChanged(position)
    }

    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<ReFace>,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)

        recyclerViewStates[position]?.let {
            viewBinding.portraitList.layoutManager?.onRestoreInstanceState(it)
        }

        viewBinding.tvName.text = item.entity.nameRes

//        if (item.isSelect) {
//            viewBinding.tvName.setTextColor(ResourcesUtils.getColor(R.color.Primary_A))
//            viewBinding.iv.setImageResource(R.drawable.edit_icon_reface_selected)
//            viewBinding.flSelectMask.visible()
//        } else {
//            viewBinding.tvName.setTextColor(ResourcesUtils.getColor(R.color.Gray_A))
//            viewBinding.iv.setImageResource(R.drawable.edit_icon_reface)
//            viewBinding.flSelectMask.gone()
//        }

        if (ABTestManager.isNeedHideVipIcon(ABTestManager.TYPE_PHOTO_EDIT)) {
            viewBinding.ivVip.gone()
        } else {
            if (item.entity.needPaid) {
                viewBinding.ivVip.visible()
            } else {
                viewBinding.ivVip.gone()
            }
        }

        viewBinding.portraitList.apply {
            if (adapter == null) {
                adapter = mAdapter
            }
            if (itemDecorationCount > 0) {
                removeItemDecorationAt(0)
            }
            addItemDecoration(ExpressionSubDecoration(space = 6.dp))

            if (layoutManager == null) {
                layoutManager = LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
            }
            // 恢复子 RecyclerView 状态
            recyclerViewStates[position]?.let {
                layoutManager?.onRestoreInstanceState(it)
            }
        }

        if (item.entity.portraitData.isEmpty()) {
            viewBinding.portraitList.gone()
            return
        } else {
            viewBinding.portraitList.visible()
        }
        if (item.entity.contentChanged) {
            item.entity.contentChanged = false
            if (item.entity.portraitData.size <= 2) {
                (viewBinding.portraitList.adapter as BaseRecyclerViewAdapter).apply {
                    updateItemEntities(
                        AdapterDataBuilder.create()
                            .addEntities(
                                item.entity.portraitData.map {
                                    it.apply {
                                        isExpand = false
                                    }
                                },
                                PortraitViewHolder::class.java
                            )
                            .build(),
                        false
                    )
                }
            } else {
                if (item.entity.expand) {
                    (viewBinding.portraitList.adapter as BaseRecyclerViewAdapter).apply {
                        updateItemEntities(
                            AdapterDataBuilder.create()
                                .addEntities(
                                    mutableListOf(""),
                                    ExpandViewHolder::class.java
                                )
                                .addEntities(
                                    item.entity.portraitData.map {
                                        it.apply {
                                            isExpand = false
                                        }
                                    },
                                    PortraitViewHolder::class.java
                                )
                                .build(),
                            false
                        )
                    }
                } else {
                    (viewBinding.portraitList.adapter as BaseRecyclerViewAdapter).apply {
                        updateItemEntities(
                            AdapterDataBuilder.create()
                                .addEntities(
                                    mutableListOf(item.entity.portraitData.first().apply {
                                        isExpand = true
                                    }),
                                    PortraitViewHolder::class.java
                                )
                                .build(),
                            false
                        )
                    }
                }
            }
        }

        (viewBinding.portraitList.adapter as BaseRecyclerViewAdapter).apply {
            // 刷新选中项
            if (item.entity.portraitData.size == 2) {
                setCurrentSelectPosition(item.entity.currentSelectPosition)
            } else {
                if (item.entity.expand) {
                    if (item.entity.currentSelectPosition >= 0) {
                        setCurrentSelectPosition(item.entity.currentSelectPosition + 1)
                    } else {
                        setCurrentSelectPosition(item.entity.currentSelectPosition)
                    }
                } else {
                    if (item.entity.currentSelectPosition == 0) {
                        setCurrentSelectPosition(0)
                    } else {
                        setCurrentSelectPosition(-1)
                    }
                }
            }
        }
    }

    override fun onViewRecycler() {
        super.onViewRecycler()
        val position = bindingAdapterPosition
        viewBinding.portraitList.layoutManager?.onSaveInstanceState()?.let {
            recyclerViewStates[position] = it
        }
    }
}