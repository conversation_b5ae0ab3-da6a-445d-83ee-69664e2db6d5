package com.commsource.studio.component

import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ComponentPaintEraserBinding
import com.commsource.camera.util.animationTransition
import com.commsource.studio.BrushDebugConfig
import com.commsource.studio.ImageStudioViewModel
import com.commsource.util.*
import com.commsource.widget.XSeekBar
import com.meitu.common.utils.GradientDrawableFactory
import com.meitu.library.util.device.DeviceUtils

/**
 * 画笔尺寸选择组件。
 * 内置支持两种画笔模式。
 */
class PaintEraserComponent : ComponentView {
    // 左边Icon
    private var leftIcon: String? = null

    // 右边Icon
    private var rightIcon: String? = null

    private var iconSize: Float = 0f
    private var paintProgress = 30

    var componentBg: Int? = null
        set(value) {
            field = value
            value?.let {
                viewBinding.vBg.setShapeBgColor(it)
            }
        }
    var progressBg: Int? = null

    var withStrokeColor: Int? = null
        set(value) {
            field = value
            value?.let { viewBinding.vBg.setShapeStrokeColor(0.5f.dp(), it) }
        }

    constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        // 获取XML中的属性。
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.PaintEraserComponent)
        leftIcon = typedArray.getString(R.styleable.PaintEraserComponent_icon_left_pen)
            ?: ResourcesUtils.getString(R.string.if_studio_paint)
        rightIcon = typedArray.getString(R.styleable.PaintEraserComponent_icon_right_pen)
            ?: ResourcesUtils.getString(R.string.if_studio_eraser)
        paintProgress = typedArray.getInteger(R.styleable.PaintEraserComponent_paint_Progress, 30)
        iconSize =
            typedArray.getDimension(R.styleable.PaintEraserComponent_icon_text_size, 38.dpf())

        if (typedArray.hasValue(R.styleable.PaintEraserComponent_component_bg)) {
            componentBg =
                typedArray.getColor(
                    R.styleable.PaintEraserComponent_component_bg,
                    ResourcesUtils.getColor(R.color.color_f5f5f5)
                )
        }
        if (typedArray.hasValue(R.styleable.PaintEraserComponent_progress_bg)) {
            progressBg =
                typedArray.getColor(R.styleable.PaintEraserComponent_progress_bg, -0x7f0d0d0e)
        }
        if (typedArray.hasValue(R.styleable.PaintEraserComponent_with_stroke_color)) {
            withStrokeColor =
                typedArray.getColor(
                    R.styleable.PaintEraserComponent_with_stroke_color,
                    ResourcesUtils.getColor(R.color.Gray_Dashline)
                )
        }
        paintPen.progress = paintProgress
        typedArray.recycle()
    }

    constructor(context: Context, attrs: AttributeSet? = null) : this(context, attrs, 0)
    constructor(context: Context) : this(context, null)

    /**
     * 动画属性。
     */
    private val animateIfvTranslate = DeviceUtils.dip2fpx(10f)
    private val animateBgTranslate = DeviceUtils.dip2fpx(48f)

    /**
     * 画笔VM。
     */
    lateinit var paintEraserViewModel: PaintEraserViewModel

    /**
     * 精细画笔。
     * note:这种penMode不要放在View中，最好放在processor中
     */
    var paintPen = PaintSelectComponent.PenMode(
        DeviceUtils.getScreenWidth() * 0.02f,
        DeviceUtils.getScreenWidth() * 0.08f,
        30
    )

    /**
     * 大画笔。
     * note:这种penMode不要放在View中，最好放在processor中
     */
    var eraserPen = PaintSelectComponent.PenMode(
        DeviceUtils.getScreenWidth() * 0.02f,
        DeviceUtils.getScreenWidth() * 0.08f,
        50
    )

    /**
     * 移动的时候隐藏其他icon。
     */
    var hideIconOnMove = false

    val viewBinding: ComponentPaintEraserBinding =
        ComponentPaintEraserBinding.inflate(LayoutInflater.from(context)).apply {
            executePendingBindings()
            addView(this.root)
        }

    override fun onInitOwner(storeOwner: ViewModelStoreOwner, lifecycleOwner: LifecycleOwner) {
        componentBg?.let { viewBinding.vBg.setShapeBgColor(it) }
        progressBg?.let { viewBinding.paintComponent.viewBinding.xsbPaint.thumbBackgroundColor = it }
        withStrokeColor?.let { viewBinding.vBg.setShapeStrokeColor(0.5f.dp(), it) }
        viewBinding.ifvPaint.text = leftIcon
        viewBinding.ifvEraser.text = rightIcon
        viewBinding.ifvPaint.setTextSize(TypedValue.COMPLEX_UNIT_PX, iconSize)
        viewBinding.ifvEraser.setTextSize(TypedValue.COMPLEX_UNIT_PX, iconSize)
        paintEraserViewModel = getViewModel(PaintEraserViewModel::class.java)
        ViewModelProvider(context as FragmentActivity)[ImageStudioViewModel::class.java].apply {
            debugBrushOkEvent.observe(lifecycleOwner) {
                if (it == true) {
                    BrushDebugConfig.penSize?.apply {
                        setPaintPen(50, this[0].dpf(), this[1].toFloat())
                        setEraserPen(50, this[0].dpf(), this[1].toFloat())
                        if (paintEraserViewModel.selectEraserEvent.value == true) {
                            selectEraser()
                        } else {
                            selectPaint()
                        }
                    }
                }
            }
        }
        viewBinding.ifvPaint.setOnClickListener {
            if (!viewBinding.ifvEraser.isVisible) {
                return@setOnClickListener
            }
            selectPaint()
        }
        viewBinding.ifvEraser.setOnClickListener {
            if (!viewBinding.ifvEraser.isVisible) {
                return@setOnClickListener
            }
            selectEraser()
        }

        // 需求。有的情况下在滑动滑杆时需要隐藏icon。
        viewBinding.paintComponent.viewBinding.xsbPaint.addOnProgressChangeListener(object :
            XSeekBar.OnProgressChangeListener {
            override fun onStartTracking(progress: Int, leftDx: Float) {
                if (hideIconOnMove) {
                    viewBinding.vBg.alpha = 0f
                    if (isSelectEraser()) {
                        viewBinding.ifvPaint.alpha = 0f
                    } else {
                        viewBinding.ifvEraser.alpha = 0f
                    }
                }
            }

            override fun onStopTracking(progress: Int, leftDx: Float, fromUser: Boolean) {
                if (fromUser && hideIconOnMove) {
                    viewBinding.vBg.alpha = 1f
                    if (isSelectEraser()) {
                        viewBinding.ifvPaint.alpha = 1f
                    } else {
                        viewBinding.ifvEraser.alpha = 1f
                    }
                }
            }
        })

        // 默认选中画笔。
        post {
            viewBinding.ifvPaint.translationX = animateIfvTranslate
            // 默认选中左边
            selectPaint(targetPenMode = defaultPenMode ?: paintPen)
        }
    }

    /**
     * 选中画笔。
     */
    fun selectPaint(targetPenMode: PaintSelectComponent.PenMode = paintPen) {
        if (isDarkTheme) {
            viewBinding.ifvPaint.setTextColor(R.color.Primary_A.resColor())
            viewBinding.ifvEraser.setTextColor(R.color.Gray_label_2.resColor())
        }
        paintEraserViewModel.selectEraserEvent.value = false
        viewBinding.paintComponent.usePaintMode(targetPenMode)
        viewBinding.paintComponent.showIndicatorThumb(false)
        viewBinding.root.animationTransition(duration = 200) {
            viewBinding.ifvPaint.translationX = animateIfvTranslate.toRtl
            viewBinding.ifvEraser.translationX = 0f.toRtl
            viewBinding.vBg.translationX = 0f.toRtl
        }
    }

    /**
     * 选中橡皮擦。
     */
    fun selectEraser() {
        if (isDarkTheme) {
            viewBinding.ifvEraser.setTextColor(R.color.Primary_A.resColor())
            viewBinding.ifvPaint.setTextColor(R.color.Gray_label_2.resColor())
        }
        paintEraserViewModel.selectEraserEvent.value = true
        viewBinding.paintComponent.usePaintMode(eraserPen)
        viewBinding.paintComponent.showIndicatorThumb(true)
        viewBinding.root.animationTransition(duration = 200) {
            viewBinding.ifvPaint.translationX = 0f.toRtl
            viewBinding.ifvEraser.translationX = -animateIfvTranslate.toRtl
            viewBinding.vBg.translationX = animateBgTranslate.toRtl
        }
    }

    fun isSelectEraser(): Boolean {
        return paintEraserViewModel.selectEraserEvent.value == true
    }

    override fun init(fragment: Fragment) {
        viewBinding.paintComponent.init(fragment)
        super.init(fragment)
    }

    override fun init(activity: FragmentActivity) {
        viewBinding.paintComponent.init(activity)
        super.init(activity)
    }

    /**
     * 隐藏橡皮擦。
     */
    fun disableEraser(disable: Boolean) {
        if (disable) {
            viewBinding.vBg.gone()
            viewBinding.ifvEraser.gone()
            viewBinding.ifvPaint.translationX = 0f
            viewBinding.paintComponent.setMarginCompat(
                start = DeviceUtils.dip2px(38f + 12f), // 38是按钮宽度、12是padding。
                end = 0
            )
        } else {
            viewBinding.vBg.visible()
            viewBinding.ifvEraser.visible()
            viewBinding.paintComponent.setMarginCompat(
                start = DeviceUtils.dip2px(38f + 12f),
                end = DeviceUtils.dip2px(38f + 12f)
            )
        }
    }

    fun setEraserPen(defaultProgress: Int, minSize: Float = 0f, maxSize: Float = 0f) {
        eraserPen.progress = defaultProgress
        if (maxSize != 0f) {
            eraserPen.maxSize = maxSize
        }
        if (minSize != 0f) {
            eraserPen.minSize = minSize
        }
    }

    fun setPaintPen(defaultProgress: Int, minSize: Float = 0f, maxSize: Float = 0f) {
        paintPen.progress = defaultProgress
        if (maxSize != 0f) {
            paintPen.maxSize = maxSize
        }
        if (minSize != 0f) {
            paintPen.minSize = minSize
        }
    }

    /**
     * 获取画笔大小
     */
    fun getPaintProgress(): Int {
        return paintPen.progress
    }

    /**
     * 设置当前画笔大小
     */
    fun setPaintProgress(progress: Int) {
        viewBinding.paintComponent.viewBinding.xsbPaint.setProgress(progress)
        viewBinding.paintComponent.currentUsePenMode.progress = progress
    }

    /**
     * 是否允许画笔和橡皮擦切换
     */
    fun enablePaintEraserSwitch(
        isEnable: Boolean,
        withAnim: Boolean = true,
        targetPenMode: PaintSelectComponent.PenMode, //目标画笔模式,
        isSelectPaint: Boolean = true,
        followAction: (() -> Unit) = {
            //如果在可切换的情况下 看是否附带行为 附带选中画笔或者橡皮擦
            if (isEnable) {
                if (isSelectPaint) {
                    viewBinding.ifvPaint.translationX = animateIfvTranslate.toRtl
                    viewBinding.ifvEraser.translationX = 0f.toRtl
                    viewBinding.vBg.translationX = 0f.toRtl
                } else {
                    viewBinding.ifvPaint.translationX = 0f.toRtl
                    viewBinding.ifvEraser.translationX = -animateIfvTranslate.toRtl
                    viewBinding.vBg.translationX = animateBgTranslate.toRtl
                }
            }
        }
    ) {
        val showIndicatorCenter: Boolean = !isSelectPaint
        viewBinding.paintComponent.showIndicatorThumb(showIndicatorCenter)
        val task = {
            if (isEnable) {
                viewBinding.ifvEraser.alpha = 1f
                viewBinding.ifvPaint.alpha = 1f
                viewBinding.vBg.alpha = 1f
            } else {
                viewBinding.ifvEraser.alpha = 0f
                viewBinding.ifvPaint.alpha = 0f
                viewBinding.vBg.alpha = 0f
            }
        }
        if (withAnim) {
            viewBinding.root.animationTransition {
                task.invoke()
                followAction.invoke()
                onTransitionStart = {
                    if (isEnable) {
                        viewBinding.ifvEraser.visible()
                        viewBinding.ifvPaint.visible()
                        viewBinding.vBg.visible()
                    }
                }
                onTransitionEnd = {
                    if (!isEnable) {
                        viewBinding.ifvEraser.gone()
                        viewBinding.ifvPaint.gone()
                        viewBinding.vBg.gone()
                    }
                }
            }
        } else {
            task.invoke()
            followAction.invoke()
            if (isEnable) {
                viewBinding.ifvEraser.visible()
                viewBinding.ifvPaint.visible()
                viewBinding.vBg.visible()
            } else {
                viewBinding.ifvEraser.gone()
                viewBinding.ifvPaint.gone()
                viewBinding.vBg.gone()
            }
        }
        paintEraserViewModel.selectEraserEvent.value = !isSelectPaint
        viewBinding.paintComponent.usePaintMode(targetPenMode)
    }

    /**
     * 当前选中的画笔模式
     * 外部使用的penMode
     */
    var defaultPenMode: PaintSelectComponent.PenMode? = null

    override fun onThemeChange(isDark: Boolean) {
        super.onThemeChange(isDark)
        viewBinding.paintComponent.isDarkTheme = isDark
        if (isDark) {
            viewBinding.vBg.background =
                GradientDrawableFactory.createDrawable(R.color.Gray_Background_2.resColor(), 51.dpf)
            viewBinding.ifvPaint.setTextColor(R.color.Gray_label_2.resColor())
            viewBinding.ifvEraser.setTextColor(R.color.Gray_label_2.resColor())
            if (isSelectEraser()) {
                viewBinding.ifvEraser.setTextColor(R.color.Primary_A.resColor())
            } else {
                viewBinding.ifvPaint.setTextColor(R.color.Primary_A.resColor())
            }
        } else {
            viewBinding.vBg.background =
                GradientDrawableFactory.createDrawable(R.color.color_f5f5f5.resColor(), 51.dpf)
            viewBinding.ifvPaint.setTextColor(R.color.Gray_A.resColor())
            viewBinding.ifvEraser.setTextColor(R.color.Gray_A.resColor())
        }
    }

}
