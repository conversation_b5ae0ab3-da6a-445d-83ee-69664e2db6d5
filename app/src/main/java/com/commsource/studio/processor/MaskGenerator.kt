package com.commsource.studio.processor

import android.graphics.Bitmap
import android.opengl.GLES20
import android.view.MotionEvent
import androidx.core.graphics.scale
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.commsource.beautyplus.fragment.BaseFragment
import com.commsource.easyeditor.utils.opengl.FBOEntity
import com.commsource.easyeditor.utils.opengl.GlThread
import com.commsource.easyeditor.utils.opengl.TextureHelper
import com.commsource.studio.BrushDebugConfig
import com.commsource.studio.ImageStudioViewModel
import com.commsource.studio.component.LayerScrollViewModel
import com.commsource.studio.layer.BaseScrollLayer
import com.commsource.studio.shader.ImageProgram

class MaskGenerator(val processor: BaseEffectProcessor?) {

    companion object {
        const val ONE_STEP = 1 // 单步
        const val TOTAL_STEP = 2 // 累加
        const val ONE_TOTAL = 3 // 累加 + 单步
    }

    var isEnable = true

    // 一次性步骤的。
    private var oneStepPaint = MTBrushPaint()
    private var oneStepMaskFbo: FBOEntity? = null

    // 全部步骤的
    private var overlayStepPaint = MTBrushPaint()
    private var overlayStepMaskFbo: FBOEntity? = null

    // 特殊反转笔刷。仅在橡皮擦模式使用。并且橡皮擦模式下是涂抹不是擦除。
    private var eraserDrawPaint = MTBrushPaint()
    private var eraserDrawMaskFbo: FBOEntity? = null

    // mask 生成模式
    private var maskGenerateMode = TOTAL_STEP

    // 笔刷仅仅点击生效
    var brushModeOnlySupportTap = false

    var isNeeIgnoreTapEvent = false

    // 当前画笔大小
    private var curPenSize = 50f

    // 是否橡皮擦模式
    private var isEraserMode = false

    // 橡皮擦功能反转
    var enableReverseEraserDrawPaint = false

    // 绘制图片的Program
    private val imageProgram = ImageProgram()

    // 开启之后橡皮模式的流量需要额外除一个系数。
    // 这样才可以实现橡皮擦透明度（多次擦除才会擦干净效果。）
    var isNeedEraserParams = false

    // 是否启用笔刷参数系数。
    // 因为流量大小问题。通过给笔刷Size 乘以
    // 一个系数，来让视觉大小和UI 的画笔大小保持一致
    var enableBrushSizeParams = false

    // 输入的宽 高。
    private var width: Int = 0
    private var height: Int = 0

    fun attachFragment(f: Fragment) {
        ViewModelProvider(f).get(LayerScrollViewModel::class.java).apply {
            addScrollListener(0, object : BaseScrollLayer.ScrollListener {
                private var hasScrollInViewPort = false
                private var isTapEvent = false
                override fun onSingleFingerDown(
                    viewPortX: Float,
                    viewPortY: Float,
                    motionEvent: MotionEvent
                ) {
                    if (isEnable) {
                        isTapEvent = false
                        hasScrollInViewPort = layer.inViewPort(viewPortX, viewPortY)
                        oneStepPaint.setPenSize(curPenSize / layer.containerScale)
                        oneStepPaint.setTouchViewScale(layer.containerScale * 2)
                        overlayStepPaint.setPenSize(curPenSize / layer.containerScale)
                        overlayStepPaint.setTouchViewScale(layer.containerScale * 2)
                        eraserDrawPaint.setPenSize(curPenSize / layer.containerScale)
                        eraserDrawPaint.setTouchViewScale(layer.containerScale * 2)
                        processTouchEvent(
                            1, viewPortX, viewPortY, layer.viewPortWidth, layer.viewPortHeight
                        )
                    }
                }

                override fun onStartSingleFingerScroll(
                    viewPortX: Float,
                    viewPortY: Float,
                    motionEvent: MotionEvent
                ) {
                    hasScrollInViewPort = layer.inViewPort(viewPortX, viewPortY)
                }

                override fun onTap(
                    viewPortX: Float,
                    viewPortY: Float,
                    motionEvent: MotionEvent
                ) {
                    if (isEnable) {
                        isTapEvent = true
                        if (!isNeeIgnoreTapEvent) {
                            processTouchEvent(
                                4, viewPortX, viewPortY, layer.viewPortWidth, layer.viewPortHeight
                            )
                        }
                    }
                }

                override fun onSingleFingerScroll(
                    viewPortX: Float,
                    viewPortY: Float,
                    motionEvent: MotionEvent
                ) {
                    if (isEnable) {
                        if (layer.inViewPort(viewPortX, viewPortY)) {
                            hasScrollInViewPort = true
                        }
                        processTouchEvent(
                            2, viewPortX, viewPortY, layer.viewPortWidth, layer.viewPortHeight
                        )
                    }
                }

                override fun onStopSingleFinger(
                    viewPortX: Float,
                    viewPortY: Float,
                    isStartScroll: Boolean,
                    isMajorFingerUp: Boolean,
                    motionEvent: MotionEvent?
                ) {
                    if (isEnable && hasScrollInViewPort && isMajorFingerUp) {
                        // 特殊逻辑，如果是仅仅支持点击的，涂抹放手的最后一点算点击。。。
                        if (brushModeOnlySupportTap && !isTapEvent) {
                            processTouchEvent(
                                4, viewPortX, viewPortY, layer.viewPortWidth, layer.viewPortHeight
                            )
                        } else {
                            processTouchEvent(
                                3, viewPortX, viewPortY, layer.viewPortWidth, layer.viewPortHeight
                            )
                        }
                    }
                }
            })
        }

        // 调试使用。
        ViewModelProvider((f as BaseFragment).ownerActivity)[ImageStudioViewModel::class.java].apply {
            debugBrushOkEvent.observe(f) {
                if (it == true) {
                    if (isNeedEraserParams) {
                        oneStepPaint.eraserParams = BrushDebugConfig.eraserParams
                        overlayStepPaint.eraserParams = BrushDebugConfig.eraserParams
                    }

                    if (enableBrushSizeParams) {
                        oneStepPaint.brushDrawSizeParams = BrushDebugConfig.brushDrawSizeParams / 10f
                        oneStepPaint.brushEraserSizeParams = BrushDebugConfig.brushEraserSizeParams / 10f

                        overlayStepPaint.brushDrawSizeParams = BrushDebugConfig.brushDrawSizeParams / 10f
                        overlayStepPaint.brushEraserSizeParams = BrushDebugConfig.brushEraserSizeParams / 10f
                    }

                    BrushDebugConfig.brushParams?.apply {
                        setParams(this[0] / 100f, this[1] / 100f, this[2] / 100f)
                    }
                }
            }
        }
    }


    @GlThread
    fun prepare(inputBitmap: Bitmap) {
        width = inputBitmap.width
        height = inputBitmap.height
        imageProgram.onGlResourceInit()
        // 单步的画笔
        if (maskGenerateMode and 1 == 1) {
            initialPaint(oneStepPaint, inputBitmap)
            oneStepMaskFbo = oneStepPaint.curResultFbo
            // 荣耀V8 要预热一下，不然第一次没效果。
            oneStepMaskFbo?.generateBitmap()?.recycle()
        }

        // 累加的画笔
        if (maskGenerateMode shr 1 == 1) {
            initialPaint(overlayStepPaint, inputBitmap)
            overlayStepMaskFbo = overlayStepPaint.curResultFbo
            // 荣耀V8 要预热一下，不然第一次没效果。
            overlayStepMaskFbo?.generateBitmap()?.recycle()
        }

        // 橡皮擦反转笔刷
        if (enableReverseEraserDrawPaint) {
            initialPaint(eraserDrawPaint, inputBitmap)
            eraserDrawMaskFbo = eraserDrawPaint.curResultFbo
            // 荣耀V8 要预热一下，不然第一次没效果。
            eraserDrawMaskFbo?.generateBitmap()?.recycle()
        }
    }


    private fun initialPaint(paint: MTBrushPaint, inputImage: Bitmap) {
        paint.isNeedEraserParams = isNeedEraserParams
        paint.isEnableBrushSizeParams = enableBrushSizeParams
        paint.onGLResourceInit()
        paint.setBrushColor(255, 255, 255)
        paint.setParams(1.0f, 1.0f, 0f)
        paint.setInput(inputImage)
    }

    @GlThread
    fun release() {
        imageProgram.onGlResourceRelease()
        oneStepPaint.onGLResourceRelease()
        overlayStepPaint.onGLResourceRelease()
        eraserDrawPaint.onGLResourceRelease()
        oneStepMaskFbo?.release()
        overlayStepMaskFbo?.release()
        eraserDrawMaskFbo?.release()
    }


    fun setBrushMode(isEraser: Boolean) {
        isEraserMode = isEraser
        val mode = if (isEraser) {
            MTBrushPaint.Mode.ERASER_MODE
        } else {
            MTBrushPaint.Mode.BRUSH_MODE
        }
        overlayStepPaint.curBrushMode = mode
        oneStepPaint.curBrushMode = mode
        // eraserDrawPaint 仅支持绘制

    }

    // 笔刷流量、硬度、边缘避让
    fun setParams(flow: Float, hardness: Float, edgeAvoidcance: Float) {
        if (maskGenerateMode shr 1 == 1) {
            overlayStepPaint.setParams(flow, hardness, edgeAvoidcance)
        }

        if (maskGenerateMode and 1 == 1) {
            oneStepPaint.setParams(flow, hardness, edgeAvoidcance)
        }

        if (enableReverseEraserDrawPaint) {
            eraserDrawPaint.setParams(flow, hardness, edgeAvoidcance)
        }
    }

    fun setHardness(hardness: Float) {
        if (maskGenerateMode shr 1 == 1) {
            overlayStepPaint.setHardness(hardness)
        }

        if (maskGenerateMode and 1 == 1) {
            oneStepPaint.setHardness(hardness)
        }

        if (enableReverseEraserDrawPaint) {
            eraserDrawPaint.setHardness(hardness)
        }
    }


    // 笔刷膨胀系数
    fun setSwellParams(brush: Float, eraser: Float) {
        if (maskGenerateMode shr 1 == 1) {
            overlayStepPaint.setBrushSwellParams(brush, eraser)
        }

        if (maskGenerateMode and 1 == 1) {
            oneStepPaint.setBrushSwellParams(brush, eraser)
        }

        if (enableReverseEraserDrawPaint) {
            eraserDrawPaint.setBrushSwellParams(brush, eraser)
        }
    }

    fun setBrushPenSize(penSize: Float) {
        curPenSize = penSize
    }

    fun setBrushGenerateMode(mode: Int) {
        maskGenerateMode = mode
    }

    fun fetchOverlayMaskFBO(): FBOEntity? {
        return overlayStepPaint.curResultFbo
    }

    fun fetchOneStepMaskFBO(): FBOEntity? {
        return oneStepPaint.curResultFbo
    }

    fun fetchEraserFBO(): FBOEntity? {
        return eraserDrawPaint.curResultFbo
    }


    fun fetchOneStepMaskBitmap(width: Int, height: Int): Bitmap? {
        return oneStepPaint.curResultFbo?.generateBitmap()?.scale(width, height)
    }

    fun fetchOverlayMaskBitmap(width: Int, height: Int): Bitmap? {
        return overlayStepPaint.curResultFbo?.generateBitmap()?.scale(width, height)
    }


    private fun processTouchEvent(type: Int, x: Float, y: Float, width: Float, height: Float) {
        if (!brushModeOnlySupportTap || isEraserMode || (brushModeOnlySupportTap && type == 4)) {
            processor?.requestRender(true, before = {
                if ((maskGenerateMode and 1) != 0) {
                    if (type == 1 || (brushModeOnlySupportTap && type == 4)) {
                        // Bugfix 部分机子首次调用Clear 无效。。 离谱
                        dispatchToBrush(oneStepPaint, 4, x, y, width, height)
                        // 一步画笔的在down 的时候清除
                        oneStepPaint.resetStatus()
                    }
                    dispatchToBrush(oneStepPaint, type, x, y, width, height)
                }

                if ((maskGenerateMode shr 1) != 0) {
                    dispatchToBrush(overlayStepPaint, type, x, y, width, height)
                }

                if (isEraserMode && enableReverseEraserDrawPaint) {
                    if (type == 1 || (brushModeOnlySupportTap && type == 4)) {
                        // Bugfix 部分机子首次调用Clear 无效。。 离谱
                        dispatchToBrush(eraserDrawPaint, 4, x, y, width, height)
                        // 一步画笔的在down 的时候清除
                        eraserDrawPaint.resetStatus()
                    }
                    dispatchToBrush(eraserDrawPaint, type, x, y, width, height)
                }
            })
        }
    }

    private fun dispatchToBrush(
        paint: MTBrushPaint, eventType: Int,
        x: Float, y: Float,
        width: Float, height: Float
    ) {
        when (eventType) {
            // down
            1 -> paint.touchesDown(x, y, width, height)
            // move
            2 -> paint.touchesMoved(x, y, width, height)
            // up
            3 -> paint.touchesUp(x, y, width, height)
            // tap
            4 -> {
                paint.touchesDown(x, y, width, height)
                paint.touchesMoved(x, y, width, height)
                paint.touchesUp(x, y, width, height)
            }
        }
    }


    /**
     * 设置笔刷的的底图。
     * 后续绘制都会基于地图进行绘制
     */
    @GlThread
    fun setMaskSource(sourceBitmap: Bitmap, isReverse: Boolean = false) {
        // 传进来的Bitmap 必须和初始化设置的输入图一致。
        var finalBitmap = sourceBitmap
        if (sourceBitmap.width != width || sourceBitmap.height != height) {
            finalBitmap = sourceBitmap.scale(width, height)
        }
        if (maskGenerateMode and 1 == 1) {
            if (isReverse) {
                oneStepPaint.reserveMask(finalBitmap)
            } else {
                oneStepPaint.blendWithAlphaMask(finalBitmap, false, false)
            }
        }
        if (maskGenerateMode shr 1 == 1) {
            if (isReverse) {
                overlayStepPaint.reserveMask(finalBitmap)
            } else {
                overlayStepPaint.blendWithAlphaMask(finalBitmap, false, false)
            }
        }
    }

    @GlThread
    fun clearMaskFBO() {
        oneStepMaskFbo?.clear()
        overlayStepMaskFbo?.clear()
    }


    fun copyImageToMaskFbo(sourceBitmap: Bitmap, maskFbo: FBOEntity) {
        val fbo = TextureHelper.createFBOWithPreFix(sourceBitmap)
        GLES20.glEnable(GLES20.GL_BLEND)
        GLES20.glBlendFunc(GLES20.GL_SRC_ALPHA, GLES20.GL_ONE_MINUS_SRC_ALPHA)
        GLES20.glBlendEquation(GLES20.GL_FUNC_ADD)
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, maskFbo.fboId)
        GLES20.glViewport(0, 0, maskFbo.width, maskFbo.height)
        imageProgram.drawTextureInFbo(fbo)
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, 0)
        fbo.release()
        GLES20.glDisable(GLES20.GL_BLEND)
        // 用这种方式会导致笔刷库画不出来。。
        // TextureHelper.copyImgToFbo(sourceBitmap,maskFbo)
    }

}