package com.commsource.studio.processor

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.text.TextUtils
import android.util.Base64
import androidx.annotation.WorkerThread
import androidx.lifecycle.MutableLiveData
import com.commsource.aiengine.utils.AiEngineSegmentUtils
import com.commsource.aiengine.utils.AiEngineSegmentUtils.removeBlack
import com.commsource.aiengine.utils.AiEngineSegmentUtils.toBitmap
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.beautyplus.BuildConfig
import com.commsource.beautyplus.util.PathUtil
import com.commsource.camera.newrender.recognize.BodyMaskData
import com.commsource.easyeditor.utils.opengl.FBOEntity
import com.commsource.easyeditor.utils.opengl.TextureHelper
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.studio.AIGCConstant
import com.commsource.studio.component.SeekComponent
import com.commsource.util.ExternalStorageUtils
import com.commsource.util.ThreadExecutor
import com.commsource.util.V
import com.commsource.util.common.BitmapUtil
import com.commsource.util.print
import com.commsource.util.processSize
import com.commsource.util.safeGet
import com.meitu.common.AppContext
import com.meitu.core.processor.MteBaseEffectUtil
import com.meitu.core.types.NativeBitmap
import com.meitu.filterglextension.PixAutoCutoutEdgeTool
import com.meitu.http.api.OnlineProcessApi
import com.meitu.http.api.OpenLabApi
import com.meitu.http.api.PixAESCrypt
import com.meitu.http.kotex.api
import com.meitu.http.kotex.response
import com.meitu.library.util.app.ResourcesUtils
import com.meitu.library.util.bitmap.BitmapUtils
import com.meitu.library.util.io.FileUtils
import com.meitu.library.util.io.StreamUtils
import com.meitu.template.bean.EffectValue
import com.meitu.template.bean.StyleMaterial
import com.pixocial.pixrendercore.node.PEContext
import com.pixocial.pixrendercore.node.PEFrame
import com.pixocial.pixrendercore.params.PEMixProgressParams
import com.pixocial.pixrendercore.params.PEPresetParams
import com.pixocial.pixrendercore.tools.PEImageConvertUtils
import java.io.File
import java.net.URL
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import kotlin.collections.set

/**
 * 风格化渲染器
 * 之前存在多线程渲染可能存在判断path exist问题 添加[sessionId]
 *
 * 油画 PEFrame单独做了MixProgressParams
 *
 *
 */
class StyleProcessor : PaintEffectProcessor() {

    companion object {
        /**
         * 整体添加清理缓存保护
         */
        fun clearStyleCache() {
            ThreadExecutor.executeSlowTask("safeDeleteStyleCacheFiles") {
                val dirName =
                    PathUtil.getExternalFileDir(AppContext.context, "style_cache_dir")
                File(dirName).listFiles()?.forEach {
                    it.deleteOnExit()
                }
            }
        }

        /**
         * 图片进行混合模式
         */
        fun processImageAfterRequest(srcBitmap: Bitmap, dstBitmap: Bitmap): Bitmap {
            val resultBitmap =
                Bitmap.createBitmap(dstBitmap.width, dstBitmap.height, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(resultBitmap)
            val paint = Paint()
            canvas.drawBitmap(dstBitmap, 0f, 0f, null)
            paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_IN)
            canvas.drawBitmap(
                srcBitmap,
                Rect(0, 0, srcBitmap.width, srcBitmap.height),
                Rect(0, 0, dstBitmap.width, dstBitmap.height),
                paint
            )
            dstBitmap.recycle()
            return resultBitmap
        }
    }

    /**
     * 多线程sessionId
     */
    val sessionId: String = UUID.randomUUID().toString()

    var isRelease = false

    val peContext by lazy { PEContext() }

    /**
     * 渲染pipeline
     */
    private val renderFrame by lazy { PEFrame(peContext) }

    /**
     * 最大效果混合 frame 专供油画效果提供和原图混合输出的场景
     */
    private val mixFrame by lazy { PEFrame(peContext) }

    /**
     * PE引擎预设参数 主要就是渲染处理风格化preset.json 文件
     */
    val presetParams by lazy { PEPresetParams() }

    /**
     * 最大效果混合参数
     */
    val mixProgressParam by lazy { PEMixProgressParams() }

    /**
     * 显示loading事件
     */
    val loadingEvent by lazy { MutableLiveData<Boolean>() }

    /**
     * 滑杆UI状态事件处理
     */
    val seekUiStateEvent by lazy { MutableLiveData<List<SeekComponent.SeekWrapper>?>() }

    /**
     * 画笔UI状态 默认不展示
     */
    val paintUiStateEvent by lazy { MutableLiveData<Boolean>(false) }

    /**
     * 手势图层是否可用状态
     */
    val prismUIStateEvent by lazy { MutableLiveData<Boolean>(false) }

    /**
     * 关闭分散交互手势
     */
    val dispersionUIStateEvent by lazy { MutableLiveData<Boolean>(false) }
    val maskUIStateEvent by lazy { MutableLiveData<Boolean>(false) }//bugfix

    @Volatile
    private var startRequestTime = System.currentTimeMillis()

    init {
        scrollStateFun3 = { isStart: Boolean, isEraser: Boolean, hasScrollIntoArea: Boolean ->
            if (isApplyDisperseMaterial()) {
                if (!isEraser && hasScrollIntoArea && !isStart) {
                    handMaskApplyStateUIStateEvent.postValue(true)
                }
            }
        }
    }

    /**
     * 轮询图片效果次数
     */
    private var queryImageCount = 0

    override var enableBrushPaint = true
    override var showMask = false
    override var maskGenMode = MaskGenerator.TOTAL_STEP

    // 部分耗时风格实体效果缓存池
    // key:风格化ID value:风格化最大效果保存的本地路径
    private var styleEffectCacheMap = ConcurrentHashMap<String, String>()

    //aigc效果缓存在线对应map
    //key:风格化ID value:aigc在线msgId
    private var aigcOnlineEffectIDCacheMap = ConcurrentHashMap<String, String?>()

    var bodyEdgeMask: Bitmap? = null

    /**
     * 是否需要闪动一次mask
     */
    var needBrieflyMask: Boolean = true

    /**
     * 手动mask状态
     */
    var handMaskUIStateEvent = MutableLiveData<Boolean>(false)

    /**
     * 手动mask是否可点击确认
     */
    var handMaskApplyStateUIStateEvent = MutableLiveData<Boolean>(false)

    /**
     * 重置上一次选中素材
     */
    var resetApplyMaterial = NoStickLiveData<StyleMaterial?>()

    /**
     * 选择手动mask模式
     */
    fun switchHandMaskUIState(
        isHandMaskMode: Boolean,
        refreshMask: Boolean = false,
        enableChangeUiState: Boolean = true,//一个特殊交互 在点击退出的时候 不要做切换动画了 直接confirm
        afterRender: (() -> Unit)? = null
    ) {
        requestRender(force = true,
            before = {
                renderOrigin = isHandMaskMode
                showMask = isHandMaskMode
                isEnable = isHandMaskMode
                if (refreshMask) {
                    //如果是分散效果,配置上mask
                    if (isDisperseStyle(currentApplyStyleMaterial)) {
                        //拿出mask 给到引擎
                        val mask = getCurImageMasker().maskBitmap ?: bodyEdgeMask
                        mask.takeIf { BitmapUtils.isAvailableBitmap(it) }?.let {
                            presetParams.setImage(
                                "FilterMask",
                                PEImageConvertUtils.peBaseImageFromBitmap(it, false, 0, 1)!!,
                                true
                            )
                        }
                        presetParams.updateEffect()
                    }
                }
            }, after = {
                if (enableChangeUiState) {
                    handMaskUIStateEvent.postValue(isHandMaskMode)
                    dispersionUIStateEvent.postValue(!isHandMaskMode)
                }
                afterRender?.invoke()
            })
    }

    // 是否可以使用自动分散
    // 是否含有人像
    // null 状态判断首次处理分散mask抠边缘
    private var isCanAutoDispersion: Boolean? = null

    /**
     * 特殊风格化 棱镜效果记忆参数
     */
    var prismFilterZoom = 1.0f
    var prismFilterRotation = 0
    var prismCenterPoint = floatArrayOf(0.5f, 0.5f)
    private fun resetPrismPresetParams() {//棱镜切换重置默认点位
        prismFilterZoom = 1.0f
        prismFilterRotation = 0
        prismCenterPoint = floatArrayOf(0.5f, 0.5f)
    }

    /**
     * 分散中心点位置
     */
    var disperseSize: Int = 50
    var disperseDensity: Int = 50
    var disperseCenterPoint = floatArrayOf(0.82f, 0.5f)//分散效果记忆参数

    private fun isPrismStyle(style: StyleMaterial?): Boolean {
        return style?.oldId == "1STY00000021" || style?.oldId == "1STY00000049"
    }

    /**
     * 是否是分散效果
     */
    private fun isDisperseStyle(style: StyleMaterial?): Boolean {
        return style?.effectValue?.type == EffectValue.EffectDisperse
    }

    /**
     * 是否是在线最大效果的风格化效果
     */
    private fun isOnlineMaxEffectStyle(style: StyleMaterial?): Boolean {
        return style?.effectValue?.type == EffectValue.EffectOnline || style?.effectValue?.type == EffectValue.EffectAIGC
    }

    /**
     * 当前套用的风格化素材
     */
    var currentApplyStyleMaterial: StyleMaterial? = null
    fun isApplyDisperseMaterial(): Boolean {
        return isDisperseStyle(currentApplyStyleMaterial)
    }

    /**
     * 风格化渲染流程定义的渲染原图
     * [onRender] 仅拦截当前onRender的输出情况
     */
    var renderOrigin = false

    /**
     * 处理风格化效果
     * @param needTipsDisperseMask 是否需要提示分散mask
     * @param beforeRender 渲染前GL
     * @param afterDisperseBodyMask 渲染分散效果后 身边边缘bitmap闪动效果
     * @param onlineProcessError 在线处理异常
     * @param afterRender 渲染后GL 目前设计成单次风格化效果渲染能处理好（包括在线，这里设计成这样很重要 因为在配方中的风格化渲染固化，需要这么设计这个结构）
     * @param forceRenderCurrentEffect 是否单次套用素材强制渲染效果图
     */
    fun applyStyleMaterial(
        styleMaterial: StyleMaterial?,
        needTipsDisperseMask: Boolean = needBrieflyMask,
        afterDisperseBodyMask: ((bodyMask: Bitmap?) -> Unit) = {},
        beforeRender: (() -> Unit)? = null,
        onlineProcessError: (() -> Unit)? = null,
        forceRenderCurrentEffect: Boolean = handMaskUIStateEvent.value != true,
        afterRender: ((frame: PEFrame?) -> Unit)? = null,
    ) {

        cancel = false
        startRequestTime = System.currentTimeMillis()

        if (styleMaterial?.effectValue?.type == EffectValue.EffectOilPainting
            || styleMaterial?.onlineId == "BP_STY_00000037"
            || styleMaterial?.onlineId == "BP_STY_00000033"
            || styleMaterial?.onlineId == "BP_STY_00000049"
        ) {
            // 兼容下，避免闪退
            return
        }


        //真实内部渲染方法
        fun innerRender() {
            if (hasRelease) {
                return
            }

            val needBuildSeekWrapper = currentApplyStyleMaterial != styleMaterial
            currentApplyStyleMaterial = styleMaterial
            requestRender(force = true, before = {
                if (hasRelease) {
                    return@requestRender
                }
                beforeRender?.invoke()
                val material = currentApplyStyleMaterial
                if (material != null) {
                    //特殊棱镜素材 支持手势输入
//                    if (isPrismStyle(material)) {
//                        // 重置单次棱镜参数
//                        resetPrismPresetParams()
//                    }//用例说又不重置了
                    //特殊分散效果 首次检测
                    if (isDisperseStyle(material)) {
                        //设置共同记忆的preset值
                        material.presetValue?.forEach {
                            when (it.key) {
                                "size" -> it.useDegree = disperseSize
                                "density" -> it.useDegree = disperseDensity
                            }
                        }
                        loadingEvent.postValue(true)
                        //同步处理身体检测数据
                        processBodyMaskData()
                        //处理身体边缘mask
                        val isFirstProcess = processBodyMaskEdge()
                        //提示一次交互mask闪动
                        if (needTipsDisperseMask && isCanAutoDispersion == true) {
                            needBrieflyMask = false
                            afterDisperseBodyMask.invoke(bodyEdgeMask)
                            renderOrigin = true//标记当前输出原图
                        } else {
                            //如果在连续套用分散效果下 是否显示原图 需要判定是否在手动下 手动下显示原图
                            renderOrigin = !forceRenderCurrentEffect
                            if (isFirstProcess) {
                                dispersionUIStateEvent.postValue(isCanAutoDispersion == true)
                            } else {
                                switchHandMaskUIState(isHandMaskMode = handMaskUIStateEvent.value == true)
                            }
                        }
                    } else {
                        showMask = false
                        isEnable = false
                        renderOrigin = false
                        dispersionUIStateEvent.postValue(false)
                        maskUIStateEvent.postValue(false)
                    }
                    //需要缓存的图片
                    if (needCacheMaxEffect(material)) {
                        renderOrigin = false
                        //目标缓存图
                        val cachePath = getCachePath(material)
                        checkCacheMaxEffectImage(material)
                        //设置给混合Frame
                        val bitmap = ExternalStorageUtils.loadImage(cachePath)
                        mixProgressParam.setMaxEffectImage(PEImageConvertUtils.peBaseImageFromBitmap(bitmap, false)!!, true)
                        mixProgressParam.updateEffect()
                    } else {
                        //首次套用效果 都处理loading
                        loadingEvent.postValue(true)
                        presetParams.configPath = material.getConfigPath()
                        presetParams.parse()
                        presetParams.updateEffect()
                        //如果是分散效果,配置上mask
                        if (isDisperseStyle(material)) {
                            //拿出mask 给到引擎
                            val mask = getCurImageMasker().maskBitmap ?: bodyEdgeMask
                            mask.takeIf { BitmapUtils.isAvailableBitmap(it) }?.let {
                                presetParams.setImage(
                                    "FilterMask",
                                    PEImageConvertUtils.peBaseImageFromBitmap(it, false, 0, 1)!!,
                                    true
                                )
                            }
                            presetParams.updateEffect()
                        }
                        if (isOnlineMaxEffectStyle(material) && hasCache(material)) {
                            val cachePath = getCachePath(material)
                            val bitmap = ExternalStorageUtils.loadImage(cachePath)
                            bitmap.takeIf { BitmapUtils.isAvailableBitmap(it) }?.let {
                                presetParams.setImage(
                                    "MaxEffectImage",
                                    PEImageConvertUtils.peBaseImageFromBitmap(it, true)!!,
                                    true
                                )
                            }
                            presetParams.updateEffect()
                        }
                    }
                } else {
                    renderOrigin = true
                    showMask = false
                    isEnable = false
                    dispersionUIStateEvent.postValue(false)
                    presetParams.configPath = ""
                    presetParams.parse()
                    presetParams.updateEffect()
                }
            }, after = {
                if (hasRelease) {
                    return@requestRender
                }
                val material = currentApplyStyleMaterial
                //构建滑杆状态
                if (needBuildSeekWrapper) {
                    buildSeekWrappers(material)
                }
                //特殊的素材状态切换 （分散）
                paintUiStateEvent.postValue(material?.effectValue?.type == EffectValue.EffectDisperse)
                //特殊棱镜素材 支持手势输入
                prismUIStateEvent.postValue(isPrismStyle(material))
                //渲染结束以后调用
                material?.let {
                    if (needCacheMaxEffect(material)) {
                        afterRender?.invoke(mixFrame)
                    } else {
                        afterRender?.invoke(renderFrame)
                    }
                }
            })
        }
        //特殊在线渲染发起
        styleMaterial?.effectValue?.type?.takeIf {
            (it == EffectValue.EffectAIGC || it == EffectValue.EffectOnline)
                    && !hasCache(styleMaterial) //如果已经有缓存了在线效果 直接走正常风格化套用
        }?.let {
            requestRender(force = true) {
                //生成原图Bitmap
                val inputBitmap = srcFBOEntity.generateBitmap()
                onlineProcess(inputBitmap = inputBitmap,
                    style = styleMaterial,
                    effectValue = styleMaterial.effectValue!!,
                    success = {
                        if (isRelease) {//online async,to be safe
                            return@onlineProcess
                        }


                        require(hasCache(styleMaterial)) { "在线处理风格化流程到这,发现不存在缓存,必须要有在线缓存" }
                        innerRender()
                    },
                    fail = {
                        if (isRelease) {//online async,to be safe
                            return@onlineProcess
                        }

                        resetApplyMaterial.postValue(currentApplyStyleMaterial)
                        loadingEvent.postValue(false)
                        afterRender?.invoke(null)
                        onlineProcessError?.invoke()
                        it.print("csx")
                    })
            }
            return
        }
        //常规本地渲染效果 直接发起
        innerRender()
    }

    /**
     * 处理在线效果 整体包装掉在线的处理 明确对外暴露最大效果图[cacheEffectToDisk]
     *
     * @param inputBitmap 输入原图
     * @param effectValue 在线下发的效果配置
     * @param success 成功回调 直接对接渲染业务
     * @param fail 失败回调 直接对接渲染业务
     */
    @WorkerThread
    private fun onlineProcess(
        inputBitmap: Bitmap,
        style: StyleMaterial,
        effectValue: EffectValue,
        success: (() -> Unit)? = null,
        fail: ((debugMsg: String) -> Unit)? = null,
    ) {
        /**
         * 生成在线输出的最大效果Bitmap并保存
         */
        ">>>风格化输入图是否需要压缩>>>${style.customMateData?.needCompress}".print("StyleProcessor")
        val srcBitmap = if (style.customMateData?.needCompress != false) {
            BitmapUtils.scaleBitmap(inputBitmap, 1080, 1080)
        } else {
            inputBitmap
        }
        if (!BitmapUtils.isAvailableBitmap(srcBitmap)) {
            fail?.invoke("生成srcBitmap失败")
            return
        }
        val oriBitmap = srcBitmap.copy(Bitmap.Config.ARGB_8888, false)
        fun generateOnlineEffectBitmapAndCache(labResponse: OpenLabApi.LabResponse) {
            if (0 == labResponse.error_code) {
                //在线结果解析
                labResponse.media_info_list.safeGet(0)?.let {
                    when (it.media_profiles.media_data_type) {
                        "url" -> {
                            val url = it.media_data
                            val labMaskStream = URL(url).openStream()
                            val labMaskBitmap = BitmapFactory.decodeStream(labMaskStream)
                            StreamUtils.close(labMaskStream)
                            val result = processImageAfterRequest(oriBitmap, labMaskBitmap)
                            if (BitmapUtils.isAvailableBitmap(result)) {
                                cacheEffectToDisk(bitmap = result, style = style)
                                success?.invoke()
                            } else {
                                fail?.invoke("获取url生成bitmap不合法")
                            }
                        }

                        else -> {
                            //非标记url 目前在这里均当作base64解析处理
                            val bitmapArray =
                                Base64.decode(it.media_data, Base64.NO_WRAP)
                            val labMaskBitmap = BitmapFactory.decodeByteArray(
                                bitmapArray, 0, bitmapArray.size
                            )
                            val result = processImageAfterRequest(oriBitmap, labMaskBitmap)
                            if (BitmapUtils.isAvailableBitmap(result)) {
                                cacheEffectToDisk(bitmap = result, style = style)
                                success?.invoke()
                            } else {
                                fail?.invoke("获取base64生成bitmap不合法")
                            }
                        }
                    }
                }
                return
            }
            //前置流程理论不能走到这
        }

        /**
         * 特殊AIGC内部轮询
         */
        fun loopQueryImageOnAIGC() {
            val msgId = aigcOnlineEffectIDCacheMap[style.getMixId()]
            require(!TextUtils.isEmpty(msgId)) { "轮询AIGC图片发现MsgId不存在" }
            //没有缓存 那么需要轮询拉取目标msg图片
            //设置单次轮询结果图20次 20次之后 直接走fail
            queryImageCount = 0
            fun loopQueryImage() {
                queryImageCount++
                OnlineProcessApi::class.java.api()
                    .queryImage(msgId!!)
                    .responseOnBackground()
                    .response {
                        onNext = block@{
                            //aigc图片查询到结果
                            when (it.error_code) {
                                0 -> {
                                    generateOnlineEffectBitmapAndCache(it)
                                    MTAnalyticsAgent.logEvent(
                                        "style_query_count", "count", queryImageCount.toString()
                                    )
                                }

                                29901 -> {
                                    if (isTimeout(style)) {
                                        fail?.invoke("轮询图片超时主动取消了")
                                        return@block
                                    }

                                    if (!cancel) {
                                        "轮询结果".V()
                                        Thread.sleep(3 * 1000L)//稍等个100ms
                                        loopQueryImage()
                                    }
                                }

                                else -> {
                                    aigcOnlineEffectIDCacheMap.remove(style.getMixId())
                                    fail?.invoke("轮询图片返回结果errorCode为${it.error_code},msg:${it.error_msg}")
                                }
                            }
                        }

                        onError = {
                            aigcOnlineEffectIDCacheMap.remove(style.getMixId())
                            fail?.invoke("轮询图片网络请求失败")
                        }
                    }
            }
            loopQueryImage()
        }
        ThreadExecutor.executeSlowTask("styleOnlineProcess") {
            loadingEvent.postValue(true)
            when (effectValue.type) {
                EffectValue.EffectOnline -> {
                    //在线效果
                    effectValue.takeIf { !TextUtils.isEmpty(it.apiKey) && !TextUtils.isEmpty(it.apiSecret) }
                        ?.let {
                            OnlineProcessApi::class.java.api()
                                .styleOnlineProcess(
                                    srcBitmap = srcBitmap,
                                    online = PixAESCrypt.decryptOffset(effectValue.online),
                                    apiKey = PixAESCrypt.decryptOffset(effectValue.apiKey),
                                    apiSecret = PixAESCrypt.decryptOffset(effectValue.apiSecret)
                                ).responseOnBackground()
                                .synRequest()
                                .response {
                                    onNext = {
                                        if (it.error_code == 0) {

                                            generateOnlineEffectBitmapAndCache(it)
                                        } else {

                                            fail?.invoke("在线效果返回结果errorCode是非0")
                                        }
                                    }

                                    onError = {
                                        //常规请求都失败

                                        fail?.invoke("在线效果网络请求失败")
                                    }
                                }
                        }
                }

                EffectValue.EffectAIGC -> {
                    if (aigcOnlineEffectIDCacheMap[style.getMixId()] != null) {
                        //已经存在在线的结果图ID
                        //直接进入拉取图片逻辑
                        loopQueryImageOnAIGC()
                    } else {
                        effectValue.takeIf { !TextUtils.isEmpty(it.styleId) && !TextUtils.isEmpty(it.effectAddress) }
                            ?.let {
                                OnlineProcessApi::class.java.api()
                                    .aigcOnlineProcess(
                                        srcBitmap = srcBitmap,
                                        styleId = PixAESCrypt.decryptOffset(effectValue.styleId),
                                        effectAddress = PixAESCrypt.decryptOffset(effectValue.effectAddress)
                                    ).responseOnBackground()
                                    .synRequest()
                                    .response {
                                        onNext = block@{
                                            if (isTimeout(style)) {
                                                fail?.invoke("aigc接口请求超时，主动取消")
                                                return@block
                                            }

                                            if (!TextUtils.isEmpty(it.msgId)) {
                                                MTAnalyticsAgent.logEvent(
                                                    MTAnalyticsConstant.ai_filter_task_creation,
                                                    HashMap<String, String>(4).apply {
                                                        this["material_id"] = style.onlineId
                                                        this["source"] = AIGCConstant.ai_filter_task_creation_source
                                                        this["is_suc"] = "是"
                                                    })
                                                //保存这个字段
                                                aigcOnlineEffectIDCacheMap[style.getMixId()] =
                                                    it.msgId
                                                loopQueryImageOnAIGC()
                                            }
                                        }

                                        onError = {
                                            //常规请求都失败
                                            MTAnalyticsAgent.logEvent(
                                                MTAnalyticsConstant.ai_filter_task_creation,
                                                HashMap<String, String>(4).apply {
                                                    this["material_id"] = style.onlineId
                                                    this["source"] = AIGCConstant.ai_filter_task_creation_source
                                                    this["is_suc"] = "否"
                                                })
                                            fail?.invoke("aigc接口请求失败")
                                        }
                                    }
                            }
                    }
                }
            }
        }
    }

    private fun isTimeout(style: StyleMaterial): Boolean {
        var waitTime = 120
        if ((style.customMateData?.waiting_time ?: 0) > 0) {
            try {
                waitTime = style.customMateData!!.waiting_time!!
            } catch (ignore: Exception) {
                ignore.printStackTrace()
            }
        }
        if (waitTime > 0) {
            val delta = System.currentTimeMillis() - startRequestTime
            if (delta >= waitTime * 1000L) {
                return true
            }
        }
        return false
    }

    /**
     * 这种是油画的单次渲染慢 需要保存到磁盘
     */
    private fun checkCacheMaxEffectImage(material: StyleMaterial) {
        if (!hasCache(material)) {
            loadingEvent.postValue(true)
            //不存在缓存
            //渲染缓存一次
            presetParams.configPath = material.getConfigPath()
            presetParams.parse()
            presetParams.updateEffect()
            renderFrame.process()
            //缓存效果到磁盘
            val maxEffectBitmap = PEImageConvertUtils.bitmapFromPEBaseImage(renderFrame.getImage().readBaseImage(), true)!!
            cacheEffectToDisk(maxEffectBitmap, material)
        }
    }

    /**
     * 构建滑杆UI状态
     */
    private fun buildSeekWrappers(style: StyleMaterial?) {
        if (style == null) {
            seekUiStateEvent.postValue(null)
        } else {
            seekUiStateEvent.postValue(style.presetValue?.filter { it.type == 0 }?.map {
                val presetValue = it
                SeekComponent.SeekWrapper().apply {
                    //这里强制读取多语言翻译资源 如果没有 直接用name
                    val name = try {
                        ResourcesUtils.getString(
                            ResourcesUtils.getIdentifier(
                                it.name,
                                "string", BuildConfig.APPLICATION_ID
                            )
                        )
                    } catch (t: Throwable) {
                        it.name
                    }
                    this.name = name
                    this.minProgress = it.min
                    this.maxProgress = it.max
                    this.progress = it.useDegree

                    onStartTracking = {

                    }

                    onStopTracking = {
                        if (isApplyDisperseMaterial()) {
                            when (presetValue.key) {
                                "size" -> disperseSize = it
                                "density" -> disperseDensity = it
                            }
                        }
                        presetValue.useDegree = it
                        requestRender(force = true)
                    }

                    onProgressChange = { progress, fromUser ->
                        if (fromUser) {
                            if (isApplyDisperseMaterial()) {
                                when (presetValue.key) {
                                    "size" -> disperseSize = progress
                                    "density" -> disperseDensity = progress
                                }
                            }
                            presetValue.useDegree = progress
                            requestRender(force = false)
                        }
                    }
                }
            })
        }
    }

    override fun onGlResourceInit() {
        super.onGlResourceInit()
        peContext.init(AppContext.application)
        /// 需要设置最大值且跟上层最大值要一致，不然人脸检测会有问题
        peContext.detectOptions.aiEngineDetectOption.faceCropCount = 10
        /// 未来不应该直接动，人脸库6.2删除，应该直接使用上面的裁剪人脸数量的属性，有问题请联系 <EMAIL>
        peContext.detectOptions.aiEngineDetectOption.maxDetectFaceCount = 10
        peContext.detectOptions.aiEngineDetectOption.enableGenMesh = true
        presetParams.arPublicConfigPath = "armaterial/ARKernelPublicParamConfiguration.plist"
        renderFrame.setSingleParams(presetParams)
        presetParams.postProcessMode = 101
        presetParams.updateEffect()
        renderFrame.setupWithPEBaseImage(
            PEImageConvertUtils.peBaseImageFromBitmap(srcFBOEntity.generateBitmap(), true)!!,
            true
        ) //初始化原图

        mixFrame.setSingleParams(mixProgressParam)
        mixFrame.setupWithPEBaseImage(
            PEImageConvertUtils.peBaseImageFromBitmap(srcFBOEntity.generateBitmap(), true)!!,
            true
        ) //初始化原图
    }

    override fun onGlResourceRelease() {
        super.onGlResourceRelease()
        if (!isRelease) {
            isRelease = true
            presetParams.release()
            mixProgressParam.release()
            renderFrame.release()
            mixFrame.release()
            peContext.release()
            clearCache()
        }
    }

    override fun onRender(disFBO: FBOEntity) {
        //标记渲染输出原图
        if (renderOrigin || currentApplyStyleMaterial == null) {
            textureCopyProgram.copyTexture(srcFBOEntity, disFBOEntity)
            loadingEvent.postValue(false)
            return
        }
        if (isRelease || cancel) {
            return
        }
        currentApplyStyleMaterial?.let {
            if (needCacheMaxEffect(it)) {
                //主要是油画效果 需要Cache处理
                it.presetValue?.forEach {
                    mixProgressParam.progress =
                        it.useDegree.toFloat() / it.max.toFloat().coerceAtLeast(1f)
                }
                mixProgressParam.updateEffect()
                //如果是需要缓存最大效果的 直接用混合Frame处理后作为输出
                mixFrame.process()
                textureCopyProgram.copyTexture(mixFrame.getImage().textureId, disFBO)
            } else {
                //常规preset
                it.presetValue?.forEach {
                    presetParams.setSliderValue(
                        it.key,
                        it.useDegree.toFloat() / (it.max.toFloat().coerceAtLeast(1f))
                    )
                }
                //棱镜效果
                if (isPrismStyle(it)) {
                    presetParams.setSliderValue("TouchPointX", prismCenterPoint[0])
                    presetParams.setSliderValue("TouchPointY", prismCenterPoint[1])
                    val targetValue = prismFilterRotation / 360f * 3f
                    presetParams.setSliderValue("Rotate", targetValue)
                    presetParams.setSliderValue("Scale", prismFilterZoom)
                }
                //分散素材
                if (isDisperseStyle(it)) {
                    presetParams.setSliderValue("TouchPointX", disperseCenterPoint[0])
                    presetParams.setSliderValue("TouchPointY", disperseCenterPoint[1])
                    presetParams.setSliderValue("Rotate", 0f)
                    presetParams.setSliderValue("Scale", 1.0f)
                }
                presetParams.updateEffect()
                renderFrame.process()
                textureCopyProgram.copyTexture(renderFrame.getImage().textureId, disFBO)
            }
        }
        loadingEvent.postValue(false)
    }

    /**
     * 缓存效果图到风格化缓存空间
     */
    @Synchronized
    private fun cacheEffectToDisk(bitmap: Bitmap, style: StyleMaterial) {
        // 缓存到磁盘
        style.getMixId().let { id ->
            val cachePath = getCachePath(style)
            BitmapUtil.saveImageToDisk(bitmap, cachePath)
            styleEffectCacheMap.put(id, cachePath)
        }
    }

    /**
     * 获取缓存路径
     */
    private fun getCachePath(style: StyleMaterial): String {
        val dir = getCachePath()
        FileUtils.createDir(dir)
        return "${dir}${File.separator}${style.getMixId()}.png"
    }


    /**
     * 油画效果 需要缓存
     */
    private fun needCacheMaxEffect(style: StyleMaterial): Boolean {
        return style.effectValue?.type == EffectValue.EffectOilPainting
    }

    /**
     * 是否存在缓存
     */
    private fun hasCache(style: StyleMaterial): Boolean {
        val path = styleEffectCacheMap[style.getMixId()]
        return !TextUtils.isEmpty(path) && FileUtils.isFileExist(path)
    }

    /**
     * 获取风格化缓存空间
     */
    private fun getCachePath(): String {
        return PathUtil.getExternalFileDir(AppContext.context, "style_cache_dir/$sessionId")
    }

    /**
     * 清除风格化缓存空间
     * 常规清除就清除sessionId对应的根路径即可
     */
    fun clearCache() {
        val dirName = PathUtil.getExternalFileDir(AppContext.context, "style_cache_dir/$sessionId")
        File(dirName).listFiles()?.forEach {
            it.deleteOnExit()
        }
    }

    /**
     * 当前图片身体检测数据
     */
    var bodyMaskData: BodyMaskData? = null

    /**
     * 预处理身体检测
     */
    @Synchronized
    private fun processBodyMaskData() {
        if (bodyMaskData == null) {
            val targetBitmap = NativeBitmap.createBitmap(
                imageData.image.copy(Bitmap.Config.ARGB_8888, true).processSize(300)
            )
            detectData.bodyMaskData = BodyMaskData().apply {
                width = targetBitmap.width
                height = targetBitmap.height
                maskBitmap = AiEngineSegmentUtils.detectBody(targetBitmap)?.also {
                    lightValue = it.maskData?.let { data ->
                        MteBaseEffectUtil.luminanceAverageThread(
                            data, this.width, this.height, null
                        )
                    } ?: 0
                }.toBitmap()
                textureId = TextureHelper.loadTexture(maskBitmap, false)
            }.also {
                bodyMaskData = it
            }
        }
    }

    /**
     * 处理身体边缘
     * @return true 第一次检测
     */
    private fun processBodyMaskEdge(): Boolean {
        //首次检测身体边缘
        if (isCanAutoDispersion == null) {
            isCanAutoDispersion = false
            bodyMaskData?.let {
                if (it.lightValue > 25 || detectData.faceDataBox.faceCount > 0) {
                    isCanAutoDispersion = true
                    it.maskBitmap?.let {
                        it.copy(Bitmap.Config.ARGB_8888, true).let {
                            PixAutoCutoutEdgeTool.runAutoCutoutEdge(it)
                            it.removeBlack().run {
                                bodyEdgeMask = this
                                maskGenerator?.setMaskSource(this)
                            }
                        }
                    }
                }
            }
            //首次边缘检测 默认进入状态处理
            if (isCanAutoDispersion == true) {
                handMaskApplyStateUIStateEvent.postValue(true)
                switchHandMaskUIState(isHandMaskMode = false)
            } else {
                switchHandMaskUIState(isHandMaskMode = true)
            }
            return true
        }
        return false
    }

    @Volatile
    var cancel: Boolean = false
        private set

    fun cancelQueue() {
        cancel = true
    }

}

