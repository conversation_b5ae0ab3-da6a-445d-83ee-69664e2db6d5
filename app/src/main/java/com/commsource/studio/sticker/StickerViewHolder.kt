package com.commsource.studio.sticker

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemStickerLayoutBinding
import com.commsource.duffle.sticker.Sticker
import com.commsource.statistics.ABTestManager
import com.commsource.studio.ImageStudioViewModel
import com.commsource.util.ViewUtils
import com.commsource.util.dp
import com.commsource.util.gone
import com.commsource.util.visible
import com.commsource.widget.list.XItem
import com.commsource.widget.list.XViewHolder

/**
 *
 * Created on 2020/7/29
 * <AUTHOR>
 */
open class StickerViewHolder : XViewHolder<Sticker> {

    var canShowNew: Boolean = false
    var mLastWidthOrHeightSize = 0

    val binding = ItemStickerLayoutBinding.bind(itemView)

    constructor(context: Context, parent: ViewGroup) : super(
        context,
        parent,
        R.layout.item_sticker_layout
    ) {
        binding.executePendingBindings()
        mLastWidthOrHeightSize = StickerItemDecoration.getSize(5)
        ViewUtils.setWidth(binding.flRoot, StickerItemDecoration.getSize(5))
        ViewUtils.setHeight(binding.flRoot, StickerItemDecoration.getSize(5))
    }

    /**
     * 刷新下载进度
     */
    private fun onDownloadProgressChange(sticker: Sticker) {
        when {
            sticker.isDownloading() -> {
                binding.rlv.visible()
                binding.ivDownload.gone()
            }

            sticker.needDownload() -> {
                binding.rlv.gone()
                binding.ivDownload.visible()
            }

            else -> {
                binding.rlv.gone()
                binding.ivDownload.gone()
            }
        }
    }

    override fun onBindViewHolder(position: Int, item: XItem<Sticker>, payloads: List<Any>?) {
        canShowNew = (adapter.getTag() as? Boolean) ?: false
        //todo renyong.huang
        var curSize = StickerItemDecoration.getSize(5)
        if (mLastWidthOrHeightSize != 0 && mLastWidthOrHeightSize != curSize) {
            mLastWidthOrHeightSize = curSize
            ViewUtils.setWidth(binding.flRoot, mLastWidthOrHeightSize)
            ViewUtils.setHeight(binding.flRoot, mLastWidthOrHeightSize)
        }
        if (payloads.isNullOrEmpty()) {
            val size = 45.dp()
            binding.flRoot.apply {
                thumbnailImageView.scaleType = ImageView.ScaleType.FIT_CENTER
                loadThumbnail(size, size) {
                    load(item.entity.stickerThumbnail)
                        .fadeTransition(150)
                }
            }

            if (ABTestManager.isNeedHideVipIcon(ABTestManager.TYPE_PHOTO_EDIT,true)) {
                binding.ivPayMark.gone()
            } else {
                ViewUtils.showOrHideView(
                    binding.ivPayMark, item.entity.needPaid() || item.entity.isCustom()
                )
            }
        }
        onDownloadProgressChange(item.entity)

        // 根据贴纸包属性，设置部分贴纸的信息
        binding.newIcon.visibility =
            if (item.entity.needShowNew() && canShowNew) View.VISIBLE else View.GONE
        binding.flRoot.setBackgroundColor(item.entity.getStiBgColor())
        ViewUtils.showOrHideView(binding.ivColorRing, item.entity.enableTint == 1)
        itemView.tag = item.entity.materialId
    }
}