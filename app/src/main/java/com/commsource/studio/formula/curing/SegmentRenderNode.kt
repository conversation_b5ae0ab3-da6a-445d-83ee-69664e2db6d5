package com.commsource.studio.formula.curing

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Rect
import androidx.annotation.WorkerThread
import androidx.core.graphics.alpha
import com.commsource.studio.StudioConfig
import com.commsource.util.ErrorNotifier
import com.commsource.util.UIHelper
import com.commsource.util.print
import com.meitu.http.AbsRequest
import com.meitu.http.ResponseListener
import com.meitu.http.XHttp
import com.meitu.http.api.RemoveBgApi
import com.meitu.http.exception.HttpException
import com.meitu.library.util.bitmap.BitmapUtils
import com.meitu.library.util.net.NetUtils

/**
 * 分割渲染Node
 */
class SegmentRenderNode : RenderNode() {

    companion object {
        const val RENDER_CONTENT_KEY = "REMOVE_BG"
    }

    override var renderContentKey: String? = RENDER_CONTENT_KEY
    private var request: AbsRequest? = null
    private var isCanceled = false

    @WorkerThread
    fun requestSegment(input: Bitmap): Bitmap? {
        var resultBitmap: Bitmap? = null
        if (!NetUtils.canNetworking()) {
            UIHelper.getInstance().post { ErrorNotifier.showNetworkErrorToast() }
            return resultBitmap
        }
        XHttp.getService(RemoveBgApi::class.java)
            .getBitmapFromCloud(
                input,
                isNeedCopy = false,
                true,
                needWeakNetTips = false,
                responseListener = object : ResponseListener<RemoveBgApi.RemoveBgResponse> {
                    //正常请求回来的在线
                    override fun onNext(t: RemoveBgApi.RemoveBgResponse?) {
                        ">>>>>>去背景请求成功${isCanceled}>>>>>>".print("yyp")
                        t?.takeIf { !isCanceled }?.let {
                            val labMaskBitmap = it.result?.first
                            if (BitmapUtils.isAvailableBitmap(labMaskBitmap)) {
                                resultBitmap = labMaskBitmap?.let { cropBitmap(it) }
                            }
                        }
                    }

                    override fun onError(throwable: Throwable?) {
                        super.onError(throwable)
                        throwable?.printStackTrace()
                        UIHelper.getInstance().post {
                            if (throwable is HttpException) {
                                ErrorNotifier.showServerErrorToast()
                            } else {
                                ErrorNotifier.showNetworkErrorToast()
                            }
                        }
                    }
                }).also {
                request = it
            }
        return resultBitmap
    }

    override fun cancel() {
        isCanceled = true
        request?.cancel()
    }

    override fun render(
        inputBitmap: Bitmap,
        handleNext: (resultBitmap: Bitmap) -> Unit
    ) {
        if (!StudioConfig.needShowRemoveBgUpdate()) {
            val result = requestSegment(inputBitmap)
            handleNext.invoke(result ?: inputBitmap)
        } else {
            handleNext.invoke(inputBitmap)
        }
    }

    fun cropBitmap(oriBitmap: Bitmap): Bitmap {
        val oriH = oriBitmap.height
        val oriW = oriBitmap.width
        val pixels = BitmapUtils.bitmap2Int(oriBitmap)
        // 计算裁剪框
        var left = oriBitmap.width
        var right = -1
        var top = oriBitmap.height
        var bottom = -1
        var offset = 0
        for (i in 0 until oriH) {
            offset = i * oriW
            for (j in 0 until oriW) {
                val alpha = pixels[offset + j].alpha
                if (alpha > 0) {
                    if (j < left) {
                        left = j
                    }
                    if (j > right) {
                        right = j
                    }
                    if (i < top) {
                        top = i
                    }
                    if (i > bottom) {
                        bottom = i
                    }
                }
            }
        }
        val srcRect = Rect(left, top, right, bottom)
        val dstRect = Rect(0, 0, srcRect.width(), srcRect.height())
        val temp = Bitmap.createBitmap(dstRect.width(), dstRect.height(), Bitmap.Config.ARGB_8888)
        val tempCanvas = Canvas(temp)
        tempCanvas.drawBitmap(oriBitmap, srcRect, dstRect, null)
        return temp
    }
}