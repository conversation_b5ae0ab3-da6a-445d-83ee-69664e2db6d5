package com.commsource.studio.formula.convert

import android.graphics.Color
import com.commsource.repository.child.GradientRepository
import com.commsource.repository.child.TextureRepository
import com.commsource.studio.bean.BgLayerInfo
import com.commsource.studio.function.background.*
import com.meitu.library.util.io.FileUtils
import com.meitu.webview.utils.GsonHelper
import java.io.File

/**
 * @Description : 背景效果Convert
 * <AUTHOR> bear
 * @Date : 2021/11/23
 */
class BackgroundLayerConverter(effectConverter: EffectConverter) :
    Converter<BgLayerInfo>(FormulaConstant.Background, effectConverter) {

    override fun convertLayerInfo(
        wrapper: FormulaWrapper,
        formulaLayer: FormulaLayer
    ): BgLayerInfo? {
        return BgLayerInfo().apply {
            //其实这个就是canvas的高度宽度
            width = wrapper.canvasWidth
            height = wrapper.canvasHeight
            cropEnum = ConvertFun.convertFraming(wrapper)
            isFromFormulaCrop = true
            if (formulaLayer.effectParams?.background != null) {
                isFromFormulaColor = true
                val alpha = formulaLayer.effectParams.background.alpha ?: 1.0f
                val alphaStr = ConvertFun.singleHexColor((alpha * 255).toInt())
                formulaLayer.effectParams.background.color?.let {
                    val colorStr = "#" + alphaStr + it.replace("0x", "")
                    val color = try {
                        Color.parseColor(colorStr)
                    } catch (e: Exception) {
                        Color.WHITE
                    }
                    backgroundType = ShaderHelper.createColorBackgroundType(
                        color,
                        isFromPicker = true
                    )
                }
            }
            formulaLayer.products?.forEach {
                when (it.type) {
                    FormulaConstant.OnlineBackgroundGradientType -> {
                        GradientRepository.findGradientMaterial(it.m_id)?.let { material ->
                            //这里一定要下载好了 如果没下载好 是前面的流程出错了
                            //如果配置还没解析 这里解析一下配置到内存中
                            if (material.config == null) {
                                val targetPath =
                                    GradientRepository.localSDRootPath + material.id + ".json"
                                val string = FileUtils.readFile(targetPath)
                                material.config = GsonHelper.fromJsonNoException(
                                    string,
                                    GradientConfig::class.java
                                )
                            }
                            //配置加载
                            material.config?.let {
                                val backgroundColor = BackgroundColor(
                                    material.id,
                                    material.icon ?: "",
                                    it.type,
                                    material.needPaid(),
                                    it.startPoint,
                                    it.endPoint,
                                    it.colors,
                                    it.locations,
                                    material.isInternal(),
                                    GradientRepository.getMaterialPath(material)
                                )
                                isFromFormulaColor = true
                                backgroundType =
                                    ShaderHelper.translate2BackgroundType(backgroundColor)
                            }
                        }
                    }
                    FormulaConstant.OnlineBackgroundTextureType -> {
                        TextureRepository.findTextureMaterial(it.m_id)?.let { material ->
                            if (material.config == null) {
                                val json =
                                    FileUtils.readFile(TextureRepository.getTexturePath(material) + File.separator + "build-in-texture.json")
                                material.config =
                                    GsonHelper.fromJsonNoException(json, TextureConfig::class.java)
                            }
                            material.config?.run {
                                val path = TextureRepository.getTexturePath(material)
                                isFromFormulaTexture = true
                                backgroundTexture = BackgroundTexture(
                                    material.id,
                                    textureMode,
                                    alpha,
                                    null,
                                    darkBlend,
                                    lightBlend,
                                    whiteBlend,
                                    blackBlend,
                                    material.needPaid(),
                                    material.isInternal(),
                                    isSvg,
                                    path
                                ).apply {
                                    formulaLayer.effectParams?.background?.texture?.let {
                                        alpha = it.alpha
                                        textureScaleFactor = it.widthRatio
                                        textureOffsetX = it.offsetX
                                        textureOffsetY = it.offsetY
                                        blend = it.blend?.let { BackgroundTexture.blendModeToString(it) }
                                        isSetValue = true
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    override fun generateFormulaLayer(
        wrapper: GenerateFormulaWrapper,
        bgLayerInfo: BgLayerInfo
    ): FormulaLayer {
        val products = ArrayList<FormulaProduct>()
        bgLayerInfo.backgroundType
        //如果背景渐变存在 就是用了商品渐变色
        val background = if (bgLayerInfo.backgroundType?.backgroundColor != null) {
            bgLayerInfo.backgroundType?.backgroundColor?.let {
                products.add(
                    FormulaProduct(
                        m_id = it.id,
                        FormulaConstant.OnlineBackgroundGradientType,
                        asset_url = null
                    )
                )
            }
            // 前两个参数随便。由于上面设置 FormulaConstant.OnlineBackgroundGradientType,
            // 所以最终backgroundType会被上面覆盖掉。
            FormulaBackground("0xffffff", 1f,
                bgLayerInfo.backgroundTexture?.let {
                    BgTexture(
                        it.blend?.let { BackgroundTexture.blendModeToInt(it) },
                        it.alpha,
                        it.textureOffsetX,
                        it.textureOffsetY,
                        it.textureScaleFactor,
                        it.textureRatio
                    )
                }
            )
        } else {
            val color = bgLayerInfo.backgroundType?.pureColor ?: Color.WHITE
            FormulaBackground(
                ConvertFun.generateHexColor(color),
                Color.alpha(color).toFloat() / 255,
                bgLayerInfo.backgroundTexture?.let {
                    BgTexture(
                        it.blend?.let { BackgroundTexture.blendModeToInt(it) },
                        it.alpha,
                        it.textureOffsetX,
                        it.textureOffsetY,
                        it.textureScaleFactor,
                        it.textureRatio
                    )
                }
            )
        }
        bgLayerInfo.backgroundTexture?.let {
            products.add(
                FormulaProduct(
                    m_id = it.id,
                    FormulaConstant.OnlineBackgroundTextureType,
                    asset_url = null
                )
            )
        }
        return FormulaLayer(
            FormulaConstant.Background, null, Position(
                bgLayerInfo.position.centerOffset.x,
                bgLayerInfo.position.centerOffset.y,
                bgLayerInfo.position.ratio,
                bgLayerInfo.position.rotate,
                bgLayerInfo.width,
                bgLayerInfo.height,
                flipX = false,
                flipY = false,
                cropInfo = null
            ), null,
            EffectParams(null, null, background, null, null), products
        )
    }
}