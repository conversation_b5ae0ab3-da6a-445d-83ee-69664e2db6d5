package com.commsource.studio.render

import com.commsource.studio.effect.ColourResult

class ColorTemperatureRenderProxy(private val colourResult: ColourResult) : MTEffectRenderProxy("studio/temperature") {

    override fun onRender(fboA: Int, fboB: Int, texA: Int, texB: Int, width: Int, height: Int, isCaptureFrame: Boolean): Int {
        mtEffectRender.changeUniformValue(1001, "alpha", colourResult.effectAlpha, 1)
        return super.onRender(fboA, fboB, texA, texB, width, height, isCaptureFrame)
    }
}