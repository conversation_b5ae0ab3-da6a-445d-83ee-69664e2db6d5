package com.commsource.studio

import android.graphics.PointF
import android.os.Handler
import android.os.Message
import android.view.*
import com.commsource.studio.BpGestureDetector.OnGestureListener
import com.commsource.studio.BpGestureDetector.SimpleOnGestureListener
import com.commsource.util.TimeLog
import com.commsource.util.UIHelper
import com.commsource.util.common.MathUtil
import com.commsource.util.print
import com.meitu.common.AppContext
import java.lang.ref.WeakReference
import java.util.*
import kotlin.math.abs

/**
 * [GestureDetectorPro]是[GestureDetector]和[ScaleGestureDetector]
 * 的混合高级版，它重新设计了原有的手势，并且拓展出更多的手势供你使用。
 *
 * <AUTHOR> 2016.01.21
 * @see OnGestureListener
 *
 * @see SimpleOnGestureListener
 */
class BpGestureDetector(onGestureListener: OnGestureListener?) {
    private val TAG = "BpGestureDetector"

    /**
     * 距离临界值，表示手势被判定为点击时所允许的手指最大移动距离。
     */
    private val mTouchSlopSquare: Int

    /**
     * 距离临界值，表示手势被判定为双击时所允许的手指最大移动距离。
     */
    private val mDoubleTapTouchSlopSquare: Int

    /**
     * 距离临界值，表示手势被判定为双击时所允许的两次点击位置之间的最大距离，
     * 也就是说双击其实不仅仅要求你要在短时间内点击两次屏幕，而且这两次点击位置的距离还不能太远。
     */
    private val mDoubleTapSlopSquare: Int

    /**
     * 速度临界值，表示手势被判定为速滑时所需要的手指最小移动速度。
     */
    private val mMinimumFlingVelocity: Int

    /**
     * 速度临界值，表示手势被判定为速滑时所允许的手指最大移动速度。
     */
    private val mMaximumFlingVelocity: Int
    private val mHandler: Handler

    /**
     * 用于标识手指是否还按在屏幕上。
     */
    private var mStillDown = false

    /**
     * 用于标识推迟触发单击手势，考虑这样一种情况，当用户点击屏幕没有松开并且还没有达到触发长按手势的情况，
     * 这种情况下 GestureDetector 会将单击手势推迟触发，直到用户松开手指的时候，判断是否达到触发长按手
     * 势的临界值，如果没有，就触发被推迟的单击手势。
     */
    private var mDeferConfirmSingleTap = false

    /**
     * 用于标识当前处于长按状态。
     */
    private var mInLongPress = false

    /**
     * 是否支持长按滑动
     */
    private var allowLongPressMove = true

    /**
     * 表示当前处于单击手势所允许的手指移动范围内。
     */
    private var mAlwaysInTapRegion = false

    /**
     * 表示当前处于双击双击所允许的手指移动范围内。
     */
    private var mAlwaysInBiggerTapRegion = false

    /**
     * 标识长按手势是否可用，默认可用。
     */
    private var mIsLongPressEnabled: Boolean
    private var mVelocityTracker: VelocityTracker? = null
    private var mLongPressTimeout: Int = LONG_PRESS_TIMEOUT
    private var mValidDegreeOfLeftToRight: Float = DEFAULT_VALID_DEGREE_OF_LEFT_TO_RIGHT
    private var mValidDegreeOfRightToLeft: Float = DEFAULT_VALID_DEGREE_OF_RIGHT_TO_LEFT
    private var mValidDegreeOfTopToBottom: Float = Companion.DEFAULT_VALID_DEGREE_OF_TOP_TO_BOTTOM
    private var mValidDegreeOfBottomToTop: Float = Companion.DEFAULT_VALID_DEGREE_OF_BOTTOM_TO_TOP
    private var mLastFocusX = 0f
    private var mLastFocusY = 0f
    private var mCurrentFocusX = 0f
    private var mCurrentFocusY = 0f
    private var mDownFocusX = 0f
    private var mDownFocusY = 0f
    private val mOnGestureListener: OnGestureListener
    private var mPreviousDownEvent: MotionEvent? = null
    private var mPreviousUpEvent: MotionEvent? = null
    private var mCurrentDownEvent: MotionEvent? = null
    private var mCurrentUpEvent: MotionEvent? = null
    private var mIsInLongPress = false

    /**
     * 获取当前屏幕上的手指个数，该数值永远大于等于1。
     *
     * @return 手指个数
     */
    var pointerCount = 0
        private set

    /**
     * 标识是否允许触发Fling，当出现多指操作时就不允许触发Fling，因为多指Fling往往属于误触。
     */
    private var mCanFling = false
    private var mIsMultipleFlingEnabled = false
    private var mFlingSlopSquare: Float = Companion.DEFAULT_FLING_SLOP_SQUARE
    private val mCurrentFocus = PointF()

    /**
     * move敏感标识
     */
    private var mIsMoveSensitive = false

    /**
     * Fling敏感标识
     */
    private var isFlingSensitive = false

    /**
     * 双指操作时的距离。
     */
    private var mDoubleTouchDistance = 0f
    private var mLastDoubleTouchDistance = 0f

    /**
     * 双指操作时的角度。
     */
    private var mDoubleTouchAngle = 0f
    private var mLastDoubleTouchAngle = 0f

    /**
     * 上次触发的双指手势。
     */
    private var mLastTouch1 = PointF()
    private var mLastTouch2 = PointF()

    /**
     * GestureDetector所有延时操作都是利用Handler的延时消息实现的，例如长按事件、双击和显示手指按压状态。
     */
    private class GestureHandler internal constructor(gestureDetector: BpGestureDetector) :
        Handler() {
        private val mWeakDetector: WeakReference<BpGestureDetector>
        override fun handleMessage(msg: Message) {
            val detector = mWeakDetector.get()
            if (detector != null) {
                when (msg.what) {
                    MSG_SHOW_PRESS ->                         // 显示手指点击状态。
                        detector.mOnGestureListener.onShowPress(detector.mCurrentDownEvent)
                    Companion.MSG_LONG_PRESS ->                         // 长按事件。
                        detector.dispatchLongPress()
                    Companion.MSG_TAP ->                         // 这里根据手势是否已经抬起来判断是否时单击事件。
                        if (detector.mOnGestureListener != null) {
                            if (!detector.mStillDown) {
                                // 手指已经松开并且到了回调单击事件的时间。
                                detector.mOnGestureListener.onSingleTap(
                                    detector.mCurrentDownEvent!!,
                                    detector.mCurrentUpEvent!!
                                )
                            } else {
                                // 到了回调单击事件的时间，但是手指还没有松开，将回调时机推迟到手指松开的时候。
                                detector.mDeferConfirmSingleTap = true
                            }
                        }
                    else -> throw RuntimeException("Unknown message $msg")
                }
            }
        }

        /**
         * 直接在当前线程创建Handler。
         */
        init {
            mWeakDetector = WeakReference(gestureDetector)
        }
    }

    /**
     * * 在[android.view.View.onTouchEvent]里调用该方法，之后[GestureDetectorPro]
     * 会解析给定的事件信息，当手势被触发的时候会通过[OnGestureListener]的回调方法通知你。
     *
     *
     * 不要过分依赖于该方法的返回值，当[OnGestureListener]的所有回调方法都返回false时，
     * 该方法的返回值永远都是false，看下面的例子：
     * <pre>`
     * public void onTouchEvent(MotionEvent event) {
     * boolean isConsumed = gestureDetector.onTouchEvent(event);
     * if (isConsumed) {
     * // 这段代码永远不会被执行
     * do something
     * }
     * return handled;
     * }
    `</pre> *
     *
     * @param event 当前事件信息
     * @return true代表消费了该事件
     * @param event
     * @return
     */
    fun onTouchEvent(event: MotionEvent): Boolean {
        val timeLog = TimeLog.createAndStart()
        pointerCount = event.pointerCount
        val action = event.action
        val focus = calculateFocus(action, event)
        mCurrentFocus.set(focus)

        // 用于计算手指滑动速度的工具类。
        if (mVelocityTracker == null) {
            mVelocityTracker = VelocityTracker.obtain()
        }
        mVelocityTracker!!.addMovement(event)
        var handled = false
        when (action and MotionEvent.ACTION_MASK) {
            MotionEvent.ACTION_POINTER_DOWN -> handled = onActionPointerDown(focus, event)
            MotionEvent.ACTION_POINTER_UP -> handled = onActionPointerUp(focus, event)
            MotionEvent.ACTION_DOWN -> handled = onActionDown(focus, event)
            MotionEvent.ACTION_UP -> handled = onActionUp(focus, event)
            MotionEvent.ACTION_MOVE -> handled = onActionMove(focus, event)
            MotionEvent.ACTION_CANCEL -> onCancel(focus, event)
            else -> {
            }
        }
        mOnGestureListener.onGestureEnd(event)
//        "检测手势耗时：${timeLog.update()}".print(TAG)
        return handled
    }

    /**
     * 根据当前事件信息计算出焦点位置，单指操作的时候，手指所在的位置就是
     * 焦点位置，多指操作的时候，我们将每个手指的坐标相加求平均值来计算出
     * 焦点位置。
     *
     * @param action 动作信息
     * @param event  当前事件信息
     * @return 焦点位置
     */
    private fun calculateFocus(action: Int, event: MotionEvent): PointF {
        val focus = PointF()

        // 判断是不是抬起某个次要手指（第一个按下的手指为主要手指，其他都是次要手指），然后获取该手指的索引。
        val pointerUp = action and MotionEvent.ACTION_MASK == MotionEvent.ACTION_POINTER_UP
        val skipIndex = if (pointerUp) event.actionIndex else -1

        // 多指操作时需要计算出焦点，计算的方式是累加所有按在屏幕上的手指的坐标值，
        // 然后求平均值。当只有一个手指操作的时候，该手指的触碰点就是焦点。
        var sumX = 0f
        var sumY = 0f
        val count = event.pointerCount
        for (i in 0 until count) {
            if (skipIndex == i) {
                continue
            }
            sumX += event.getX(i)
            sumY += event.getY(i)
        }
        val div = if (pointerUp) count - 1 else count
        val focusX = sumX / div
        val focusY = sumY / div
        focus[focusX] = focusY
        return focus
    }

    /**
     * 处理[MotionEvent.ACTION_CANCEL]事件。
     *
     * @param focus 当前焦点位置
     * @param event 当前事件信息
     */
    private fun onCancel(focus: PointF, event: MotionEvent) {
        cancel()
        mOnGestureListener.onMajorFingerUp(event)
    }

    /**
     * 处理[MotionEvent.ACTION_POINTER_DOWN]事件。
     *
     * @param focus 当前焦点位置
     * @param event 当前事件信息
     * @return true表示消费了该事件
     */
    private fun onActionPointerDown(focus: PointF, event: MotionEvent): Boolean {
        val handled: Boolean
        mLastFocusX = focus.x
        mDownFocusX = mLastFocusX
        mLastFocusY = focus.y
        mDownFocusY = mLastFocusY
        handled = mOnGestureListener.onMinorFingerDown(event)
        cancelTaps()
        mCanFling = mIsMultipleFlingEnabled
        if (pointerCount == 2) {
            mOnGestureListener.onDoubleGestureStart(
                PointF(event.getX(0), event.getY(0)),
                PointF(event.getX(1), event.getY(1)),
                focus
            )
        }
        if (isInDoubleTouchGesture(event)) {
            mDoubleTouchDistance =
                MathUtil.getDistance(event.getX(0), event.getY(0), event.getX(1), event.getY(1))
            mDoubleTouchAngle =
                calculateDegree(event.getX(0), event.getY(0), event.getX(1), event.getY(1))
            mLastDoubleTouchDistance = mDoubleTouchDistance
            mLastDoubleTouchAngle = mDoubleTouchAngle
            mLastTouch1.set(event.getX(0), event.getY(0))
            mLastTouch2.set(event.getX(1), event.getY(1))
        }
        return handled
    }

    /**
     * 处理[MotionEvent.ACTION_POINTER_UP]事件。
     *
     * @param focus 当前焦点位置
     * @param event 当前事件信息
     * @return true表示消费了该事件
     */
    private fun onActionPointerUp(focus: PointF, event: MotionEvent): Boolean {
        val handled: Boolean
        mLastFocusX = focus.x
        mDownFocusX = mLastFocusX
        mLastFocusY = focus.y
        mDownFocusY = mLastFocusY

        // 当多指操作下，有一个手指松开时，计算松开手指的速度和其他未松开手指的速度的数量积（向量的点乘），如果
        // 得到的数量积是负数，说明松开的手指正沿着和其他手指完全相反的方向移动，也就是说这时候我们的手指是正在
        // 做一个捏和或放大的动作，GestureDetector不支持该手势，所以这时候就清除VelocityTracker的数据。
        // Check the dot product of current velocities.
        // If the pointer that left was opposing another velocity vector, clear.
        mVelocityTracker!!.computeCurrentVelocity(1000, mMaximumFlingVelocity.toFloat())

        // 获取松开的手指的速度。
        val upIndex = event.actionIndex
        val id1 = event.getPointerId(upIndex)
        val x1 = mVelocityTracker!!.getXVelocity(id1)
        val y1 = mVelocityTracker!!.getYVelocity(id1)

        // 进行遍历，把松开的手指的速度和其他手指的速度进行向量点乘，判断松开的手指是否和其他手指背道而驰。
        for (i in 0 until event.pointerCount) {
            if (i == upIndex) {
                continue
            }
            val id2 = event.getPointerId(i)
            val x = x1 * mVelocityTracker!!.getXVelocity(id2)
            val y = y1 * mVelocityTracker!!.getYVelocity(id2)
            val dot = x + y

            // 松开的手指与某个未松开手指的速度点乘值小于零，清空数据。
            if (dot < 0) {
                mVelocityTracker!!.clear()
                break
            }
        }
//        if (pointerCount < 2) {
//            mOnGestureListener.onPinchEnd(this)
//        }
        handled = mOnGestureListener.onMinorFingerUp(event)
        if (pointerCount == 2) {
            mOnGestureListener.onDoubleGestureStart(
                PointF(event.getX(0), event.getY(0)),
                PointF(event.getX(1), event.getY(1)),
                focus
            )
        }

        if (isInDoubleTouchGesture(event)) {
            mDoubleTouchDistance =
                MathUtil.getDistance(event.getX(0), event.getY(0), event.getX(1), event.getY(1))
            mDoubleTouchAngle =
                calculateDegree(event.getX(0), event.getY(0), event.getX(1), event.getY(1))
            mLastDoubleTouchDistance = mDoubleTouchDistance
            mLastDoubleTouchAngle = mDoubleTouchAngle
            mLastTouch1.set(event.getX(0), event.getY(0))
            mLastTouch2.set(event.getX(1), event.getY(1))
        }
        return handled
    }

    /**
     * 处理[MotionEvent.ACTION_DOWN]事件。
     *
     * @param focus 当前焦点位置
     * @param event 当前事件信息
     * @return true表示消费了该事件
     */
    private fun onActionDown(focus: PointF, event: MotionEvent): Boolean {
        var handled = false
        mLastFocusX = focus.x
        mDownFocusX = mLastFocusX
        mLastFocusY = focus.y
        mDownFocusY = mLastFocusY
        mCurrentDownEvent = copyMotionEvent(mCurrentDownEvent, event)

        // 判断是否有单击事件的消息在队列中等待被触发，如果有的话就移除该回调消息，可以看出双击和单击是互斥的。
        val hadTapMessage = mHandler.hasMessages(MSG_TAP)
        if (hadTapMessage) {
            mHandler.removeMessages(MSG_TAP)
        }

        // 判断双击事件时需要上一次点击的事件信息，如果为null，说明不是双击，如果具备了必要的信息，则
        // 对两次点击的时间差和点击位置进行判断，看是否符合双击要求。
        if (mCurrentDownEvent != null
            && mPreviousUpEvent != null
            && !hadTapMessage
            && isConsideredDoubleTap(mCurrentDownEvent!!, mPreviousUpEvent!!, mCurrentDownEvent!!)
        ) {
            handled = mOnGestureListener.onDoubleTap(
                mPreviousDownEvent,
                mPreviousUpEvent,
                mCurrentDownEvent
            )
        } else {
            // 如果不是双击事件，则往消息队列中发送一个回调单击事件的延时消息，该消息的延迟的时间
            // 长短为双击事件中两次点击的最大时间差，该时间内如果没有再次点击，则认为是单击事件。
            // This is a first tap
            mHandler.sendEmptyMessageDelayed(MSG_TAP, 100)
        }

        // 回收上一次点击时按下手指的事件，然后记录本次按下手指的事件。
        mPreviousDownEvent = mCurrentDownEvent

        // 设置标识信息。
        mAlwaysInTapRegion = true
        mAlwaysInBiggerTapRegion = true
        mStillDown = true
        mInLongPress = false
        mDeferConfirmSingleTap = false
        mCanFling = true
        if (mCurrentDownEvent != null) {
            // 每次按下手指的时候都会取消前一个回调长按事件的消息并且往消息队列中重新发送一个回调长按事件的消息，
            // 该消息会延迟一定时间触发，当手指松开的时候该消息会被从消息队列中移除。
            if (mIsLongPressEnabled) {
                mHandler.removeMessages(MSG_LONG_PRESS)
                mHandler.sendEmptyMessageAtTime(
                    MSG_LONG_PRESS,
                    mCurrentDownEvent!!.downTime + mLongPressTimeout
                )
            }

            // 往消息队列中发送高亮显示点击状态的延时消息，在100毫秒之后会触发onShowPress回调。
            mHandler.sendEmptyMessageAtTime(
                MSG_SHOW_PRESS,
                mCurrentDownEvent!!.downTime + TAP_TIMEOUT
            )
        }
        handled = handled or mOnGestureListener.onMajorFingerDown(event)
        return handled
    }

    /**
     * 处理[MotionEvent.ACTION_UP]事件。
     *
     * @param focus 当前焦点位置
     * @param event 当前事件信息
     * @return true表示消费了该事件
     */
    private fun onActionUp(focus: PointF, event: MotionEvent): Boolean {
        var handled = false
        mCurrentUpEvent = copyMotionEvent(mCurrentUpEvent, event)

        // 手指已经离开屏幕。
        mStillDown = false
        if (mInLongPress) {
            // 当处于长按状态时，会移除消息队列中的单击事件。
            mHandler.removeMessages(MSG_TAP)
            mInLongPress = false
        } else if (mAlwaysInTapRegion && mDeferConfirmSingleTap) {
            mOnGestureListener.onSingleTap(mCurrentDownEvent!!, mCurrentUpEvent!!)
        }
        if (mCanFling) {

            // 计算当前位置和起始点的位置之间的距离。
            val deltaX = (focus.x - mDownFocusX).toInt()
            val deltaY = (focus.y - mDownFocusY).toInt()
            val distance = deltaX * deltaX + deltaY * deltaY

            // 手指快速滑动的操作，称之为Fling。
            // A fling must travel the minimum tap distance
            val velocityTracker = mVelocityTracker
            val pointerId = event.getPointerId(0)
            velocityTracker!!.computeCurrentVelocity(1000, mMaximumFlingVelocity.toFloat())
            val velocityY = velocityTracker.getYVelocity(pointerId)
            val velocityX = velocityTracker.getXVelocity(pointerId)
            if (isFlingSensitive) {
                if (Math.abs(velocityY) > mMinimumFlingVelocity || Math.abs(velocityX) > mMinimumFlingVelocity) {
                    handled =
                        mOnGestureListener.onFling(mCurrentDownEvent, event, velocityX, velocityY)
                    handled = handled or dispatchDirectionFling(
                        mCurrentDownEvent!!,
                        mCurrentUpEvent!!,
                        velocityX,
                        velocityY
                    )
                }
            } else {
                if (distance > mFlingSlopSquare
                    && (Math.abs(velocityY) > mMinimumFlingVelocity || Math.abs(velocityX) > mMinimumFlingVelocity)
                ) {
                    handled =
                        mOnGestureListener.onFling(mCurrentDownEvent, event, velocityX, velocityY)
                    handled = handled or dispatchDirectionFling(
                        mCurrentDownEvent!!,
                        mCurrentUpEvent!!,
                        velocityX,
                        velocityY
                    )
                }
            }
        }

        // 记录本次手指抬起的动作事件。
        mPreviousUpEvent = mCurrentUpEvent

        // 回收VelocityTracker。
        if (mVelocityTracker != null) {
            // This may have been cleared when we called out to the
            // application above.
            mVelocityTracker!!.recycle()
            mVelocityTracker = null
        }
        mDeferConfirmSingleTap = false

        // 移除其他事件。
        mHandler.removeMessages(MSG_SHOW_PRESS)
        mHandler.removeMessages(MSG_LONG_PRESS)
        if (mAlwaysInTapRegion) {
            handled = mOnGestureListener.onTap(mCurrentDownEvent!!, mCurrentUpEvent!!)
        }
        if (mIsInLongPress) {
            handled = handled or mOnGestureListener.onLongPressUp(mCurrentUpEvent!!)
            mIsInLongPress = false
        }
        handled = handled or mOnGestureListener.onMajorFingerUp(event)
        return handled
    }

    /**
     * 处理[MotionEvent.ACTION_MOVE]事件。
     *
     * @param focus 当前焦点位置
     * @param event 当前事件信息
     * @return true表示消费了该事件
     */
    private fun onActionMove(focus: PointF, event: MotionEvent): Boolean {
        // 避免和长按事件冲突。
        if (mInLongPress && !allowLongPressMove) {
            return false
        }
        var handled = false

        // 根据两次滑动时焦点的位置计算出手指每一次平移的距离。
        val scrollX = mLastFocusX - focus.x
        val scrollY = mLastFocusY - focus.y
        if (mAlwaysInTapRegion) {
            // 手指按下时首次滑动需要判断滑动距离是否超出了点击的有效范围。

            // 根据手指按下时的焦点位置和当前滑动到的焦点位置计算出手指总共平移的距离。
            val deltaX = (focus.x - mDownFocusX).toInt()
            val deltaY = (focus.y - mDownFocusY).toInt()
            val distance = deltaX * deltaX + deltaY * deltaY

            // 当手指滑动的距离大于临界值时才会触发滑动回调。
            if (distance > mTouchSlopSquare || mIsMoveSensitive) {
                handled = mOnGestureListener.onScroll(mCurrentDownEvent, event, scrollX, scrollY)
                if (event.pointerCount == 1) {
                    handled = handled or mOnGestureListener.onMajorScroll(
                        mCurrentDownEvent!!,
                        event,
                        scrollX,
                        scrollY
                    )
                }

                // 每次触发滑动回调之后都会记录下本次的焦点位置用于和下一次滑动时的焦点位置做比较。
                mLastFocusX = focus.x
                mLastFocusY = focus.y
                mAlwaysInTapRegion = false

                // 触发滑动回调之后会移除单击、高亮和长按回调，可以看出滑动和单双击、触摸反馈、长按是互斥的。
                mHandler.removeMessages(MSG_TAP)
                mHandler.removeMessages(MSG_SHOW_PRESS)
                mHandler.removeMessages(MSG_LONG_PRESS)
            }

            // 当手指点击时如果有滑动的一定距离，且该距离超过了双击临界值，则认为不是双击操作。
            if (distance > mDoubleTapTouchSlopSquare) {
                mAlwaysInBiggerTapRegion = false
            }
        } else if (Math.abs(scrollX) >= 1 || Math.abs(scrollY) >= 1) {
            // 到这里的时候手指滑动的距离已经都是超过点击有效距离的最小值了，而且我们可以看出当滑
            // 动的距离超过1px的时候就会出发一次onScroll。
            handled = mOnGestureListener.onScroll(mCurrentDownEvent, event, scrollX, scrollY)
            if (event.pointerCount == 1) {
                handled = handled or mOnGestureListener.onMajorScroll(
                    mCurrentDownEvent!!,
                    event,
                    scrollX,
                    scrollY
                )
            }
            mLastFocusX = focus.x
            mLastFocusY = focus.y
        }
        // 双指手势计算。
        if (isInDoubleTouchGesture(event)) {
            var scale = 1f
            val firstDegree =
                calculateDegree(event.getX(0), event.getY(0), mLastTouch1.x, mLastTouch1.y)
            val secondDegree =
                calculateDegree(event.getX(1), event.getY(1), mLastTouch2.x, mLastTouch2.y)
            if (abs(firstDegree - secondDegree) > 30 && mLastDoubleTouchDistance > 0) {
                scale = mDoubleTouchDistance / mLastDoubleTouchDistance
            }
            mLastDoubleTouchDistance = mDoubleTouchDistance
            mLastDoubleTouchAngle = mDoubleTouchAngle
            mDoubleTouchDistance =
                MathUtil.getDistance(event.getX(0), event.getY(0), event.getX(1), event.getY(1))
            mDoubleTouchAngle =
                calculateDegree(event.getX(0), event.getY(0), event.getX(1), event.getY(1))
            mOnGestureListener.onDoubleGestureMove(
                scale,
                mDoubleTouchAngle - mLastDoubleTouchAngle,
                focus
            )
            mLastTouch1.set(event.getX(0), event.getY(0))
            mLastTouch2.set(event.getX(1), event.getY(1))
        }
        return handled
    }

    /**
     * 当前是否处于双指手势中。
     */
    private fun isInDoubleTouchGesture(event: MotionEvent): Boolean {
        return event.pointerCount >= 2
    }

    /**
     * 复制指定的[MotionEvent]并且回收指定的[MotionEvent]。
     *
     * @param recycledEvent 要被回收的[MotionEvent]
     * @param targetEvent   要被复制的[MotionEvent]
     * @return MotionEvent复制对象
     */
    private fun copyMotionEvent(
        recycledEvent: MotionEvent?,
        targetEvent: MotionEvent
    ): MotionEvent {
        val obtain = MotionEvent.obtain(targetEvent)
        recycledEvent?.recycle()
        return obtain
    }

    /**
     * 取消所有事件。
     */
    private fun cancel() {
        // 取消手指点击状态监听。
        mHandler.removeMessages(MSG_SHOW_PRESS)

        // 取消长按监听。
        mHandler.removeMessages(MSG_LONG_PRESS)

        // 取消点击监听。
        mHandler.removeMessages(MSG_TAP)
        mVelocityTracker!!.recycle()
        mVelocityTracker = null
        mStillDown = false
        mAlwaysInTapRegion = false
        mAlwaysInBiggerTapRegion = false
        mDeferConfirmSingleTap = false
        if (mInLongPress) {
            mInLongPress = false
        }
    }

    /**
     * 分发长按事件。
     */
    private fun dispatchLongPress() {
        // 移除单击消息，可以看出长按和单双击之间是互斥的。
        mHandler.removeMessages(MSG_TAP)
        mDeferConfirmSingleTap = false
        mAlwaysInTapRegion = false
        mInLongPress = true
        mIsInLongPress = true
        mOnGestureListener.onLongPress(mCurrentDownEvent!!)
    }

    /**
     * 分发具有方向的速滑事件。
     *
     * @param downEvent 手指按压事件
     * @param upEvent   手指抬起事件
     * @param velocityX X轴的滑动速度（px/second）
     * @param velocityY Y轴的滑动速度（px/second）
     */
    private fun dispatchDirectionFling(
        downEvent: MotionEvent, upEvent: MotionEvent,
        velocityX: Float, velocityY: Float
    ): Boolean {
        var handled = false
        val downX = downEvent.x
        val downY = downEvent.y
        val upX = upEvent.x
        val upY = upEvent.y
        val degree = calculateDegree(downX, downY, upX, upY)

        // 判断是否是从左向右滑。
        if (degree >= mValidDegreeOfLeftToRight && degree <= 90 + mValidDegreeOfBottomToTop) {
            handled =
                mOnGestureListener.onFlingFromLeftToRight(downEvent, upEvent, velocityX, velocityY)
        }

        // 判断是否是从右向左滑。
        if (degree <= -90 + mValidDegreeOfRightToLeft && degree >= -90 - mValidDegreeOfRightToLeft) {
            handled = handled or mOnGestureListener.onFlingFromRightToLeft(
                downEvent,
                upEvent,
                velocityX,
                velocityY
            )
        }

        // 判断是否是从上往下滑。
        if (degree <= -180 + mValidDegreeOfTopToBottom || degree >= 180 - mValidDegreeOfTopToBottom) {
            handled = handled or mOnGestureListener.onFlingFromTopToBottom(
                downEvent,
                upEvent,
                velocityX,
                velocityY
            )
        }

        // 判断是否是从下往上滑。
        if (degree <= mValidDegreeOfBottomToTop && degree >= -mValidDegreeOfBottomToTop) {
            handled = handled or mOnGestureListener.onFlingFromBottomToTop(
                downEvent,
                upEvent,
                velocityX,
                velocityY
            )
        }
        return handled
    }

    /**
     * 计算出两点之间的夹角。
     *
     * <pre>
     * 0°
     * |
     * 4    |    1
     * |
     * |
     * -90° ————————o———————— 90°
     * |
     * 3    |    2
     * |
     * |
     * ±180°
    </pre> *
     *
     * @param downX 下压的x轴坐标
     * @param downY 下压的y轴坐标
     * @param upX   抬起的x轴坐标
     * @param upY   抬起的y轴坐标
     * @return 连点之间的夹角大小
     */
    private fun calculateDegree(downX: Float, downY: Float, upX: Float, upY: Float): Float {
        val translateX = upX - downX
        val translateY = upY - downY
        val angle =
            (Math.asin(translateX / Math.sqrt(translateX * translateX + translateY * translateY.toDouble())) * 180
                    / Math.PI).toFloat()
        var degree = 0.0f
        if (!java.lang.Float.isNaN(angle)) {
            // 第一象限
            if (translateX >= 0 && translateY <= 0) {
                degree = angle
                // 第二象限
            } else if (translateX <= 0 && translateY <= 0) {
                degree = angle
                // 第三象限
            } else if (translateX <= 0 && translateY >= 0) {
                degree = -180 - angle
                // 第四象限
            } else if (translateX >= 0 && translateY >= 0) {
                degree = 180 - angle
            }
        }
        return degree
    }

    /**
     * 判断两次点击是否是双击操作的核心方法。
     *
     * @param firstDown  第一次点击时按下手指的事件
     * @param firstUp    第一次点击时松开手指的事件
     * @param secondDown 第二次点击时按下手指的事件
     * @return true是双击事件。
     */
    private fun isConsideredDoubleTap(
        firstDown: MotionEvent,
        firstUp: MotionEvent,
        secondDown: MotionEvent
    ): Boolean {
        if (!mAlwaysInBiggerTapRegion) {
            return false
        }

        // 计算第二次点击的时间点和上一次手指抬起时的时间点的差值，如果差值超过双击的
        // 最大时间差或小于双击的最小时间差，就判定本次操作不是双击操作。查看源码可以
        // 知道判定是否是双击操作的最小时间间隔为40毫秒，最大时间间隔为300毫秒。
        val deltaTime = secondDown.eventTime - firstUp.eventTime
        if (deltaTime > DOUBLE_TAP_TIMEOUT || deltaTime < DOUBLE_TAP_MIN_TIME) {
            return false
        }

        // 计算双击时两次点击的坐标点距离，当两次点击的位置差距太大的时候也不算双击。
        val deltaX = firstDown.x.toInt() - secondDown.x.toInt()
        val deltaY = firstDown.y.toInt() - secondDown.y.toInt()
        return deltaX * deltaX + deltaY * deltaY < mDoubleTapSlopSquare
    }

    /**
     * 取消所有单指点击事件。
     */
    private fun cancelTaps() {
        // 取消手指按压状态消息。
        mHandler.removeMessages(MSG_SHOW_PRESS)

        // 取消长按消息。
        mHandler.removeMessages(MSG_LONG_PRESS)

        // 取消单击消息。
        mHandler.removeMessages(MSG_TAP)
        mAlwaysInTapRegion = false
        mAlwaysInBiggerTapRegion = false
        mDeferConfirmSingleTap = false
        if (mInLongPress) {
            mInLongPress = false
        }
    }

    /**
     * 获取当前的焦点的x轴坐标，单指操作时手指的位置既是焦点，多指操作时我们求坐标轴的平均值。
     *
     * @return 焦点的x轴坐标
     */
    val focusX: Float
        get() = mCurrentFocus.x

    /**
     * 获取当前的焦点的y轴坐标，单指操作时手指的位置既是焦点，多指操作时我们求坐标轴的平均值。
     *
     * @return 焦点的y轴坐标
     */
    val focusY: Float
        get() = mCurrentFocus.y


    /**
     * 设置判定手指为从左向右滑动的有效角度范围。
     *
     * @param validDegreeOfLeftToRight 角度范围
     */
    fun setValidDegreeOfLeftToRight(validDegreeOfLeftToRight: Float) {
        mValidDegreeOfLeftToRight = validDegreeOfLeftToRight
    }

    /**
     * 设置判定手指为从右向左滑动的有效角度范围。
     *
     * @param validDegreeOfRightToLeft 角度范围
     */
    fun setValidDegreeOfRightToLeft(validDegreeOfRightToLeft: Float) {
        mValidDegreeOfRightToLeft = validDegreeOfRightToLeft
    }

    /**
     * 设置判定手指为从上往下滑动的有效角度范围。
     *
     * @param validDegreeOfTopToBottom 角度范围
     */
    fun setValidDegreeOfTopToBottom(validDegreeOfTopToBottom: Float) {
        mValidDegreeOfTopToBottom = validDegreeOfTopToBottom
    }

    /**
     * 设置判定手指为从下往上滑动的有效角度范围。
     *
     * @param validDegreeOfBottomToTop 角度范围
     */
    fun setValidDegreeOfBottomToTop(validDegreeOfBottomToTop: Float) {
        mValidDegreeOfBottomToTop = validDegreeOfBottomToTop
    }

    /**
     * 设置是否允许多指触发Fling操作，默认不允许。
     *
     * @param isFlingEnabled true允许，false不允许
     */
    fun setMultipleFlingEnabled(isFlingEnabled: Boolean) {
        mIsMultipleFlingEnabled = isFlingEnabled
    }

    /**
     * 设置允许触发速滑手势的最小移动距离，这是相对于初始点的距离。
     *
     * @param flingSlop 最小移动距离
     */
    fun setFlingSlop(flingSlop: Float) {
        mFlingSlopSquare = if (flingSlop < 0) 0f else flingSlop * flingSlop
    }


    /**
     * 设置长按的超时时间。
     *
     * @param timeout 超时时间：timeout >= 150
     */
    fun setLongPressTimeout(timeout: Int) {
        mLongPressTimeout = if (timeout < 150) 150 else timeout
    }

    /**
     * 设置是否可以长按，如果启用长按手势，那么当用户按下手指并且保持不放的时候，你会收到一次
     * 长按事件的反馈（仅此而已）。如果禁用长按手势，那么当用户按下手指并且滑动的时候，你会收
     * 到滑动事件的反馈。默认情况下，长按功能是开启的。
     *
     * @param isLongPressEnabled 是否启用长按功能
     */
    fun setIsLongPressEnabled(isLongPressEnabled: Boolean) {
        mIsLongPressEnabled = isLongPressEnabled
    }

    /**
     * 设置move是否敏感，若敏感，actionMove时将不判断移动距离直接触发回调，add by lhy
     * @param isMoveSensitive 是否敏感
     */
    fun setMoveSensitive(isMoveSensitive: Boolean) {
        mIsMoveSensitive = isMoveSensitive
    }

    fun setFlingSensitive(isFlingSensitive: Boolean) {
        this.isFlingSensitive = isFlingSensitive
    }


    fun setAllowLongPressMove(allowLongPressMove: Boolean) {
        this.allowLongPressMove = allowLongPressMove
    }

    companion object {
        private const val DEFAULT_VALID_DEGREE_OF_LEFT_TO_RIGHT = 45f
        private const val DEFAULT_VALID_DEGREE_OF_RIGHT_TO_LEFT = 45f
        private const val DEFAULT_VALID_DEGREE_OF_TOP_TO_BOTTOM = 45f
        private const val DEFAULT_VALID_DEGREE_OF_BOTTOM_TO_TOP = 45f
        private const val DEFAULT_FLING_SLOP_SQUARE = 50f * 50f

        /**
         * 时间临界值，表示手势被判定为长按时所需要的手指在屏幕上的最小停留时间，
         * 如果停留时间小于这个阀值，则判断为点击（这个阀值不准确，分析源码的时候会解释）。
         */
        private val LONG_PRESS_TIMEOUT = ViewConfiguration.getLongPressTimeout()

        /**
         * 时间临界值，表示触发反馈手势所需要的手指在屏幕上的最小停留时间，这里需要注意的是虽然该
         * 属性取名叫TAP_TIMEOUT，但实际上它跟点击手势并没有直接关系，而跟它有直接关系的则是反
         * 馈手势，反馈手势并不是用户手指触碰到屏幕就马上触发的，而触碰屏幕一小段时间（100ms）之后触发。
         * 达到单击事件触发的临界时间
         */
        private val TAP_TIMEOUT = ViewConfiguration.getTapTimeout()

        /**
         * 时间临界值，表示手势被判定为双击时所允许的两次点击之间的最大间隔时间。
         */
        private val DOUBLE_TAP_TIMEOUT = ViewConfiguration.getDoubleTapTimeout()

        /**
         * 时间临界值，表示手势被判定为双击时所允许的两次点击之间的最小间隔时间，也就是说手速太快也不行。
         */
        private const val DOUBLE_TAP_MIN_TIME = 0

        /**
         * 用于Message.what，表示触发反馈手势的标识。
         */
        private const val MSG_SHOW_PRESS = 1

        /**
         * 用于Message.what，表示触发长按手势的标识。
         */
        private const val MSG_LONG_PRESS = 2

        /**
         * 用于Message.what，表示触发单击手势的标识。
         */
        private const val MSG_TAP = 3
    }

    init {

        // GestureDetectorPro不允许传递空的OnGestureListener。
        if (onGestureListener == null) {
            throw RuntimeException(
                "Invoke GestureDetectorPro(Context, OnGestureListener) with null OnGestureListener."
            )
        }
        mHandler = GestureHandler(this)
        mOnGestureListener = onGestureListener
        onGestureListener.setGestureDetector(this)

        // 初始状态下长按手势是可用的。
        mIsLongPressEnabled = true
        val configuration = ViewConfiguration.get(AppContext.context)
        val touchSlop: Int
        val doubleTapSlop: Int
        val doubleTapTouchSlop: Int
        touchSlop = configuration.scaledTouchSlop
        doubleTapTouchSlop = configuration.scaledTouchSlop
        doubleTapSlop = configuration.scaledDoubleTapSlop
        mMinimumFlingVelocity = configuration.scaledMinimumFlingVelocity
        mMaximumFlingVelocity = configuration.scaledMaximumFlingVelocity

        // 计算临界值的平方并赋值，当我们手指从a(x1, y1)滑动到b(x2, y2)的时候，我们获取的实际上的x轴和y轴的平移距离，
        // 而我们实际需要的是a和b之间的直线距离，所以这里利用直角三角形a² = b² + c²的原理计算出实际的距离的平方值即可，
        // 再做开根号的操作就多余了。
        mTouchSlopSquare = touchSlop * touchSlop
        mDoubleTapTouchSlopSquare = doubleTapTouchSlop * doubleTapTouchSlop
        mDoubleTapSlopSquare = doubleTapSlop * doubleTapSlop
    }

    /**
     * [GestureDetectorPro]的手势回调接口，你必须在调用[.GestureDetectorPro]
     * 方法的时候传递实现该接口的对象作为参数，否则[GestureDetectorPro]将会抛出异常信息。
     */
    interface OnGestureListener {
        /**
         * 是否启用。
         */
        var isEnable: Boolean

        /**
         * 设置检测器对象。
         */
        fun setGestureDetector(gestureDetector: BpGestureDetector)

        /**
         * 每一次单指点击到屏幕并松开时触发该回调方法。
         *
         * @param downEvent 手指按压时的事件信息
         * @param upEvent   手指抬起时的事件信息
         * @return true代表消费了该事件
         */
        fun onTap(downEvent: MotionEvent, upEvent: MotionEvent): Boolean

        /**
         * 反馈操作，用于通知用户手指的触碰已经被屏幕响应，例如高亮显示控件。
         *
         * @param downEvent 手指按压时的事件信息
         */
        fun onShowPress(downEvent: MotionEvent?)

        /**
         * 当主要手指按下的时候触发该回调方法。
         *
         * @param downEvent 按压事件信息
         * @return true代表消费了该事件
         */
        fun onMajorFingerDown(downEvent: MotionEvent): Boolean

        /**
         * 当主要手指抬起的时候触发该回调方法。
         *
         * @param upEvent 抬起事件信息
         * @return true代表消费了该事件
         */
        fun onMajorFingerUp(upEvent: MotionEvent?): Boolean

        /**
         * 次要手指按压到屏幕时触发该回调方法。
         *
         * @param downEvent 手指按压时的事件信息
         * @return true代表消费了该事件
         */
        fun onMinorFingerDown(downEvent: MotionEvent?): Boolean

        /**
         * 次要手指抬起时触发该回调方法。
         *
         * @param upEvent 手指抬起时的事件信息
         * @return true代表消费了该事件
         */
        fun onMinorFingerUp(upEvent: MotionEvent?): Boolean

        /**
         * 单击的时候触发该回调方法。
         *
         * @param downEvent 单击时手指按压的事件信息
         * @param upEvent   单击时手指抬起的事件信息
         */
        fun onSingleTap(downEvent: MotionEvent, upEvent: MotionEvent): Boolean

        /**
         * 双击的时候触发该回调方法。
         *
         * @param firstDownEvent  双击时的第一次手指按压事件信息
         * @param firstUpEvent    双击时的第一次手指抬起事件信息
         * @param secondDownEvent 双击时的第二次手指按压事件信息
         * @return true代表消费了该事件
         */
        fun onDoubleTap(
            firstDownEvent: MotionEvent?,
            firstUpEvent: MotionEvent?,
            secondDownEvent: MotionEvent?
        ): Boolean

        /**
         * 长按的时候触发该回调方法。
         *
         * @param downEvent 长按时的手指按压事件信息
         */
        fun onLongPress(downEvent: MotionEvent): Boolean

        /**
         * 长按后抬起手指触发该方法。
         *
         * @param upEvent 抬起手指的事件信息
         * @return true代表消费了该事件
         */
        fun onLongPressUp(upEvent: MotionEvent): Boolean

        /**
         * 手指在屏幕上滑动的时候触发该回调方法。
         *
         * @param downEvent 主手指按压的事件信息
         * @param moveEvent 每一次滑动时的事件信息
         * @param distanceX X轴上的滑动距离，这是相对于上一次焦点位置的递增值，不是和初始位置的距离
         * @param distanceY Y轴上的滑动距离，这是相对于上一次焦点位置的递增值，不是和初始位置的距离
         * @return true代表消费了该事件
         */
        fun onScroll(
            downEvent: MotionEvent?,
            moveEvent: MotionEvent?,
            distanceX: Float,
            distanceY: Float
        ): Boolean

        /**
         * 当只有主手指在屏幕上滑动的时候触发该回调方法。
         *
         * @param downEvent 主手指按压的事件信息
         * @param moveEvent 每一次滑动时的事件信息
         * @param distanceX X轴上的滑动距离，这是相对于上一次焦点位置的递增值，不是和初始位置的距离
         * @param distanceY Y轴上的滑动距离，这是相对于上一次焦点位置的递增值，不是和初始位置的距离
         * @return true代表消费了该事件
         */
        fun onMajorScroll(
            downEvent: MotionEvent,
            moveEvent: MotionEvent,
            distanceX: Float,
            distanceY: Float
        ): Boolean

        /**
         * 手指快速滑过屏幕的时候触发该回调方法。
         *
         * @param downEvent 第一次手指按压的事件信息
         * @param upEvent   最后一次手指抬起时的事件信息
         * @param velocityX X轴的滑动速度（px/second）
         * @param velocityY Y轴的滑动速度（px/second）
         * @return true代表消费了该事件
         */
        fun onFling(
            downEvent: MotionEvent?,
            upEvent: MotionEvent?,
            velocityX: Float,
            velocityY: Float
        ): Boolean

        /**
         * 手指在屏幕上从左向右速滑的时候触发该回调方法。
         *
         * @param downEvent 第一次手指按压的事件信息
         * @param upEvent   最后一次手指抬起时的事件信息
         * @param velocityX X轴的滑动速度（px/second）
         * @param velocityY Y轴的滑动速度（px/second）
         * @return true代表消费了该事件
         */
        fun onFlingFromLeftToRight(
            downEvent: MotionEvent?,
            upEvent: MotionEvent?,
            velocityX: Float,
            velocityY: Float
        ): Boolean

        /**
         * 手指在屏幕上从右向左速滑的时候触发该回调方法。
         *
         * @param downEvent 第一次手指按压的事件信息
         * @param upEvent   最后一次手指抬起时的事件信息
         * @param velocityX X轴的滑动速度（px/second）
         * @param velocityY Y轴的滑动速度（px/second）
         * @return true代表消费了该事件
         */
        fun onFlingFromRightToLeft(
            downEvent: MotionEvent?,
            upEvent: MotionEvent?,
            velocityX: Float,
            velocityY: Float
        ): Boolean

        /**
         * 手指在屏幕上从上向下速滑的时候触发该回调方法。
         *
         * @param downEvent 第一次手指按压的事件信息
         * @param upEvent   最后一次手指抬起时的事件信息
         * @param velocityX X轴的滑动速度（px/second）
         * @param velocityY Y轴的滑动速度（px/second）
         * @return true代表消费了该事件
         */
        fun onFlingFromTopToBottom(
            downEvent: MotionEvent?,
            upEvent: MotionEvent?,
            velocityX: Float,
            velocityY: Float
        ): Boolean

        /**
         * 手指在屏幕上从下向上速滑的时候触发该回调方法。
         *
         * @param downEvent 第一次手指按压的事件信息
         * @param upEvent   最后一次手指抬起时的事件信息
         * @param velocityX X轴的滑动速度（px/second）
         * @param velocityY Y轴的滑动速度（px/second）
         * @return true代表消费了该事件
         */
        fun onFlingFromBottomToTop(
            downEvent: MotionEvent?,
            upEvent: MotionEvent?,
            velocityX: Float,
            velocityY: Float
        ): Boolean

        /**
         * 双指手势触发时回调
         * @param scale 与上次事件相比scale的变化。
         * @param angle 与上次事件相比角度的变化。
         */
        fun onDoubleGestureStart(
            firstDownEvent: PointF,
            secondDownEvent: PointF,
            focus: PointF
        ): Boolean

        /**
         * 双指手势触发时回调
         * @param scale 与上次事件相比scale的变化。
         * @param angle 与上次事件相比角度的变化。
         */
        fun onDoubleGestureMove(scale: Float, angle: Float, focus: PointF): Boolean

        /**
         * 手势结束回调。
         */
        fun onGestureEnd(touchEvent: MotionEvent): Boolean
    }

    /**
     * 这是一个让你能够只实现部分手势监听的快捷类，它简单实现了[OnGestureListener]的所有方法，
     * 这些方法没有做任何操作并且返回false，你可以通过重写的方式只监听你想要的手势操作。
     */
    open class SimpleOnGestureListener : OnGestureListener {

        open lateinit var detector: BpGestureDetector

        override var isEnable = true
            set(value) {
                field = value
            }

        override fun setGestureDetector(gestureDetector: BpGestureDetector) {
            detector = gestureDetector
        }

        override fun onTap(downEvent: MotionEvent, upEvent: MotionEvent): Boolean {
            return false
        }

        override fun onShowPress(downEvent: MotionEvent?) {}
        override fun onMajorFingerDown(downEvent: MotionEvent): Boolean {
            return false
        }

        override fun onMajorFingerUp(upEvent: MotionEvent?): Boolean {
            return false
        }

        override fun onMinorFingerDown(downEvent: MotionEvent?): Boolean {
            return false
        }

        override fun onMinorFingerUp(upEvent: MotionEvent?): Boolean {
            return false
        }

        override fun onSingleTap(downEvent: MotionEvent, upEvent: MotionEvent): Boolean {
            return false
        }

        override fun onDoubleTap(
            firstDownEvent: MotionEvent?,
            firstUpEvent: MotionEvent?,
            secondDownEvent: MotionEvent?
        ): Boolean {
            return false
        }

        override fun onLongPress(downEvent: MotionEvent): Boolean {
            return false
        }

        override fun onLongPressUp(upEvent: MotionEvent): Boolean {
            return false
        }

        override fun onScroll(
            downEvent: MotionEvent?,
            moveEvent: MotionEvent?,
            distanceX: Float,
            distanceY: Float
        ): Boolean {
            return false
        }

        override fun onMajorScroll(
            downEvent: MotionEvent,
            moveEvent: MotionEvent,
            distanceX: Float,
            distanceY: Float
        ): Boolean {
            return false
        }

        override fun onFling(
            downEvent: MotionEvent?,
            upEvent: MotionEvent?,
            velocityX: Float,
            velocityY: Float
        ): Boolean {
            return false
        }

        override fun onDoubleGestureMove(scale: Float, angle: Float, focus: PointF): Boolean {
            return false
        }

        /**
         * 手势结束回调。
         */
        override fun onGestureEnd(touchEvent: MotionEvent): Boolean {
            return false
        }

        override fun onFlingFromLeftToRight(
            downEvent: MotionEvent?, upEvent: MotionEvent?, velocityX: Float,
            velocityY: Float
        ): Boolean {
            return false
        }

        override fun onFlingFromRightToLeft(
            downEvent: MotionEvent?, upEvent: MotionEvent?, velocityX: Float,
            velocityY: Float
        ): Boolean {
            return false
        }

        override fun onFlingFromTopToBottom(
            downEvent: MotionEvent?, upEvent: MotionEvent?, velocityX: Float,
            velocityY: Float
        ): Boolean {
            return false
        }

        override fun onFlingFromBottomToTop(
            downEvent: MotionEvent?, upEvent: MotionEvent?, velocityX: Float,
            velocityY: Float
        ): Boolean {
            return false
        }

        override fun onDoubleGestureStart(
            firstDownEvent: PointF,
            secondDownEvent: PointF,
            focus: PointF
        ): Boolean {
            return false
        }
    }

    /**
     * 嵌套的手势监听，将手势分发到所有内嵌的监听中，在实现手势统一分发，自上到下统一管理的目的。
     */
    open class NestOnGestureListener : SimpleOnGestureListener() {

        /**
         * 内部的手势监听队列，其包含的意义是让外部监听器先处理手势，如果外部不处理，再返回给内部处理。
         */
        val nestGestureListener = LinkedList<OnGestureListener>()

        /**
         * 添加一个嵌套监听器。
         */
        fun addNestGestureListener(gestureListener: OnGestureListener) {
            gestureListener.setGestureDetector(detector)
            // nestGestureListener 自动化跑出来异步线程操作数组导致线程安全问题
            UIHelper.runOnUiThread { nestGestureListener.add(0, gestureListener) }
        }

        /**
         * 移除一个嵌套监听器。
         */
        fun removeGestureListener(gestureListener: OnGestureListener) {
            // nestGestureListener 自动化跑出来异步线程操作数组导致线程安全问题
            UIHelper.runOnUiThread { nestGestureListener.remove(gestureListener) }
        }

        override fun onTap(downEvent: MotionEvent, upEvent: MotionEvent): Boolean {
            var nestHandle = false
            nestGestureListener.forEach {
                nestHandle = it.isEnable && it.onTap(downEvent, upEvent)
                if (nestHandle) {
                    return true
                }
            }
            return nestHandle
        }

        override fun onShowPress(downEvent: MotionEvent?) {}

        override fun onMajorFingerDown(downEvent: MotionEvent): Boolean {
            var nestHandle = false
            nestGestureListener.forEach {
                nestHandle = it.isEnable && it.onMajorFingerDown(downEvent)
                if (nestHandle) {
                    return true
                }
            }
            return nestHandle
        }

        override fun onMajorFingerUp(upEvent: MotionEvent?): Boolean {
            var nestHandle = false
            nestGestureListener.forEach {
                nestHandle = it.isEnable && it.onMajorFingerUp(upEvent)
                if (nestHandle) {
                    return true
                }
            }
            return nestHandle
        }

        override fun onMinorFingerDown(downEvent: MotionEvent?): Boolean {
            var nestHandle = false
            nestGestureListener.forEach {
                nestHandle = it.isEnable && it.onMinorFingerDown(downEvent)
                if (nestHandle) {
                    return true
                }
            }
            return nestHandle
        }

        override fun onMinorFingerUp(upEvent: MotionEvent?): Boolean {
            var nestHandle = false
            nestGestureListener.forEach {
                nestHandle = it.isEnable && it.onMinorFingerUp(upEvent)
                if (nestHandle) {
                    return true
                }
            }
            return nestHandle
        }

        override fun onSingleTap(downEvent: MotionEvent, upEvent: MotionEvent): Boolean {
            var nestHandle = false
            nestGestureListener.forEach {
                nestHandle = it.isEnable && it.onSingleTap(downEvent, upEvent)
                if (nestHandle) {
                    return true
                }
            }
            return nestHandle
        }

        override fun onDoubleTap(
            firstDownEvent: MotionEvent?,
            firstUpEvent: MotionEvent?,
            secondDownEvent: MotionEvent?
        ): Boolean {
            var nestHandle = false
            nestGestureListener.forEach {
                nestHandle =
                    it.isEnable && it.onDoubleTap(firstDownEvent, firstUpEvent, secondDownEvent)
                if (nestHandle) {
                    return true
                }
            }
            return nestHandle
        }

        override fun onLongPress(downEvent: MotionEvent): Boolean {
            var nestHandle = false
            nestGestureListener.forEach {
                nestHandle = it.isEnable && it.onLongPress(downEvent)
                if (nestHandle) {
                    return true
                }
            }
            return nestHandle
        }

        override fun onLongPressUp(upEvent: MotionEvent): Boolean {
            var nestHandle = false
            nestGestureListener.forEach {
                nestHandle = it.isEnable && it.onLongPressUp(upEvent)
                if (nestHandle) {
                    return true
                }
            }
            return nestHandle
        }

        override fun onScroll(
            downEvent: MotionEvent?,
            moveEvent: MotionEvent?,
            distanceX: Float,
            distanceY: Float
        ): Boolean {
            var nestHandle = false
            nestGestureListener.forEach {
                nestHandle = it.isEnable && it.onScroll(downEvent, moveEvent, distanceX, distanceY)
                if (nestHandle) {
                    return true
                }
            }
            return nestHandle
        }

        override fun onMajorScroll(
            downEvent: MotionEvent,
            moveEvent: MotionEvent,
            distanceX: Float,
            distanceY: Float
        ): Boolean {
            var nestHandle = false
            nestGestureListener.forEach {
                nestHandle =
                    it.isEnable && it.onMajorScroll(downEvent, moveEvent, distanceX, distanceY)
                if (nestHandle) {
                    return true
                }
            }
            return nestHandle
        }

        override fun onFling(
            downEvent: MotionEvent?,
            upEvent: MotionEvent?,
            velocityX: Float,
            velocityY: Float
        ): Boolean {
            var nestHandle = false
            nestGestureListener.forEach {
                nestHandle = it.isEnable && it.onFling(downEvent, upEvent, velocityX, velocityY)
                if (nestHandle) {
                    return true
                }
            }
            return nestHandle
        }

        override fun onDoubleGestureMove(scale: Float, angle: Float, focus: PointF): Boolean {
            var nestHandle = false
            nestGestureListener.forEach {
                nestHandle = it.isEnable && it.onDoubleGestureMove(scale, angle, focus)
                if (nestHandle) {
                    return true
                }
            }
            return nestHandle
        }

        override fun onFlingFromLeftToRight(
            downEvent: MotionEvent?, upEvent: MotionEvent?, distanceX: Float,
            distanceY: Float
        ): Boolean {
            var nestHandle = false
            nestGestureListener.forEach {
                nestHandle = it.isEnable && it.onFlingFromLeftToRight(
                    downEvent,
                    upEvent,
                    distanceX,
                    distanceY
                )
                if (nestHandle) {
                    return true
                }
            }
            return nestHandle
        }

        override fun onFlingFromRightToLeft(
            downEvent: MotionEvent?, upEvent: MotionEvent?, distanceX: Float,
            distanceY: Float
        ): Boolean {
            var nestHandle = false
            nestGestureListener.forEach {
                nestHandle = it.isEnable && it.onFlingFromRightToLeft(
                    downEvent,
                    upEvent,
                    distanceX,
                    distanceY
                )
                if (nestHandle) {
                    return true
                }
            }
            return nestHandle
        }

        override fun onFlingFromTopToBottom(
            downEvent: MotionEvent?, upEvent: MotionEvent?, distanceX: Float,
            distanceY: Float
        ): Boolean {
            var nestHandle = false
            nestGestureListener.forEach {
                nestHandle = it.isEnable && it.onFlingFromTopToBottom(
                    downEvent,
                    upEvent,
                    distanceX,
                    distanceY
                )
                if (nestHandle) {
                    return true
                }
            }
            return nestHandle
        }

        override fun onFlingFromBottomToTop(
            downEvent: MotionEvent?, upEvent: MotionEvent?, distanceX: Float,
            distanceY: Float
        ): Boolean {
            var nestHandle = false
            nestGestureListener.forEach lit@{
                nestHandle = it.isEnable && it.onFlingFromBottomToTop(
                    downEvent,
                    upEvent,
                    distanceX,
                    distanceY
                )
                if (nestHandle) {
                    return@lit
                }
            }
            return nestHandle
        }

        override fun onDoubleGestureStart(
            firstDownEvent: PointF,
            secondDownEvent: PointF,
            focus: PointF
        ): Boolean {
            var nestHandle = false
            nestGestureListener.forEach lit@{
                nestHandle =
                    it.isEnable && it.onDoubleGestureStart(firstDownEvent, secondDownEvent, focus)
                if (nestHandle) {
                    return@lit
                }
            }
            return nestHandle
        }

        override fun onGestureEnd(touchEvent: MotionEvent): Boolean {
            var nestHandle = false
            nestGestureListener.forEach lit@{
                nestHandle = it.isEnable && it.onGestureEnd(touchEvent)
                if (nestHandle) {
                    return true
                }
            }
            return nestHandle
        }
    }

}