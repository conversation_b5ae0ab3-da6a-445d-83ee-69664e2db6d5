package com.commsource.studio.layer

import android.content.Context
import android.graphics.*
import android.view.View
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelStoreOwner
import com.commsource.beautyplus.R
import com.commsource.studio.MagnifyComponent
import com.commsource.studio.MatrixBox
import com.commsource.studio.component.PaintSelectComponent
import com.commsource.studio.onDrawListener
import com.commsource.util.ResourcesUtils
import com.meitu.common.animutil.buildAnimSet
import com.meitu.library.util.device.DeviceUtils

/**
 * @Description:
 *
 * @Author: vinvince, @Time: 2022/8/29 15:26
 */
class RealTimeLiquifyLayer(context: Context) : BaseScrollLayer(context), onDrawListener,
    BaseScrollLayer.ScrollListener {

    /**
     * 绘制圆形画笔。
     */
    private val penLayerDrawable = PenLayerDrawable(this).apply {
        strokeWidth = DeviceUtils.dip2fpx(2f)
        isEnable = false
    }

    /**
     * 绘制Mask的Paint。
     */
    private val bitmapPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        alpha = 0
    }

    private var paintXfermode = PorterDuffXfermode(PorterDuff.Mode.DST_IN)

    /**
     * Mask绘制的Alpha
     */
    private var maskAlpha = 1f
        set(value) {
            field = value
            layerView.postInvalidate()
        }

    /**
     *  用作动画的Mask
     */
    private var showMaskAnim: Boolean = false

    // 画mask区域
    private val bitmapRect = Rect()
    private val viewportRect = Rect()

    /**
     * 从手动模式获取背景保护的纹理图片
     */
    var fetchMaskBitmap: (() -> Bitmap?)? = null

    private val canvasGestureMatrixBox = MatrixBox()

    private var skipCanvasLimit = false

    init {
        addScrollListener(this, 0)
    }

    override fun onInitOwner(storeOwner: ViewModelStoreOwner, lifecycleOwner: LifecycleOwner) {
        super.onInitOwner(storeOwner, lifecycleOwner)
        // 监听画笔。
        getViewModel(PaintSelectComponent.PaintSelectViewModel::class.java).apply {
            paintSizeChangeEvent.observe(lifecycleOwner, Observer {
                penLayerDrawable.radius = it
                skipCanvasLimit = true
                layerView.invalidate()
            })
            paintStartSelectEvent.observe(lifecycleOwner, Observer {
                penLayerDrawable.isEnable = it
                showPenInCenter(it)
            })
        }

        // 设置放大镜。
        getViewModel(MagnifyComponent.MagnifyViewModel::class.java).addOnDrawListener(this)
    }

    override fun onDrawCanvas(canvas: Canvas) {
        penLayerDrawable.onDraw(canvas)
    }

    override fun onCreateView(): View {
        return DrawPathView(context)
    }

    inner class DrawPathView(context: Context) : View(context) {
        override fun onDraw(canvas: Canvas) {
            canvas.save()
            canvas.concat(canvasGestureMatrixBox.matrix)
            canvas.translate(viewPortLeft, viewPortTop)
            if (!skipCanvasLimit) {
                canvas.clipRect(0f, 0f, viewPortWidth, viewPortHeight)
            }
            skipCanvasLimit = false

            // 显示动画
            if (showMaskAnim) {
                bitmapPaint.alpha = (128 * maskAlpha).toInt()
                fetchMaskBitmap?.invoke()?.let {
                    bitmapPaint.xfermode = null
                    canvas.drawColor(ResourcesUtils.getColor(R.color.color_fb5986))
                    bitmapRect.set(0, 0, it.width, it.height)
                    viewportRect.set(0, 0, viewPortWidth.toInt(), viewPortHeight.toInt())
                    bitmapPaint.xfermode = paintXfermode
                    canvas.drawBitmap(it, bitmapRect, viewportRect, bitmapPaint)
                }
            } else {
                onDrawCanvas(canvas)
            }
            canvas.restore()
        }
    }

    override fun onCanvasGestureMatrixChange(matrixBox: MatrixBox) {
        canvasGestureMatrixBox.set(matrixBox)
        layerView.invalidate()
    }

    fun showPenInCenter(show: Boolean) {
        penLayerDrawable.isEnable = show
        floatArrayOf(viewPortRectF.centerX(), viewPortRectF.centerY()).apply {
            mapPointContainerToViewPort(this)
            penLayerDrawable.drawPoint.set(this[0], this[1])
            //记录此点。
            layerView.invalidate()
        }
    }

    /**
     * 短暂的展示一下Mask.
     */
    fun showMaskBriefly(enAction: (() -> Unit)? = null) {
        showMaskAnim = true
        maskAlpha = 1.0f
        buildAnimSet {
            valueAnim {
                values = floatArrayOf(0.0f, 1.0f)
                duration = 300
                onUpdateFloat = {
                    maskAlpha = it
                    layerView.invalidate()
                }
            } next valueAnim {
                values = floatArrayOf(1.0f, 0.0f)
                startDelay = 200
                duration = 300
                onUpdateFloat = {
                    maskAlpha = it
                    layerView.invalidate()
                }
            }
            // 动画结束
            onEnd = {
                showMaskAnim = false
                enAction?.invoke()
            }
        }.start()
    }

}