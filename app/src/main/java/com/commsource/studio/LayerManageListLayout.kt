package com.commsource.studio

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Point
import android.graphics.Rect
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.lifecycle.ViewModelProvider
import com.commsource.album.LOGV_Album
import com.commsource.beautyplus.BaseActivity
import com.commsource.studio.bean.BaseLayerInfo
import com.commsource.studio.bean.FocusLayerInfo
import com.commsource.studio.bean.GroupLayerInfo
import com.commsource.studio.gesture.GroupContainerViewHolder
import com.commsource.studio.gesture.LayerManageViewHolder
import com.commsource.studio.gesture.LayerThumbnailViewHolder
import com.commsource.studio.gesture.NormalContainerViewHolder
import com.commsource.util.*
import com.commsource.util.common.MathUtil
import com.commsource.widget.recyclerview.BaseItem
import com.meitu.library.util.Debug.Debug
import com.meitu.library.util.device.DeviceUtils
import kotlin.math.abs
import kotlin.math.roundToInt

/**
 * 图层管理列表布局，
 */
class LayerManageListLayout : FrameLayout {
    companion object {

        /**
         * 拖拽距离限制。
         */
        val DRAG_OUT_DISTANCE = 88.dpf()

        /**
         * Item的高度。
         */
        val ITEM_HEIGHT = 64.dp()

        /**
         * Item的宽度。
         */
        val ITEM_WIDTH = 88.dp()

        /**
         * 编组正常高度。
         */
        val GROUP_NORMAL_HEIGHT = 30.dp()

        /**
         * 编组拖拽。
         */
        val GROUP_DRAG_HEIGHT = 94.dp()

        /**
         * 动画时长。
         */
        val ANIMATE_TIME = 250L
    }

    /**
     * 滚动的Offset。
     */
    var scrollLength = 0

    /**
     * 最大滚动距离。
     */
    val maxScrollLength: Int
        get() {
            return (totalLength - height).coerceAtLeast(0)
        }

    /**
     * 布局的offset。
     */
    var layoutOffset = 0

    /**
     * 整体长度。
     */
    var totalLength = 0

    /**
     * 滚动动画。
     */
    private var flingAnimator = FlingAnimator().apply {
        listener = {
            val scrollOffset = it - scrollLength
            currentDragLayerInfoItem?.onDrag(0, scrollOffset)
            scrollLength = it
            layoutChildren()
        }
    }

    /**
     * 列表中图层Item容器的位置。
     */
    var containerItemFrames = HashMap<String, ItemFrame>()

    /**
     * 列表中容器内容的位置，这个位置相对的是[containerItemFrames]中的位置。
     */
    var thumbnailItemFrames = HashMap<String, ItemFrame>()

    /**
     * 用于布局的数据源。
     */
    var layoutInfoList = ArrayList<BaseLayerInfo>()

    /**
     * 外界传入的数据源。
     */
    var dataInfoList: List<BaseLayerInfo>? = null

    /**
     * 当前拖拽的LayoutInfo。
     */
    var currentDragLayerInfoItem: DragLayerInfoItem? = null

    /**
     * 多选模式中，多选的临时组，这个值不为空时，代表此时为多选。
     */
    var multiSelectGroupLayerInfo: GroupLayerInfo? = null

    /**
     * 图层信息顺序改变监听，在改变顺序时会回调给外面。
     */
    var layerInfosChangeListener: ((layerInfoList: List<BaseLayerInfo>) -> Unit)? = null

    /**
     * 图层删除事件。
     */
    var layerInfoDeleteListener: ((layerInfo: BaseLayerInfo) -> Unit)? = null

    /**
     * 图层点击回调。
     */
    var layerInfoClickListener: ((layerInfo: BaseLayerInfo) -> Unit)? = null

    var dragLayerListener: DragLayerListener? = null

    var onLayoutChildListener: OnLayoutChildListener? = null

    var layerVisibleHeight: (() -> Int)? = null

    /**
     * 当前的焦点图层。
     */
    var currentFocusLayerInfo: BaseLayerInfo? = null


    lateinit var itemContainer:FrameLayout
    /**
     * 主界面的VM。
     */
    val studioViewModel by lazy {
        ViewModelProvider(context as BaseActivity).get(
            ImageStudioViewModel::class.java
        )
    }

    init {
        setWillNotDraw(false)
    }

    private var bpGestureDetector = BpGestureDetector(GestureListener())


    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)


    /**
     * 将LayerInfo平铺开来。
     */
    private fun flatLayerInfoList(data: List<BaseLayerInfo>?): List<BaseLayerInfo> {
        return ArrayList<BaseLayerInfo>().apply {
            data?.reverseForEach {
                this.add(it)
                // 组内的元素平铺开。
                if (it is GroupLayerInfo) {
                    it.subLayerInfos.reverseForEach { pair ->
                        this.add(pair.first)
                    }
                }
            }
        }
    }

    /**
     * 数据源刷新。
     */
    fun refreshData(data: List<BaseLayerInfo>) {
        "图层数量 ${data.size}".LOGV_Album()
        dataInfoList = data
        layoutInfoList.clear()
        layoutInfoList.addAll(flatLayerInfoList(data))

        val containerFrames = HashMap<String, ItemFrame>()
        val thumbnailFrames = HashMap<String, ItemFrame>()
        layoutInfoList.forEachIndexed { index, layerInfo ->
            // 复用或者创建容器的ItemFrame.
            (containerItemFrames.remove(layerInfo.parentKey) ?: createContainer(layerInfo)).run {
                viewHolder.onBindViewHolder(layoutInfoList.size - 1 - index, BaseItem(layerInfo), null)
                containerFrames[layerInfo.parentKey] = this
                // 复用或者创建缩略图的ItemFrame.
                (thumbnailItemFrames.remove(layerInfo.parentKey)
                        ?: createLayerItemView(layerInfo, attachView))?.run {
                    viewHolder.onBindViewHolder(layoutInfoList.size - 1 - index, BaseItem(layerInfo), null)
                    thumbnailFrames[layerInfo.parentKey] = this
                }
            }
        }
        setSelected(currentFocusLayerInfo)
        // 移除不需要的。
        containerItemFrames.entries.forEach {
            removeView(it.value.attachView)
        }
//        thumbnailItemFrames.entries.forEach {
//            removeView(it.value.attachView)
//        }
        thumbnailItemFrames.clear()
        containerItemFrames = containerFrames
        thumbnailItemFrames = thumbnailFrames
        // 长度改变重新计算。
        totalLength = calculateTotalLength()
        layoutOffset = ((height - totalLength) / 2).coerceAtLeast(0)
        resetChildrenLayout()
        // 重置滚动位置。
        resetScrollLength(animate = false)
        layoutChildren()
    }


    /**
     * 中心滚动到到LayerInfo.
     */
    fun centerScrollTo(layerInfo: BaseLayerInfo?, animate: Boolean = true) {
        if (layerInfo == null) {
            return
        }
        containerItemFrames[layerInfo.parentKey]?.let {
            val targetScrollLength = MathUtil.clamp(it.layoutRect.centerY() - height / 2, 0, maxScrollLength)
            flingAnimator.cancel()
            if (animate) {
                flingAnimator.scrollTo(scrollLength, targetScrollLength)
            } else {
                scrollLength = targetScrollLength
                layoutChildren()
            }
        }
    }

    /**
     * 更新选中态。
     */
    fun setSelected(layerInfo: BaseLayerInfo?) {
        currentFocusLayerInfo = layerInfo
        containerItemFrames.entries.forEach {
            val shouldSelected = if (layerInfo is GroupLayerInfo) {
                layerInfo.containsByKey(it.value.viewHolder.layerInfo) || layerInfo.parentKey == it.value.viewHolder.layerInfo?.parentKey
                        || layerInfo.contains(it.value.viewHolder.layerInfo) || layerInfo == it.value.viewHolder.layerInfo
            } else {
                layerInfo?.parentKey == it.value.viewHolder.layerInfo?.parentKey || layerInfo == it.value.viewHolder.layerInfo
            }
            if (it.value.viewHolder.isSelected != shouldSelected) {
                it.value.viewHolder.onSelectStateChange(shouldSelected)
            }
        }
    }

    /**
     * 多选状态更新时调用。
     */
    fun onMultiSelectLayerUpdate(groupLayerInfo: GroupLayerInfo?) {
        multiSelectGroupLayerInfo = groupLayerInfo
        setSelected(groupLayerInfo)
    }

    /**
     * 将[layoutInfoList]重新转换成数据源，对比[dataInfoList]是否一致，如果不一致，通知外界。
     */
    fun notifyLayerInfoChangeIfNeed() {
        if (layoutInfoList == flatLayerInfoList(dataInfoList)) {
            // 如果前后数据没有发生变化，这边不做处理。
            return
        }
        val newDataInfoList = ArrayList<BaseLayerInfo>()
        layoutInfoList.forEachIndexed { index, layerInfo ->
            if (layerInfo is FocusLayerInfo && layerInfo.groupLayerInfo != null) {
                // 如果是组内图层在下面遍历。
                return@forEachIndexed
            }
            newDataInfoList.add(0, layerInfo)
            if (layerInfo is GroupLayerInfo) {
                // 如果是图层组需要遍历接下来的图层。
                val newSubInfos = ArrayList<Pair<FocusLayerInfo, MatrixBox>>()
                var subIndex = index + 1
                var subLayerInfo = layoutInfoList.safeGet(subIndex) as? FocusLayerInfo
                while (subLayerInfo?.groupLayerInfo == layerInfo) {
                    // 组内图层从原本[GroupLayerInfo]中的数据找到对应的Pair。
                    layerInfo.getLayerInfoPair(subLayerInfo)?.let { newSubInfos.add(0, it) }
                    subLayerInfo   = layoutInfoList.safeGet(++subIndex) as? FocusLayerInfo
                }
                // 最后赋值
                layerInfo.subLayerInfos = newSubInfos
            }
        }
        layerInfosChangeListener?.invoke(newDataInfoList)

    }


    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        layoutOffset = ((height - totalLength) / 2).coerceAtLeast(0)
        resetChildrenLayout()
        resetScrollLength(animate = false)
        layoutChildren()
    }

    private fun createContainer(layerInfo: BaseLayerInfo): ItemFrame {
        if (layerInfo is GroupLayerInfo) {
            return ItemFrame(GroupContainerViewHolder(context, this), ITEM_WIDTH, GROUP_NORMAL_HEIGHT).apply {
                addView(attachView, ITEM_WIDTH, GROUP_NORMAL_HEIGHT)
                layoutDelegate = {
                    attachView.translationX = (currentRect.left).toFloat()
                    attachView.translationY = (currentRect.top - scrollLength).toFloat()

                    onLayoutChildListener?.onLayoutChild()
                }
            }
        } else {
            return ItemFrame(NormalContainerViewHolder(context, this), ITEM_WIDTH, ITEM_HEIGHT).apply {
                addView(attachView, ITEM_WIDTH, ITEM_HEIGHT)
                layoutDelegate = {
                    attachView.translationX = (currentRect.left).toFloat()
                    attachView.translationY = (currentRect.top - scrollLength).toFloat()
                    // thumbnailItemFrames[layerInfo.parentKey]?.layoutDelegate?.invoke()

                    onLayoutChildListener?.onLayoutChild()
                }
            }
        }
    }

    private fun createLayerItemView(layerInfo: BaseLayerInfo, container: ViewGroup): ItemFrame? {
        return if (layerInfo is GroupLayerInfo) {
            null
        } else {
            ItemFrame(LayerThumbnailViewHolder(context, this), 48.dp(), 48.dp()).apply {
                container.addView(attachView)
                attachView.elevation = 1.dpf()
                layoutDelegate = {
                    attachView.translationX = (currentRect.left).toFloat()
                    attachView.translationY = (currentRect.top).toFloat()
                }
                if (RTLTool.isLayoutRtl()) {
                    set((-8).dp(), 8.dp())
                } else {
                    set(8.dp(), 8.dp())
                }
            }
        }
    }

    private fun removeItemFrames(layerInfo: BaseLayerInfo) {
        containerItemFrames[layerInfo.parentKey]?.run {
            removeView(attachView)
            containerItemFrames.remove(layerInfo.parentKey)
        }
        thumbnailItemFrames[layerInfo.parentKey]?.run {
            removeView(attachView)
            thumbnailItemFrames.remove(layerInfo.parentKey)
        }
    }

    /**
     * 重置所有子图层的Layout
     */
    private fun resetChildrenLayout(offset: Int = layoutOffset, animate: Boolean = false) {
        var startY = offset
        layoutInfoList.forEachIndexed { index, baseLayerInfo ->
            startY = layoutItemFrame(baseLayerInfo, startY, animate)
            containerItemFrames[baseLayerInfo.parentKey]?.viewHolder?.onPositionChange(layoutInfoList.size - 1 - index, layoutInfoList.size)
            thumbnailItemFrames[baseLayerInfo.parentKey]?.viewHolder?.onPositionChange(layoutInfoList.size - 1 - index, layoutInfoList.size)
        }
    }

    private fun dragBackAnimate(dragItemFrame:DragLayerInfoItem, offset: Int = layoutOffset, animate: Boolean = false, groupHeight:Int, action: (() -> Unit)? = null) {
        var startY = offset
        layoutInfoList.forEachIndexed { index, baseLayerInfo ->
            if (baseLayerInfo == dragItemFrame.dragLayerInfo) {
                layoutDragItemFrame(dragItemFrame, startY, animate, action)
                startY += groupHeight
            } else {
                startY = layoutItemFrame(baseLayerInfo, startY, animate)
            }
            containerItemFrames[baseLayerInfo.parentKey]?.viewHolder?.onPositionChange(layoutInfoList.size - 1 - index, layoutInfoList.size)
            thumbnailItemFrames[baseLayerInfo.parentKey]?.viewHolder?.onPositionChange(layoutInfoList.size - 1 - index, layoutInfoList.size)
        }
    }

    /**
     * 拖拽放开第二段动画
     */
    private fun dragBackAnimateSecond(dragItemFrame:DragLayerInfoItem, offset: Int = layoutOffset, animate: Boolean = false, groupHeight:Int) {
        var startY = offset
        layoutInfoList.forEachIndexed { index, baseLayerInfo ->
            if (baseLayerInfo == dragItemFrame.dragLayerInfo) {
                layoutItemFrame(baseLayerInfo, startY, animate)
                startY += groupHeight
            } else {
                startY = layoutItemFrame(baseLayerInfo, startY, animate)
            }
            containerItemFrames[baseLayerInfo.parentKey]?.viewHolder?.onPositionChange(layoutInfoList.size - 1 - index, layoutInfoList.size)
            thumbnailItemFrames[baseLayerInfo.parentKey]?.viewHolder?.onPositionChange(layoutInfoList.size - 1 - index, layoutInfoList.size)
        }
    }

    /**
     * 重置滚动距离，一般是列表长度变化时调用。
     */
    private fun resetScrollLength(animate: Boolean) {
        if (scrollLength <= maxScrollLength) {
            return
        }
        flingAnimator.cancel()
        if (animate) {
            flingAnimator.scrollTo(scrollLength, maxScrollLength)
        } else {
            scrollLength = MathUtil.clamp(scrollLength, 0, maxScrollLength)
        }
    }

    /**
     * 根据起始位置，对Item进行位置设置。
     * @return 返回布局尾部。
     */
    private fun layoutItemFrame(
        layerInfo: BaseLayerInfo,
        start: Int,
        animate: Boolean,
        action: (() -> Unit)? = null
    ): Int {
        containerItemFrames[layerInfo.parentKey]?.let { itemViewFrame ->
            // 如果一个item超出列表外，则布局不考虑他。
            itemViewFrame.layout(0, start)
            if (!itemViewFrame.isOnDrag) {
                if (animate) {
                    // 如果一个Item正在被拖拽，不用设置位置，但是需要给他留位置。
                    itemViewFrame.animateTo(0, start) {
                        action?.invoke()
                    }
                } else {
                    itemViewFrame.set(0, start)
                }
            }
            return start + itemViewFrame.height
        }
        return start
    }

    /**
     * 根据起始位置，对Item进行位置设置。
     * @return 返回布局尾部。
     */
    private fun layoutDragItemFrame(
        dragItemFrame: DragLayerInfoItem,
        start: Int,
        animate: Boolean,
        action: (() -> Unit)? = null
    ) {
        containerItemFrames[dragItemFrame.dragLayerInfo.parentKey]?.let { itemViewFrame ->
            // 如果一个item超出列表外，则布局不考虑他。
            itemViewFrame.layout(0, start)
            if (!itemViewFrame.isOnDrag) {
                if (animate) {
                    // 如果一个Item正在被拖拽，不用设置位置，但是需要给他留位置。
                    dragItemFrame.animateTo(0, start, action)
                } else {
                    itemViewFrame.set(0, start)
                }
            }
        }
    }

    /**
     * 计算在正常排列，当前图层的y值。
     */
    private fun calculateLayerContainerTop(layerInfo: BaseLayerInfo): Int {
        var startY = 0
        layoutInfoList.forEachIndexed { index, baseLayerInfo ->
            if (baseLayerInfo == layerInfo) {
                return startY
            }
            startY += containerItemFrames[baseLayerInfo.parentKey]?.height ?: 0
        }
        return 0
    }

    /**
     * 对所有子View进行布局。
     */
    private fun layoutChildren() {
        layoutInfoList.forEach {
            containerItemFrames[it.parentKey]?.layoutDelegate?.invoke()
            thumbnailItemFrames[it.parentKey]?.layoutDelegate?.invoke()
        }
    }

    private fun canStartDrag(layerInfo: BaseLayerInfo): Boolean {
        return layoutInfoList.indexOf(layerInfo) != layoutInfoList.size - 1
    }

    /**
     * 获取当前列表整体长度。
     */
    fun calculateTotalLength(): Int {
        var totalLength = 0
        layoutInfoList.forEach { layerInfo ->
            containerItemFrames[layerInfo.parentKey]?.takeIf { !it.isOutOfList }?.run { totalLength += height }
        }
        return totalLength
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        bpGestureDetector.onTouchEvent(event)
        return true
    }

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        bpGestureDetector.onTouchEvent(ev)
        return true
    }

    inner class GestureListener : BpGestureDetector.SimpleOnGestureListener() {

        private var shouldStartDragOnMajorMove = false

        /**
         * 拖拽开始滚动的距离。
         */
        private val DRAG_SCROLL_DISTANCE = 20.dp()

        /**
         * 不需要长按直接按下即可拖拽的距离。
         */
        private val DIRECT_DRAG_DISTANCE = 30.dp()


        override fun onMajorScroll(downEvent: MotionEvent, moveEvent: MotionEvent, distanceX: Float, distanceY: Float): Boolean {
            if (currentDragLayerInfoItem != null) {
                // 拖拽Item。
                currentDragLayerInfoItem?.onDrag(-distanceX.toInt(), -distanceY.toInt())
                when {
                    moveEvent.y < DRAG_SCROLL_DISTANCE -> {
                        flingAnimator.startConstantScroll(-0.4f, scrollLength, 0, maxScrollLength)
                    }
                    moveEvent.y > height - DRAG_SCROLL_DISTANCE -> {
                        flingAnimator.startConstantScroll(0.4f, scrollLength, 0, maxScrollLength)
                    }
                    else -> {
                        flingAnimator.endConstantScroll()
                    }
                }
            } else {
                if (shouldStartDragOnMajorMove) {
                    // 开启拖动。
                    findItemByPosition(downEvent.x.roundToInt(), (downEvent.y + scrollLength - 15.dp()).roundToInt())
                            ?.takeIf { canStartDrag(it) }
                            ?.apply {
                                currentDragLayerInfoItem = DragLayerInfoItem(this).apply {
                                    onStartDrag()
                                }
                            }
                } else {
                    // 正常滑动。
                    if (maxScrollLength > 0) {
                        scrollLength = MathUtil.clamp(scrollLength + distanceY.roundToInt(), 0, maxScrollLength)
                        layoutChildren()
                    }
                }
            }
            return true
        }

        override fun onTap(downEvent: MotionEvent, upEvent: MotionEvent): Boolean {
            findItemByPosition(downEvent.x.roundToInt(), (downEvent.y + scrollLength - 15.dp()).roundToInt())?.run {
                layerInfoClickListener?.invoke(this)
            }
            return super.onTap(downEvent, upEvent)
        }

        override fun onLongPress(downEvent: MotionEvent): Boolean {
            if (currentDragLayerInfoItem == null) {
                findItemByPosition(downEvent.x.roundToInt(), (downEvent.y + scrollLength - 15.dp()).roundToInt())
                        ?.takeIf { canStartDrag(it) }
                        ?.apply {
                            currentDragLayerInfoItem = DragLayerInfoItem(this).apply {
                                onStartDrag()
                            }
                        }
            }
            return true
        }

        override fun onMajorFingerUp(upEvent: MotionEvent?): Boolean {
            currentDragLayerInfoItem?.run {
                onEndDrag()
            }
            currentDragLayerInfoItem = null
            flingAnimator.endConstantScroll()
            shouldStartDragOnMajorMove = false
            return super.onMajorFingerUp(upEvent)
        }


        override fun onMajorFingerDown(downEvent: MotionEvent): Boolean {
            if (downEvent.x > width - DIRECT_DRAG_DISTANCE) {
                shouldStartDragOnMajorMove = true
            }
            return super.onMajorFingerDown(downEvent)
        }


        override fun onFlingFromBottomToTop(downEvent: MotionEvent?, upEvent: MotionEvent?, velocityX: Float, velocityY: Float): Boolean {
            fling(velocityY)
            return super.onFlingFromBottomToTop(downEvent, upEvent, velocityX, velocityY)
        }

        override fun onFlingFromTopToBottom(downEvent: MotionEvent?, upEvent: MotionEvent?, velocityX: Float, velocityY: Float): Boolean {
            fling(velocityY)
            return super.onFlingFromTopToBottom(downEvent, upEvent, velocityX, velocityY)
        }

        fun fling(velocityY: Float) {
            if (currentDragLayerInfoItem == null) {
                maxScrollLength.takeIf { it > 0 }?.let {
                    flingAnimator.fling(-velocityY.roundToInt(), scrollLength, 0, it)
                }
            }
        }

        /**
         * 根据一个点击位置返回对于的图层对象。
         */
        private fun findItemByPosition(x: Int, y: Int): BaseLayerInfo? {
            return layoutInfoList.find { containerItemFrames[it.parentKey]?.currentRect?.contains(x, y) == true }
        }
    }


    /**
     * 当前拖拽的Item。
     */
    inner class DragLayerInfoItem(val dragLayerInfo: BaseLayerInfo) {

        /**
         * 附带的其他LayerInfos，会跟随一起移动。
         */
        val attachLayerInfos = ArrayList<BaseLayerInfo>()

        /**
         * 上次记录的位置。
         */
        var lastIndex = -1

        /**
         * 缩略图附带拖拽时的目标
         */
        private var thumbAttachDrag4PositionArray = if (RTLTool.isLayoutRtl()) {
            arrayOf(
                Point(0.dp(), 8.dp()),
                Point((-26).dp(), 8.dp()),
                Point(0.dp(), 34.dp()),
                Point((-26).dp(), 34.dp())
            )
        } else {
            arrayOf(
                Point(0.dp(), 8.dp()),
                Point(26.dp(), 8.dp()),
                Point(0.dp(), 34.dp()),
                Point(26.dp(), 34.dp())
            )
        }

        private var thumbAttachDrag2PositionArray = if (RTLTool.isLayoutRtl()) {
            arrayOf(Point(0.dp(), 20.dp()), Point((-26).dp(), 20.dp()))
        } else {
            arrayOf(Point(0.dp(), 20.dp()), Point(26.dp(), 20.dp()))
        }

        /**
         * 拖拽出去的距离。
         */
        private var dragOutDistance = 0

        /**
         * 是否拖拽出去了。
         */
        private var isDragOut = false

        /**
         * 是否可以被删除。
         */
        private var canDelete = true

        /**
         * 拖拽过程中的一点点移动。
         */
        fun offset(dx: Int, dy: Int) {
            containerItemFrames[dragLayerInfo.parentKey]?.offset(dx, dy)
            attachLayerInfos.forEach {
                if (dragLayerInfo != it) {
                    containerItemFrames[it.parentKey]?.offset(dx, dy)
                }
            }
        }

        private fun animateBy(dx: Int, dy: Int) {
            containerItemFrames[dragLayerInfo.parentKey]?.run {
                animateTo(dx + targetRect.left, dy + targetRect.top, duration = 200L)
            }
            attachLayerInfos.forEach {
                if (dragLayerInfo != it) {
                    containerItemFrames[it.parentKey]?.run {
                        animateTo(dx + targetRect.left, dy + targetRect.top, duration = 200L)
                    }
                }
            }
        }

        fun animateTo(left:Int, top:Int, action: (() -> Unit)?= null) {
            containerItemFrames[dragLayerInfo.parentKey]?.run {
                animateTo(left, top, duration = 200L) {
                    action?.invoke()
                }
            }
            val subTop = if (dragLayerInfo is GroupLayerInfo) {
                top + GROUP_NORMAL_HEIGHT
            } else {
                top
            }
            attachLayerInfos.forEach {
                if (dragLayerInfo != it) {
                    containerItemFrames[it.parentKey]?.run {
                        animateTo(left, subTop, duration = 200L)
                    }
                }
            }
        }

        /**
         * 删除拖拽图层。
         */
        fun delete() {
            val targetX = if (RTLTool.isLayoutRtl()) {
                DeviceUtils.getScreenWidth() - width + ITEM_WIDTH / 2
            } else {
                width - DeviceUtils.getScreenWidth() - ITEM_WIDTH / 2
            }
            val targetY = height / 2 + scrollLength
            attachLayerInfos.forEach {
                if (dragLayerInfo is GroupLayerInfo) {
                    containerItemFrames[it.parentKey]?.animateTo(targetX, targetY - GROUP_DRAG_HEIGHT / 2, duration = ANIMATE_TIME)
                    containerItemFrames[it.parentKey]?.alphaDismiss()
                    containerItemFrames[it.parentKey]?.attachView?.run {
                        pivotX = height / 2f - GROUP_NORMAL_HEIGHT / 2f
                        pivotY = 0f
                        animate().scaleX(0.3f).scaleY(0.3f).setDuration(ANIMATE_TIME).start()
                    }
                    thumbnailItemFrames[it.parentKey]?.alphaDismiss()
                } else {
                    if (it != dragLayerInfo) {
                        containerItemFrames[it.parentKey]?.animateTo(targetX, targetY - ITEM_HEIGHT / 2, duration = ANIMATE_TIME)
                        containerItemFrames[it.parentKey]?.alphaDismiss()
                        containerItemFrames[it.parentKey]?.attachView?.run {
                            pivotX = height / 2f
                            pivotY = 0f
                            animate().scaleX(0.3f).scaleY(0.3f).setDuration(ANIMATE_TIME).start()
                        }
                        thumbnailItemFrames[it.parentKey]?.alphaDismiss()
                    }
                }
            }
            containerItemFrames[dragLayerInfo.parentKey]?.alphaDismiss()
            containerItemFrames[dragLayerInfo.parentKey]?.attachView?.run {
                pivotX = height / 2f
                pivotY = 0f
                animate().scaleX(0.3f).scaleY(0.3f).setDuration(ANIMATE_TIME).start()
            }
            thumbnailItemFrames[dragLayerInfo.parentKey]?.alphaDismiss()
            containerItemFrames[dragLayerInfo.parentKey]?.animateTo(targetX, targetY - ITEM_HEIGHT / 2, duration = ANIMATE_TIME) {
                // 动画结束后移除View。
                removeItemFrames(dragLayerInfo)
                attachLayerInfos.forEach { removeItemFrames(it) }
                if (isMultiSelectDrag()) {
                    layerInfoDeleteListener?.invoke(multiSelectGroupLayerInfo!!)
                } else {
                    layerInfoDeleteListener?.invoke(dragLayerInfo)
                }
            }

        }

        /**
         * 是否在删除区间。
         */
        private fun isInDeleteArea(): Boolean {
            return if (RTLTool.isLayoutRtl()) {
                Rect().apply {
                    left = DeviceUtils.getScreenWidth() - 110.dp()
                    right = left + 220.dp()
                    top = height / 2 - 110.dp()
                    bottom = height / 2 + 110.dp()
                }.contains(
                    containerItemFrames[dragLayerInfo.parentKey]!!.currentRect.right + 10.dp(),
                    containerItemFrames[dragLayerInfo.parentKey]!!.currentRect.centerY() - scrollLength
                )
            } else {
                Rect().apply {
                    left = width - DeviceUtils.getScreenWidth() - 110.dp()
                    right = left + 220.dp()
                    top = height / 2 - 110.dp()
                    bottom = height / 2 + 110.dp()
                }.contains(
                    containerItemFrames[dragLayerInfo.parentKey]!!.currentRect.left - 10.dp(),
                    containerItemFrames[dragLayerInfo.parentKey]!!.currentRect.centerY() - scrollLength
                )
            }
        }

        private fun isMultiSelectDrag(): Boolean {
            return multiSelectGroupLayerInfo?.contains(dragLayerInfo) == true && multiSelectGroupLayerInfo?.subLayerInfos?.size ?: 0 > 1
        }

        /**
         * 开启拖拽。
         */
        fun onStartDrag() {
            containerItemFrames[dragLayerInfo.parentKey]?.let { dragItemFrame ->
                // 当前拖拽的frame层级提高。
                dragItemFrame.isOnDrag = true
                dragItemFrame.attachView.elevation = 5.dpf()
                val listFrames = ArrayList<ItemFrame>()
                dragItemFrame.viewHolder.onStartDrag()
                // 缩略图层级也需要提高。
                // thumbnailItemFrames[dragLayerInfo.parentKey]?.attachView?.elevation = 21.dpf()
                when {
                    isMultiSelectDrag() -> {
                        // 拖拽的是多选的元素。
                        multiSelectGroupLayerInfo?.let { multiSelectGroupLayerInfo ->
                            multiSelectGroupLayerInfo.subLayerInfos.forEachIndexed { index, pair ->
                                // 其他多选的同步做拖拽。
                                if (pair.first != dragLayerInfo) {
                                    onContainerStartAttachDrag(pair.first, dragItemFrame.targetRect.left, dragItemFrame.targetRect.top)?.let {
                                        listFrames.add(it)
                                    }
                                    layoutInfoList.remove(pair.first)
                                }
                                onThumbnailStartAttachDrag(pair.first, multiSelectGroupLayerInfo.subLayerInfos.size - 1 - index, multiSelectGroupLayerInfo.subLayerInfos.size)
                                // 记录。
                                attachLayerInfos.add(0, pair.first)
                            }
                        }
                    }
                    dragLayerInfo is GroupLayerInfo -> {
                        // 如果拖拽的是组的话，需要遍历里面的子项。
                        dragLayerInfo.subLayerInfos.forEachIndexed { index, pair ->
                            onContainerStartAttachDrag(pair.first, dragItemFrame.targetRect.left, dragItemFrame.targetRect.bottom)?.let {
                                listFrames.add(it)
                            }
                            onThumbnailStartAttachDrag(pair.first, dragLayerInfo.subLayerInfos.size - 1 - index, dragLayerInfo.subLayerInfos.size)
                            layoutInfoList.remove(pair.first)
                            // 记录。
                            attachLayerInfos.add(0, pair.first)
                        }
                        // 组大小变化。
                        dragItemFrame.height = GROUP_DRAG_HEIGHT
                        dragItemFrame.onUpdateSize()
                    }
                    else -> {
                        // 其他情况仅
                        thumbnailItemFrames[dragLayerInfo.parentKey]?.animateTo(0, 8.dp())
                    }
                }
                layoutOffset = dragItemFrame.currentRect.top - calculateLayerContainerTop(dragLayerInfo)
            }
            resetChildrenLayout(animate = true)
            layoutChildren()
            // 记录一开始的位置。
            lastIndex = layoutInfoList.indexOf(dragLayerInfo)
            canDelete = canDelete()
            dragLayerListener?.onStartDrag(canDelete)
        }

        /**
         * 结束拖拽。
         */
        fun onEndDrag() {
            containerItemFrames[dragLayerInfo.parentKey]?.let { dragItemFrame ->
                if (isInDeleteArea() && canDelete) {
                    // 到了删除区域。
                    delete()
                    dragLayerListener?.onEndDrag(inDeleteArea = true)
                } else {
                    dragLayerListener?.onEndDrag(inDeleteArea = false)
                    // 重置Item状态。
                    dragItemFrame.isOnDrag = false
                    val listFrames = ArrayList<ItemFrame>()
                    dragItemFrame.viewHolder.onEndDrag {
                        dragItemFrame.attachView.elevation = 0f
                        thumbnailItemFrames[dragLayerInfo.parentKey]?.attachView?.elevation = 1.dpf()
                    }
                    if (dragItemFrame.isOutOfList) {
                        // 如果被脱处理，需要再移回去。
                        layoutInfoList.add(lastIndex, dragLayerInfo)
                        dragItemFrame.isOutOfList = false
                    }
                    when {
                        isMultiSelectDrag() -> {
                            // 拖拽的是多选中的图层。
                            val dragIndexInAttach = attachLayerInfos.indexOf(dragLayerInfo)
                            var dragIndexInLayout = layoutInfoList.indexOf(dragLayerInfo)
                            // "onEndDrag:$dragIndexInAttach".print("lmll")
                            attachLayerInfos.forEachIndexed { index, layerInfo ->
                                // 遍历组中的元素拖拽回去。
                                if (layerInfo != dragLayerInfo) {
                                    onContainerEndAttachDrag(layerInfo)?.let {
                                        listFrames.add(it)
                                    }
                                    if (index < dragIndexInAttach) {
                                        layoutInfoList.add(dragIndexInLayout++, layerInfo)
                                    } else {
                                        layoutInfoList.add(++dragIndexInLayout, layerInfo)
                                    }
                                }
                                onThumbnailEndAttachDrag(layerInfo)
                            }
                        }
                        dragLayerInfo is GroupLayerInfo -> {
                            // 拖拽的是图层组。
                            attachLayerInfos.forEach { layerInfo ->
                                // 遍历组中的元素拖拽回去。
                                onContainerEndAttachDrag(layerInfo)?.let {
                                    listFrames.add(it)
                                }
                                onThumbnailEndAttachDrag(layerInfo)
                                layoutInfoList.add(++lastIndex, layerInfo)
                            }
                            // 组的frame尺寸变化。
                            dragItemFrame.height = GROUP_NORMAL_HEIGHT
                            dragItemFrame.onUpdateSize()
                        }
                        else -> {
                            // 其他情况。
                            if (RTLTool.isLayoutRtl()) {
                                thumbnailItemFrames[dragLayerInfo.parentKey]?.animateTo((-8).dp(), 8.dp())
                            } else {
                                thumbnailItemFrames[dragLayerInfo.parentKey]?.animateTo(8.dp(), 8.dp())
                            }
                        }
                    }
                    notifyLayerInfoChangeIfNeed()
                }
            }
            totalLength = calculateTotalLength()
            layoutOffset = ((height - totalLength) / 2).coerceAtLeast(0)
            resetChildrenLayout(animate = true)
            resetScrollLength(animate = true)
            layoutChildren()
        }

        /**
         * 拖拽过程。
         */
        fun onDrag(dx: Int, dy: Int) {
            dragOutDistance += dx
            // 计算拖拽距离。
            if (!isDragOut) {
                // 还未拉出拖拽区域。
                if (abs(dragOutDistance) > DRAG_OUT_DISTANCE) {
                    // 当前拖拽距离足够。
                    isDragOut = true
                    hapticVirtualKey()
                    // 距离在加上一段，相当于弹出来。
                    dragOutDistance -= 30.dp()
                    // 动画移动拖拽的单元。
                    animateBy(dragOutDistance, dy)
                    // container内部布局回正。
                    containerItemFrames[dragLayerInfo.parentKey]?.viewHolder?.onDragOutOfList()
                    // 缩略图回正。
                    thumbnailItemFrames[dragLayerInfo.parentKey]?.run {
                        animateTo(0, targetRect.top)
                    }
                    attachLayerInfos.forEachIndexed { index, layerInfo ->
                        thumbnailItemFrames[layerInfo.parentKey]?.run {
                            if (attachLayerInfos.size > 2) {
                                thumbAttachDrag4PositionArray[index.coerceAtMost(3)].let { animateTo(it.x, it.y) }
                            } else {
                                thumbAttachDrag2PositionArray[index].let { animateTo(it.x, it.y) }
                            }
                        }
                    }
                } else {
                    // 距离不够，x轴的offset不变。
                    offset(0, dy)
                    // 橡皮糖偏移值。
                    val gumDistance = dragOutDistance * 4 / 15
                    // 改变container内部布局形成橡皮糖效果。
                    containerItemFrames[dragLayerInfo.parentKey]?.viewHolder?.onDragBeforeOut(gumDistance)
                    // 改变缩略图的offset形成橡皮糖效果。
                    thumbnailItemFrames[dragLayerInfo.parentKey]?.run {
                        set(gumDistance, targetRect.top)
                    }
                    attachLayerInfos.forEachIndexed { index, layerInfo ->
                        thumbnailItemFrames[layerInfo.parentKey]?.run {
                            // 子缩略图分别偏移。
                            if (attachLayerInfos.size > 2) {
                                thumbAttachDrag4PositionArray[index.coerceAtMost(3)].let { set(it.x + gumDistance, it.y) }
                            } else {
                                thumbAttachDrag2PositionArray[index].let { set(it.x + gumDistance, it.y) }
                            }
                        }
                    }
                }
            } else {
                // 拖出来之后直接按照拖拽距离偏移。
                offset(dx, dy)
            }
            // 重新布局。
            reLayoutLayerInfoList()
            // 在删除区域时设置alpha。
            if (canDelete && isInDeleteArea()) {
                containerItemFrames[dragLayerInfo.parentKey]?.alpha = 0.5f
                thumbnailItemFrames[dragLayerInfo.parentKey]?.alpha = 0.25f
                attachLayerInfos.forEach {
                    thumbnailItemFrames[it.parentKey]?.alpha = 0.25f
                }
            } else {
                containerItemFrames[dragLayerInfo.parentKey]?.alpha = 1f
                thumbnailItemFrames[dragLayerInfo.parentKey]?.alpha = 1f
                attachLayerInfos.forEach {
                    thumbnailItemFrames[it.parentKey]?.alpha = 1f
                }
            }
            // 通知外界。
            if (RTLTool.isLayoutRtl()) {
                dragLayerListener?.onDrag(
                    ((dragOutDistance - DRAG_OUT_DISTANCE).coerceAtLeast(0f)),
                    isInDeleteArea() && canDelete
                )
            } else {
                dragLayerListener?.onDrag(
                    ((-dragOutDistance - DRAG_OUT_DISTANCE).coerceAtLeast(0f)),
                    isInDeleteArea() && canDelete
                )
            }
        }

        private fun reLayoutLayerInfoList() {
            // 重新布局。
            containerItemFrames[dragLayerInfo.parentKey]?.run {
                val currentIndex = layoutInfoList.indexOf(dragLayerInfo)
                val targetIndex = getIndexByDragItemCurrent(currentIndex, dragLayerInfo, targetRect)
                if (currentIndex != targetIndex) {
                    // "onMajorScroll:$currentIndex,$targetIndex".print("lmll")
                    when {
                        targetIndex == -1 -> {
                            // 拖拽出去时。
                            layoutInfoList.remove(dragLayerInfo)
                            isOutOfList = true
                        }
                        currentIndex == -1 -> {
                            // 拖拽回来。
                            lastIndex = targetIndex
                            layoutInfoList.add(targetIndex, dragLayerInfo)
                            isOutOfList = false
                        }
                        else -> {
                            // 交换位置。
                            lastIndex = targetIndex
                            layoutInfoList.remove(dragLayerInfo)
                            layoutInfoList.add(targetIndex, dragLayerInfo)
                        }
                    }
                    resetChildrenLayout(animate = true)
                    layoutChildren()
                }
            }
        }

        /**
         * 容器开始附带拖拽。
         */
        private fun onContainerStartAttachDrag(layerInfo: BaseLayerInfo, targetLeft: Int, targetTop: Int):ItemFrame? {
            return containerItemFrames[layerInfo.parentKey]?.apply {
                // 设置拖拽状态。
                isOnDrag = true
                // 全部移动到底部。
                animateTo(targetLeft, targetTop)
                // 暂时移出排列。
                isOutOfList = true
                attachView.elevation = 21f
                alphaTo(0f)
            }
        }

        private fun onContainerEndAttachDrag(layerInfo: BaseLayerInfo):ItemFrame? {
            // 遍历组中的元素拖拽回去。
            return containerItemFrames[layerInfo.parentKey]?.apply {
                isOnDrag = false
                isOutOfList = false
                alphaTo(1f) {
                    attachView.elevation = 0f
                }
            }
        }

        /**
         * 缩略图开始附带拖拽。
         */
        private fun onThumbnailStartAttachDrag(layerInfo: BaseLayerInfo, index: Int, totalSize: Int) {
            thumbnailItemFrames[layerInfo.parentKey]?.run {
                // 将图层的缩略图Item缩放。
                attachView.pivotX = 0f
                attachView.pivotY = 0f
                attachView.animate().let {
                    it.scaleX(0.48f).scaleY(0.48f)
                    if (index > 3) {
                        // 4个之后的都隐藏。
                        it.alpha(0f)
                    }
                    it.start()
                }
                if (totalSize > 2) {
                    thumbAttachDrag4PositionArray[index.coerceAtMost(3)].let { animateTo(it.x, it.y) }
                } else {
                    thumbAttachDrag2PositionArray[index].let { animateTo(it.x, it.y) }
                }
                //attachView.elevation = 21.dpf()
            }
        }

        /**
         * 缩略图结束附加拖拽。
         */
        private fun onThumbnailEndAttachDrag(layerInfo: BaseLayerInfo) {
            thumbnailItemFrames[layerInfo.parentKey]?.run {
                if (RTLTool.isLayoutRtl()) {
                    animateTo((-8).dp(), 8.dp())
                } else {
                    animateTo(8.dp(), 8.dp())
                }
                attachView.animate().cancel()
                attachView.animate()
                        .scaleX(1f)
                        .scaleY(1f)
                        .alpha(1f)
                        .setListener(object : AnimatorListenerAdapter() {
                            override fun onAnimationEnd(animation: Animator) {
                                attachView.animate().setListener(null)
                                attachView.elevation = 1.dpf()
                            }
                        }).start()
            }
        }


        /**
         * 是否可以删除。
         */
        private fun canDelete(): Boolean {
//            var count = 0
//            layoutInfoList.forEach {
//                if (it is PictureLayerInfo && dragLayerInfo != it && !attachLayerInfos.contains(it)) {
//                    count++
//                }
//            }
//            return count > 0
            return true
        }


        /**
         * 根据一个Item位置返回其现在应该在列表中的位置。
         */
        private fun getIndexByDragItemCurrent(currentIndex: Int, dragLayerInfo: BaseLayerInfo, itemCurrent: Rect?): Int {
            if (itemCurrent == null || itemCurrent.left < -itemCurrent.width() / 2) {
                // 拉出了列表。
                return -1
            }
            var lastDistance = Float.MAX_VALUE
            layoutInfoList.forEachIndexed { index, baseLayerInfo ->
                containerItemFrames[baseLayerInfo.parentKey]?.run {
                    val distance = (itemCurrent.centerY() - layoutRect.centerY()).toFloat()
                    if (distance < 0) { // 从上到下遍历到了一个位置在拖拽下方的item。
                        val insertIndex = if (-distance < lastDistance) {
                            // 如果距离下方的距离小于上方的距离，则插入此Item。
                            index
                        } else {
                            // 否则插入上方的Item。
                            (index - 1).coerceAtLeast(0)
                        }
                        val frontIndex = if (currentIndex < insertIndex && currentIndex != -1) {
                            insertIndex + 1
                        } else {
                            insertIndex
                        }
                        // 找到插入位置的上一个Item.
                        val frontLayerInfo = layoutInfoList.safeGet(frontIndex - 1)
                        val backLayerInfo = layoutInfoList.safeGet(frontIndex)
                        return if (canInsert(dragLayerInfo, frontLayerInfo, backLayerInfo) && (insertIndex != layoutInfoList.size - 1)) {
                            // 判断是否可以插入。
                            insertIndex
                        } else {
                            // 不可插入时都直接返回原位置。
                            currentIndex
                        }
                    } else {
                        lastDistance = distance
                    }
                }
            }
            return currentIndex
        }

        /**
         * 判断是否可以交换层级。
         */
        private fun canInsert(currentLayerInfo: BaseLayerInfo, frontLayerInfo: BaseLayerInfo?, backLayerInfo: BaseLayerInfo?): Boolean {
            if (currentLayerInfo == frontLayerInfo || currentLayerInfo == backLayerInfo) {
                return true
            }
            return if (currentLayerInfo is FocusLayerInfo && currentLayerInfo.groupLayerInfo != null) {
                // 编组内的图层仅支持组内交换。
                when {
                    frontLayerInfo is FocusLayerInfo && frontLayerInfo.groupLayerInfo == currentLayerInfo.groupLayerInfo -> true
                    frontLayerInfo == currentLayerInfo.groupLayerInfo -> true
                    else -> false
                }
            } else {
                // 编组外的图层仅支持交换
                when {
                    frontLayerInfo == null -> true
                    frontLayerInfo is GroupLayerInfo -> false
                    backLayerInfo is FocusLayerInfo && backLayerInfo.groupLayerInfo != null -> false
                    else -> true
                }
            }
        }
    }
}

open class DragLayerListener {

    open fun onStartDrag(canDelete: Boolean) {

    }

    open fun onDrag(distance: Float, inDeleteArea: Boolean) {

    }

    open fun onEndDrag(inDeleteArea: Boolean) {

    }
}

open interface OnLayoutChildListener {

    fun onLayoutChild()
}

/**
 * 用于表达ItemView位置的一个类，内部实现位置的动画过渡。
 * 注意Item需要有固定的尺寸。
 */
class ItemFrame(val viewHolder: LayerManageViewHolder, var width: Int, var height: Int) {

    var attachView: ViewGroup = viewHolder.itemView as ViewGroup

    /**
     * 当前位置，实际绘制位置。
     */
    val currentRect = Rect()

    /**
     * 设定的目标位置，内部动画会慢慢过渡。
     * 如果[isOutOfList]为true，这个会代表其在列表中的临时展位。
     */
    val targetRect = Rect()

    /**
     * 布局时的位置。
     */
    val layoutRect = Rect()

    /**
     * 动画处理器。
     */
    private val animator = ValueAnimator.ofFloat(0f, 1f)

    /**
     * 是否在拖拽状态中
     */
    var isOnDrag = false

    /**
     * 是否已经移出列表，移除的项不会参与计算整体高度与排列。
     */
    var isOutOfList = false

    /**
     * 布局代理。
     */
    var layoutDelegate: (() -> Unit)? = null

    /**
     * Alpha.
     */
    var alpha = 1f
        set(value) {
            field = value
            viewHolder.setContentAlpha(alpha)
        }

    /**
     * 更新item尺寸。
     */
    fun onUpdateSize() {
        currentRect.run {
            right = left + width
            bottom = top + height
        }
        targetRect.run {
            right = left + width
            bottom = top + height
        }
        layoutRect.run {
            right = left + width
            bottom = top + height
        }
    }

    fun layout(left: Int, top: Int) {
        layoutRect.set(left, top, left + width, top + height)
    }

    /**
     * 设置当前位置。
     */
    fun set(left: Int, top: Int) {
        targetRect.set(left, top, left + width, top + height)
        if (!animator.isRunning) {
            // 如果当前正在过渡动画的话,不能直接改变current位置，等待过渡结束即可。
            currentRect.set(targetRect)
            layoutDelegate?.invoke()
        }
    }

    fun offset(dx: Int, dy: Int) {
        targetRect.offset(dx, dy)
        currentRect.offset(dx, dy)
        layoutDelegate?.invoke()
    }


    /**
     * 动画过渡到目标位置。
     * 如果在上一次动画未结束时调用此方法
     */
    fun animateTo(left: Int, top: Int, shouldInterrupt: Boolean = true, duration: Long = 250L, endAction: (() -> Unit)? = null) {
        var interrupt = shouldInterrupt
        if (left == targetRect.left && top == targetRect.top) {
            // 如果目标相同，默认不打断。
            interrupt = false
        }
        targetRect.set(left, top, left + width, top + height)
        if (interrupt || !animator.isRunning) {
            // 动画没有开始或者允许打断时才执行动画。
            animator.removeAllUpdateListeners()
            animator.cancel()
            animator.duration = duration
            animator.addUpdateListener(object : ValueAnimator.AnimatorUpdateListener {
                var lastProgress = 0f
                override fun onAnimationUpdate(animation: ValueAnimator) {
                    val progress = animation.animatedValue as Float
                    currentRect.let { rect ->
                        if (progress != 1f) {
                            val ratio = (progress - lastProgress) / (1f - lastProgress)
                            rect.left = MathUtil.getRatioValue(rect.left, targetRect.left, ratio)
                            rect.top = MathUtil.getRatioValue(rect.top, targetRect.top, ratio)
                            rect.right = MathUtil.getRatioValue(rect.right, targetRect.right, ratio)
                            rect.bottom = MathUtil.getRatioValue(rect.bottom, targetRect.bottom, ratio)
                            // "animateTo:$rect + ${this@ItemFrame}".print("lmll")
                        } else {
                            rect.set(targetRect)
                            endAction?.invoke()
                        }
                    }
                    layoutDelegate?.invoke()
                    lastProgress = progress
                }
            })
            animator.start()
        }
    }

    fun alphaDismiss() {
        ValueAnimator.ofFloat(alpha, 0f).run {
            addUpdateListener {
                alpha = it.animatedValue as Float
            }
            duration = LayerManageListLayout.ANIMATE_TIME
            start()
        }
    }

    fun alphaTo(targetAlpha: Float, action: (() -> Unit)? = null) {
        ValueAnimator.ofFloat(alpha, targetAlpha).run {
            addUpdateListener {
                alpha = it.animatedValue as Float
            }
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    action?.invoke()
                }
            })
            duration = LayerManageListLayout.ANIMATE_TIME
            start()
        }
    }

    override fun toString(): String {
        return "ItemFrame(width=$width, height=$height, currentRect=$currentRect, targetRect=$targetRect, layoutRect=$layoutRect)"
    }
}
