package com.commsource.duffle.airetouch

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.commsource.duffle.DuffleCategory
import com.google.gson.annotations.SerializedName
import org.jetbrains.annotations.NotNull
import java.io.Serializable

/**
 * ai焕颜tag
 */
@Entity(tableName = "duffle_airetouch_tag")
class AiRetouchTag : DuffleCategory<AiRetouchTag, AiRetouchMaterial>, Serializable, Cloneable {

    override var cateLevel: Int = 1

    @Ignore
    override var categorySort = 0

    @PrimaryKey
    @NotNull
    @SerializedName("category_id")
    @ColumnInfo(name = "GroupId")
    override var categoryId: String = ""

    @SerializedName("lower_category")
    @Ignore
    override var lowerCategory: List<AiRetouchTag>? = null

    @SerializedName("goods")
    @Ignore
    override var lowerMaterial: List<AiRetouchMaterial>? = null

    @SerializedName("upper_category")
    @Ignore
    override var upCategory: List<AiRetouchTag>? = null

    @ColumnInfo(name = "SubCateIds")
    override var subCateIds: MutableList<String>? = null

    @ColumnInfo(name = "SubMaterials")
    override var subMaterials: MutableList<String>? = null

    @SerializedName("name")
    @ColumnInfo(name = "GroupName")
    override var categoryName: String? = ""

    override fun onCompareLocal(localEntity: AiRetouchTag): Boolean {
        // categoryId 不一样就是新增
        var same = categoryName == localEntity.categoryName
        same = same and (categorySort == localEntity.categorySort)
        return same
    }

    override fun onSortCompare(nextEntity: AiRetouchTag): Int {
        if (equals(nextEntity)) {
            return 0
        }
        return if (categoryId < nextEntity.categoryId) -1 else 1
    }

    override fun isNeedRemove(): Boolean {
        return true
    }

    override fun hashCode(): Int {
        return super.hashCode()
    }

    override fun equals(other: Any?): Boolean {
        return other is AiRetouchTag && other.categoryId == categoryId
    }

}