package com.commsource.duffle.formula

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.commsource.homev2.topic.BAHolder
import com.commsource.homev2.topic.BAParentRvHolder

class BAAnimPlayHelper {
    /**
     *  控制配方的BA对比动画的播放
     */
    private val parentVisibleRect = Rect()
    private val itemVisibleRect = Rect()
    private val innerItemVisibleRect = Rect()

    // 记录上次没有播放完成的配方的Id。
    private var baNotCompleteEntity: Any? = null
    private var continueNotCompleteEntity: Any? = null

    // 正在执行动画的Holder
    private var executingAnimHolder: BAHolder? = null
    private var bindedParentRv: RecyclerView? = null
    private var excludeBottom: Int? = null
    private var excludeTop: Int? = null
    fun bindParentRv(parentRv: RecyclerView, excludeBottom: Int? = null, excludeTop: Int? = null) {
        this.bindedParentRv = parentRv
        this.excludeBottom = excludeBottom
        this.excludeTop = excludeTop
    }

    /**
     * Rv 每一次移动都需要是实时判断
     * 是否要暂停这个BA 动画。
     */
    fun pauseBaAnimIfNeed(rv: RecyclerView? = null, forcePause: Boolean = false) {
        val parentRv = (rv ?: bindedParentRv) ?: return
        executingAnimHolder?.let {
            // 判断上下左右有没有超出。
            parentRv.getGlobalVisibleRect(parentVisibleRect)
            excludeBottom?.let { parentVisibleRect.bottom = parentVisibleRect.bottom - it }
            excludeTop?.let { parentVisibleRect.top = parentVisibleRect.top + it }
            if (forcePause || !isResumeState || !isItemVisibleMoreThanHalf(
                    parentVisibleRect,
                    innerItemVisibleRect,
                    it.getBaCompareView()
                )
            ) {
                // 停止BA动画。
                baNotCompleteEntity = it.getHoldEntity()
                continueNotCompleteEntity = null
                it.setBACompareAnimState(false)
                executingAnimHolder = null
            }
        }
    }


    /**
     * 通知下一个Holder开启播放。
     */
    fun notifyNextHolder2Play(
        rv: RecyclerView? = null,
        lastCompleteEntity: Any? = null,
        checkChildRv: Boolean = false
    ) {
        val parentRv = (rv ?: bindedParentRv) ?: return
        if (!isResumeState) return
        // 如果是状态变更触发的下一个播发。需要先检查是否有在播的
        // 有在播的话，直接跳过
        val finalLastCompleteEntity = lastCompleteEntity ?: continueNotCompleteEntity
        if (finalLastCompleteEntity == null) {
            if (executingAnimHolder != null) {
                return
            }
        }
        // 先检查一下是否所有的RV都处于Idle 状态
        if (parentRv.scrollState != RecyclerView.SCROLL_STATE_IDLE) {
            continueNotCompleteEntity = finalLastCompleteEntity
            return
        }
        // 获取下一个可以播放的进行播放
        if (parentRv.height == 0) {
            parentRv.post {
                getCanPlayBAAnimHolder(
                    parentRv, finalLastCompleteEntity, checkChildRv
                )?.let {
                    continueNotCompleteEntity = null
                    baNotCompleteEntity = null
                    executingAnimHolder = it
                    it.setBACompareAnimState(true)
                }
            }
        } else {
            getCanPlayBAAnimHolder(parentRv, finalLastCompleteEntity, checkChildRv)?.let {
                continueNotCompleteEntity = null
                baNotCompleteEntity = null
                executingAnimHolder = it
                it.setBACompareAnimState(true)
            }
        }
    }

    /**
     * 获取可以播放BA动画的Holder
     */
    private fun getCanPlayBAAnimHolder(
        parentRv: RecyclerView,
        lastCompleteEntity: Any? = null,
        checkChildRv: Boolean = false
    ): BAHolder? {
        // 记录本次查询内可以播放的全部Holder
        val tempValidList = mutableListOf<BAHolder>()
        val tempAllList = mutableListOf<BAHolder>()
        parentRv.getGlobalVisibleRect(parentVisibleRect)
        excludeBottom?.let { parentVisibleRect.bottom = parentVisibleRect.bottom - it }
        excludeTop?.let { parentVisibleRect.top = parentVisibleRect.top + it }
        if (checkChildRv) {
            val itemCount = parentRv.adapter?.itemCount ?: 0
            for (index in 0..itemCount) {
                val holder = parentRv.findViewHolderForAdapterPosition(index)
                (holder as? BAParentRvHolder)?.getChildRv()?.let {
                    checkRvItemIsVisible(it, tempAllList, tempValidList, parentVisibleRect)
                }
            }
        } else {
            checkRvItemIsVisible(parentRv, tempAllList, tempValidList, parentVisibleRect)
        }
//
//        // 最外层rv可见的item
//        val itemCount = parentRv.adapter?.itemCount ?: 0
//        // 最外层Rv在屏幕中的位置
//        parentRv.getGlobalVisibleRect(parentVisibleRect)
//        excludeBottom?.let { parentVisibleRect.bottom = parentVisibleRect.bottom - it }
//        // 查找所有可以播放的配方Holder
//        for (index in 0..itemCount) {
//            // 查找对应的配方的ViewHolder
//            val holder = parentRv.findViewHolderForAdapterPosition(index)
//            (holder as? BAHolder)?.let {
//                if (checkChildRv) {
//                    it.getChildRv()?.adapter?.itemCount
//                }
//                if (holder.isBaEnable()) {
//                    tempAllList.add(holder)
//                    if (isItemVisibleMoreThanHalf(
//                            parentVisibleRect,
//                            innerItemVisibleRect,
//                            holder.getBaCompareView()
//                        )
//                    ) {
//                        tempValidList.add(holder)
//                    }
//                }
//            }
//        }
        // 优先选择之前没有播完的。
        var result =
            baNotCompleteEntity?.let { entity -> tempValidList.find { it.getHoldEntity() == entity } }
        if (result == null) {
            // 完整播完选择下一个继续播
            var nextToPlayId: Any? = null
            (baNotCompleteEntity ?: lastCompleteEntity)
                ?.let { id -> tempAllList.indexOfFirst { it.getHoldEntity() == id } }
                ?.takeIf { it >= 0 }?.let {
                    for (index in it..tempAllList.lastIndex) {
                        val baseBAViewHolder = tempAllList[index]
                        if (index > it && baseBAViewHolder.isBaResourceLoaded() && tempValidList.contains(
                                baseBAViewHolder
                            )
                        ) {
                            nextToPlayId = baseBAViewHolder.getHoldEntity()
                            break
                        }
                    }
                }
            // 都没有的话。默认选择第一个播
            result =
                tempValidList.find { it.getHoldEntity() == nextToPlayId && it.isBaResourceLoaded() }
                    ?: kotlin.run { tempValidList.find { it.isBaResourceLoaded() } }
        }
        return result
    }


    private fun checkRvItemIsVisible(
        rv: RecyclerView,
        baEnableList: MutableList<BAHolder>,
        baValidList: MutableList<BAHolder>,
        limitRect: Rect? = null,
    ) {
        val itemCount = rv.adapter?.itemCount ?: 0
        // 最外层Rv在屏幕中的位置
        rv.getGlobalVisibleRect(itemVisibleRect)
        itemVisibleRect.apply {
            limitRect?.let {
                left = left.coerceAtLeast(limitRect.left)
                right = right.coerceAtMost(limitRect.right)
                top = top.coerceAtLeast(limitRect.top)
                bottom = bottom.coerceAtMost(limitRect.bottom)
            }
        }
        for (index in 0..itemCount) {
            // 查找对应的配方的ViewHolder
            val holder = rv.findViewHolderForAdapterPosition(index)
            (holder as? BAHolder)?.let {
                if (holder.isBaEnable()) {
                    baEnableList.add(holder)
                    if (isItemVisibleMoreThanHalf(
                            itemVisibleRect,
                            innerItemVisibleRect,
                            holder.getBaCompareView()
                        )
                    ) {
                        baValidList.add(holder)
                    }
                }
            }
        }
    }

    private fun isItemVisibleMoreThanHalf(
        parentRect: Rect, childRect: Rect, childView: View,
        checkHor: Boolean = true, checkVertical: Boolean = true
    ): Boolean {
        childView.getGlobalVisibleRect(childRect)
        if (checkHor) {
            if (childRect.left <= parentRect.left) {
                childRect.left = childRect.right - childView.width
            } else {
                childRect.right = childRect.left + childView.width
            }
        }

        if (checkVertical) {
            if (childRect.top <= parentRect.top) {
                childRect.top = childRect.bottom - childView.height
            } else {
                childRect.bottom = childRect.top + childView.height
            }
        }

        val horVisible = childRect.centerX() >= parentRect.left
                && childRect.centerX() <= parentRect.right

        val verticalVisible = childRect.centerY() >= parentRect.top
                && childRect.centerY() <= parentRect.bottom

        return when {
            checkHor && checkVertical -> horVisible && verticalVisible
            checkHor -> horVisible
            checkVertical -> verticalVisible
            else -> false
        }
    }

    private var isResumeState: Boolean = true
    fun setAllPlayState(isResume: Boolean) {
        if (isResumeState == isResume) {
            return
        }
        isResumeState = isResume
        if (isResume) {
            notifyNextHolder2Play()
        } else {
            pauseBaAnimIfNeed()
        }
    }

}