package com.commsource.applanuch

import android.app.Application
import com.meitu.library.util.Debug.Debug
import com.meitu.mtlab.arkernelinterface.ARKernelInterfaceNativeBasicClass
import com.meitu.mtlab.arkernelinterface.core.ARKernelGlobalInterfaceJNI

class PixLibLoadLaunchTask2 : AbsLaunchTask() {

    override val executeOnMainThread = false

    override val isNeedWaitComplete = false
    override fun onInitial(app: Application) {
        try {
            ARKernelGlobalInterfaceJNI.setContext(app)
            ARKernelInterfaceNativeBasicClass.tryLoadLibrary()
        } catch (e: Throwable) {
            Debug.w(e)
        }
    }
}