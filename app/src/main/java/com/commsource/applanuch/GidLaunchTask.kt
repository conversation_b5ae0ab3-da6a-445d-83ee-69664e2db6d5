package com.commsource.applanuch

import android.app.Application
import com.commsource.advertisiting.UserPortraitStrategy
import com.commsource.config.ApplicationConfig
import com.commsource.statistics.Meepo
import com.meitu.hwbusinesskit.core.MTHWBusinessConfig
import com.meitu.library.analytics.base.logging.LogLevel
import com.meitu.library.analytics.gid.GidServer

class GidLaunchTask : AbsLaunchTask() {
    override fun onInitial(app: Application) {
        val overseasConfig = OverseasConfigHolder.getConfig(app)

        GidServer.setup(app)
            .setBaseTConfig(overseasConfig)
            .setLogLevel(LogLevel.DEBUG)
            .setGidChangedCallback { gidInfo ->
                ApplicationConfig.setOverseaGID(app, gidInfo?.id)
                MTHWBusinessConfig.setUserGid(gidInfo?.id)
                // 如果不是用户启动的就不要请求
                if (LaunchManager.isUserLaunch) {
                    Meepo.requestAbCodeByGid(gidInfo?.id)
                    UserPortraitStrategy.init()
                }
            }
            .start()
    }
}