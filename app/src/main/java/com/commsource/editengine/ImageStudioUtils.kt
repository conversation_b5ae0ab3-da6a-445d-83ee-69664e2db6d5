package com.commsource.editengine

import android.graphics.Bitmap
import android.graphics.Canvas
import com.commsource.beautyplus.R
import com.commsource.util.resColor
import com.pixocial.pixrendercore.params.PEBrushParams
import com.pixocial.pixrendercore.node.PEContext
import com.pixocial.pixrendercore.node.PEDRTypeEnum
import com.pixocial.pixrendercore.node.PEFrame
import com.pixocial.pixrendercore.node.PERenderImage
import com.pixocial.pixrendercore.tools.PEImageConvertUtils

/**
 * 部分配置PE的工具方法放置
 */
object ImageStudioUtils {

    /**
     * 生成抠图图片
     * @param cropImage 被抠图图片
     * @param maskImage 抠图mask
     */
    fun generateCutoutImage(cropImage: Bitmap, maskImage: Bitmap): Bitmap {
        // 判断是否要请求提示词
        val context = PEContext()
        val peFrame = PEFrame(context)
        val brush = PEBrushParams()
        brush.cutoutByMask = true
        peFrame.setupWithPEBaseImage(PEImageConvertUtils.peBaseImageFromBitmap(cropImage, false)!!, true)
        peFrame.setSingleParams(brush)
        brush.resetMaskImage(
            PEImageConvertUtils.peBaseImageFromBitmap(maskImage, false, 1)!!,
            true,
            mode = 0,
            reverseMask = false,
            dataAlpha = 1.0f
        )
        peFrame.process()
        val image = PEImageConvertUtils.bitmapFromPEBaseImage(peFrame.getImage().readBaseImage(), true)!!
        peFrame.release()
        context.release()
        return image
    }

    /**
     * 获取实例数量
     * @param image 被检测实例图片
     */
    fun getInstanceCount(image: Bitmap): Int {
        val context = PEContext()
        val peImage = PERenderImage(context)
        peImage.setDetectImage(PEImageConvertUtils.peBaseImageFromBitmap(image, false)!!, true)
        peImage.requestDetectResult(PEDRTypeEnum.DRType_Face)
        peImage.requestDetectResult(PEDRTypeEnum.DRType_Animal)
        val count = peImage.detectResult.faceResult.faces.size + peImage.detectResult.animalResult.animals.size
        peImage.release()
        context.release()
        return count
    }

    /**
     * 填充背景颜色
     * @param inputBitmap 输入图
     * @param color 背景颜色
     */
    fun fillBackground(inputBitmap: Bitmap, color: Int = R.color.white.resColor()): Bitmap {
        val outputBitmap =
            Bitmap.createBitmap(inputBitmap.width, inputBitmap.height, Bitmap.Config.ARGB_8888)
        Canvas(outputBitmap).apply {
            drawColor(color)
            drawBitmap(inputBitmap, 0f, 0f, null)
        }
        return outputBitmap
    }
}