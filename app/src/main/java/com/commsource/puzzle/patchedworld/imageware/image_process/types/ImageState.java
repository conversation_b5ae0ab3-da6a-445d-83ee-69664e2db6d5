package com.commsource.puzzle.patchedworld.imageware.image_process.types;

/**
 * 图像状态标签，用于标识某个图像处理状态
 * <p>
 * <NAME_EMAIL> on 2016/5/3.
 *
 * <AUTHOR> Lin
 */
public enum ImageState {
    /**
     * 原图，通常来自图片文件解码或者拍照数据生成，此图像通常作为图像处理流的初始节点
     */
    ORIGINAL("image_state_original"),
    /**
     * 适应屏幕尺寸缩小原图得到的版本，用于预览上的原始图，相对原图，预览通常更快；此图像通常作为预览图像处理流的初始节点
     */
    FIT_PREVIEW("image_state_fit_preview"),
    /**
     * 用于存放处理中间步骤的图像
     */
    INTERMEDIATE("image_state_intermediate"),
    /**
     * 用于存放处理完成的图像, 此图像通常作为图像处理流的最终节点
     */
    PROCESSED("image_state_processed"),
    /**
     * 预览图的处理图像，此图像通常作为预览图像处理流的最终节点
     */
    PREVIEW_PROCESSED("image_state_preview_processed"),
    /**
     * 美颜的预览图像
     */
    PREVIEW_SKIN_CARE("image_state_preview_skin_care"),
    /**
     * 预处理完成的预览图像
     */
    PREVIEW__PRE_PROCESSED("image_state_preview_pre_processed"),
    /**
     * 预处理完成的最终要保存的图像
     */
    PRE_PROCESSED("image_state_pre_processed"),
    /**
     * 特别状态，标记来自缓存状态
     */
    FROM_CACHE("image_state_from_cache");

    private String stateSummary;

    ImageState(String stateSummary) {
        this.stateSummary = stateSummary;
    }








}
