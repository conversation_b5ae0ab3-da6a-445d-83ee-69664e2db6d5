package com.commsource.homev3

import android.content.Context
import android.view.ViewGroup
import com.commsource.beautyplus.R
import com.commsource.widget.recyclerview.BaseItem

/**
 * @Description:
 *
 * @Author: vinvince, @Time: 2024/7/12 10:58
 */
class DailyRewardViewHolder(context: Context, parent: ViewGroup) :
    BaseHomeStateHolder<Int>(context, parent, R.layout.item_homev3_reward) {

    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<Int>?,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)
    }
}