package com.commsource.help

import android.app.Activity
import android.graphics.Color
import android.graphics.Rect
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.text.style.UnderlineSpan
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.ColorInt
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.commsource.album.BpAlbumJumpRouter
import com.commsource.album.XAlbumConfig
import com.commsource.beautyplus.BaseActivity
import com.commsource.beautyplus.BeautyPlusApplication
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.TicketFragmentBinding
import com.commsource.help.HelpActivity.Companion.BACK_FROM_CLOSE_BTN
import com.commsource.help.HelpActivity.Companion.KEY_BACK_FROM
import com.commsource.help.fragment.BaseHelpFragment
import com.commsource.util.ErrorNotifier
import com.commsource.util.ResourcesUtils
import com.commsource.util.dp
import com.commsource.util.dpf
import com.commsource.util.gone
import com.commsource.util.isVisible
import com.commsource.util.resColor
import com.commsource.util.setShapeStrokeColor
import com.commsource.util.text
import com.commsource.util.visible
import com.commsource.widget.dialog.delegate.BottomSheet
import com.commsource.widget.dialog.delegate.XDialog
import com.commsource.widget.recyclerview.AdapterDataBuilder
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.google.android.exoplayer2.text.span.SpanUtil
import com.meitu.common.AppContext
import com.meitu.common.utils.ToastUtils
import com.meitu.library.util.net.NetUtils
import com.pixocial.library.albumkit.media.MediaInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

class TicketFragment : BaseHelpFragment() {

    companion object {
        const val KEY_TICKET = "KEY_TICKET"
    }

    private val binding by lazy {
        TicketFragmentBinding.inflate(layoutInflater)
    }

    private val viewModel by lazy {
        ViewModelProvider(this)[TicketViewModel::class.java]
    }

    private val helpModel by lazy {
        ViewModelProvider(
            ownerActivity.application as BeautyPlusApplication,
            ViewModelProvider.AndroidViewModelFactory.getInstance(ownerActivity.application)
        )[HelpCenterViewModel::class.java]
    }

    private val activityResultLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.apply {
                val backReason = getIntExtra(KEY_BACK_FROM, 0)
                if (backReason == BACK_FROM_CLOSE_BTN) {
                    helpModel.backToSettingEvent.value = true
                }
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        initUI()
        initListener()
        checkNetworkAndRetry {}
    }

    private fun initUI() {
        binding.attachList.apply {
            layoutManager =
                LinearLayoutManager(ownerActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = BaseRecyclerViewAdapter(this@TicketFragment).apply {
                if (!TextUtils.isEmpty(helpModel.mAdSnapshotPath)) {//广告截屏的path,构造的MediaInfo缺少很多信息，这里只用了mediaPath
                    var mediaInfo = MediaInfo()
                    mediaInfo.mediaPath = helpModel.mAdSnapshotPath
                    var file = File(helpModel.mAdSnapshotPath)
                    if (file.exists()) {
                        updateItemEntities(
                            AdapterDataBuilder.create()
                                .addEntities(listOf(1), AddMediaViewHolder::class.java)
                                .addEntities(listOf(mediaInfo), MediaItemViewHolder::class.java)
                                .build(), false
                        )
                    } else {//如果图片不存在
                        setSingleItemEntities(listOf(1), AddMediaViewHolder::class.java)
                    }
                } else {
                    setSingleItemEntities(listOf(1), AddMediaViewHolder::class.java)
                }

                setOnEntityClickListener(Integer::class.java) { pos, entity ->
                    val lastData = items.filter { it.entity is MediaInfo }.map { it.entity }
                    BpAlbumJumpRouter.returnMediasToHelpCenter(
                        ownerActivity,
                        lastData = (lastData as? List<MediaInfo>) ?: emptyList(),
                        XAlbumConfig.MultiSelectSetting(
                            minSelectCount = 1,
                            maxSelectCount = 9,
                            selectOverCountTips = R.string.t_max_video_image.text(),
                            confirmDesc = R.string.t_continue.text(),
                            enableAdjustMedia = false
                        )
                    ) {
                        updateItemEntities(
                            AdapterDataBuilder.create()
                                .addEntities(listOf(1), AddMediaViewHolder::class.java)
                                .addEntities(it, MediaItemViewHolder::class.java).build(), false
                        )
                    }
                    true
                }

                setOnItemChildClickListener { position, item, view ->
                    items.removeAt(position)
                    notifyItemRemoved(position)
                }
            }

            addItemDecoration(object : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State
                ) {
                    val pos = parent.getChildAdapterPosition(view)
                    val count = parent.adapter?.itemCount ?: 0
                    var start = 0
                    var end = 0
                    when {
                        pos == 0 -> start = 0
                        pos > 0 && pos < count - 1 -> start = 8.dp()
                        else -> {
                            start = 8.dp()
                            end = 24.dp()
                        }
                    }
                    outRect.set(start, 0, end, 0)
                }
            })
        }

        GradientDrawable(
            GradientDrawable.Orientation.LEFT_RIGHT,
            intArrayOf(
                R.color.color_fd5a5c.resColor(),
                R.color.color_fe537f.resColor(),
                R.color.color_ff48b1.resColor()
            )
        ).apply {
            cornerRadius = 25.dpf()
        }.also {
            binding.submit.background = it
            binding.submittedPage.doneBtn.background = it
        }
        refreshSubmitBtnStatus()

        // 超链接
        binding.servicePrompt.movementMethod = LinkMovementMethod.getInstance()
        binding.servicePrompt.text =
            viewModel.transformToSpan(
                R.string.v7130_A_2.text(),
                activityResultLauncher = activityResultLauncher
            )
        binding.edmServicePrompt.movementMethod = LinkMovementMethod.getInstance()
        binding.edmServicePrompt.text =
            viewModel.emailSpan(activityResultLauncher = activityResultLauncher)

        updatePromptText()
    }

    private fun initListener() {
        // 切换的时候默认选中第一个
        if (helpModel.formItem.isNotEmpty()) {
            helpModel.selectForm = helpModel.formItem.first()
            helpModel.selectIssue =
                helpModel.issueItem[helpModel.selectForm]?.second?.elementAtOrNull(0)
        }

        // 初始化展示。
        helpModel.selectForm?.let {
            binding.issueCatContent.text = it.display_name
        }
        helpModel.selectIssue?.let {
            binding.issueContent.text = it.name
        } ?: kotlin.run {
            helpModel.selectForm?.let {
                binding.issueContent.text = it.display_name
            }
        }

        binding.emailContent.doAfterTextChanged {
            refreshSubmitBtnStatus()
        }

        binding.descContent.doAfterTextChanged {
            refreshSubmitBtnStatus()
        }

        binding.attachLog.isSelected = false
        binding.attachLog.setImageResource(R.drawable.radius_8_black20)
        binding.logCheckContainer.setOnClickListener {
            binding.attachLog.isSelected = !binding.attachLog.isSelected
            if (binding.attachLog.isSelected) {
                binding.attachLog.setImageResource(R.drawable.save_to_my_work_checked)
            } else {
                binding.attachLog.setImageResource(R.drawable.radius_8_black20)
            }
        }

        binding.checkEdm.isSelected = true
        binding.checkEdm.setImageResource(R.drawable.save_to_my_work_checked)
        binding.edmCheckContainer.setOnClickListener {
            binding.checkEdm.isSelected = !binding.checkEdm.isSelected
            if (binding.checkEdm.isSelected) {
                binding.checkEdm.setImageResource(R.drawable.save_to_my_work_checked)
            } else {
                binding.checkEdm.setImageResource(R.drawable.radius_8_black20)
            }
        }

        binding.issueCatContent.setOnClickListener {
            showDropDownDialog(
                R.string.t_issue_cat.text(),
                helpModel.formItem.map { it.display_name }
                    .filter { it?.isNotEmpty() == true }) { name ->
                binding.issueCatContent.text = name
                helpModel.selectForm = helpModel.formItem.find { it.display_name == name }
                // 切换的时候默认选中第一个
                helpModel.selectIssue =
                    helpModel.issueItem[helpModel.selectForm]?.second?.elementAtOrNull(0)
                binding.issueContent.text = helpModel.selectIssue?.name ?: name
                binding.issueContent.setShapeStrokeColor(1.dp(), Color.TRANSPARENT)
                binding.issueCatContent.setShapeStrokeColor(1.dp(), Color.TRANSPARENT)

                updateReasonGroupVisible()
                refreshSubmitBtnStatus()
            }
        }

        binding.issueContent.setOnClickListener {
            if (helpModel.selectIssue == null) {
                helpModel.selectForm?.let { form ->
                    showDropDownDialog(
                        R.string.t_issue.text(),
                        listOf(form.display_name).filter { it?.isNotEmpty() == true }) { name ->
                        binding.issueContent.text = name

                        updateReasonGroupVisible()
                        refreshSubmitBtnStatus()
                    }
                }
            } else {
                helpModel.selectForm?.let { form ->
                    helpModel.issueItem[form]?.second?.map { it.name }?.let {
                        showDropDownDialog(
                            R.string.t_issue.text(),
                            it.filter { it?.isNotEmpty() == true }) { name ->
                            binding.issueContent.text = name
                            helpModel.selectIssue =
                                helpModel.issueItem[form]?.second?.find { it.name == name }

                            updateReasonGroupVisible()
                            refreshSubmitBtnStatus()
                        }
                    }
                }
            }
        }

        binding.reasonContent.setOnClickListener {
            helpModel.reasonItems.map { it.name }.let {
                showDropDownDialog(
                    R.string.v7130_A_3.text(),
                    it.filter { it?.isNotEmpty() == true }) { name ->
                    binding.reasonContent.text = name
                    helpModel.selectReason = helpModel.reasonItems.find { it.name == name }
                    refreshSubmitBtnStatus()
                }
            }
        }

        binding.submit.setOnClickListener {
            val email = binding.emailContent.editableText.toString()
            when {
                !viewModel.isValidEmail(email) -> {
                    ToastUtils.showShortToast(R.string.t_fill_email)
                    binding.emailContent.setShapeStrokeColor(
                        1.dp(),
                        ResourcesUtils.getColor(R.color.color_ff5353)
                    )
                }

                TextUtils.isEmpty(binding.issueCatContent.text) -> {
                    ToastUtils.showShortToast(R.string.t_fill_issueCat)
                    binding.issueCatContent.setShapeStrokeColor(
                        1.dp(),
                        ResourcesUtils.getColor(R.color.color_ff5353)
                    )
                }

                TextUtils.isEmpty(binding.issueContent.text) -> {
                    ToastUtils.showShortToast(R.string.t_fill_issueitem)
                    binding.issueContent.setShapeStrokeColor(
                        1.dp(),
                        ResourcesUtils.getColor(R.color.color_ff5353)
                    )
                }

                TextUtils.isEmpty(binding.descContent.editableText.toString()) -> {
                    ToastUtils.showShortToast(R.string.t_desc_content)
                    binding.descContent.setShapeStrokeColor(
                        1.dp(),
                        ResourcesUtils.getColor(R.color.color_ff5353)
                    )
                }

                else -> {
                    val mediaList =
                        (binding.attachList.adapter as? BaseRecyclerViewAdapter)?.items?.filter {
                            it.entity is MediaInfo
                        }?.map {
                            (it.entity as MediaInfo).mediaPath
                        }
                    (ownerActivity as? BaseActivity)?.showLoadingDialog()
                    lifecycleScope.launch(Dispatchers.IO) {
                        val result = viewModel.submitTicket(
                            mediaList,
                            binding.attachLog.isSelected,
                            email,
                            HelpServer.BRAND_ID.toString(),
                            binding.descContent.editableText.toString(),
                            helpModel.selectForm,
                            helpModel.issueItem[helpModel.selectForm]?.first,
                            helpModel.selectIssue,
                            reasonId = helpModel.reasonItemId,
                            reasonItem = if (binding.groupReason.isVisible) {
                                helpModel.selectReason
                            } else {
                                null
                            },
                            edmCheckBoxChecked = if (binding.edmContainer.isVisible) {
                                binding.checkEdm.isSelected
                            } else {
                                null
                            },
                            edmCheckId = helpModel.checkboxItem?.id
                        )
                        withContext(Dispatchers.Main) {
                            (ownerActivity as? BaseActivity)?.dismissLoadingDialog()
                            if (result) {
                                binding.submittedPage.root.visible()
                            } else {
                                ErrorNotifier.showNetworkRetryDialog({
                                    binding.submit.performClick()
                                })
                            }
                        }
                    }
                }
            }
        }

        binding.emailContent.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                binding.emailContent.setShapeStrokeColor(
                    1.dp(),
                    ResourcesUtils.getColor(R.color.Gray_A)
                )
            } else {
                binding.emailContent.setShapeStrokeColor(1.dp(), Color.TRANSPARENT)
            }
        }

        binding.descContent.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                binding.descContent.setShapeStrokeColor(
                    1.dp(),
                    ResourcesUtils.getColor(R.color.Gray_A)
                )
            } else {
                binding.descContent.setShapeStrokeColor(1.dp(), Color.TRANSPARENT)
            }
        }

        // 提交成功之后的按钮
        binding.submittedPage.doneBtn.setOnClickListener {
            helpModel.backToSettingEvent.value = true
        }

        binding.submittedPage.moreIssue.setOnClickListener {
            helpModel.backHomeEvent.value = true
        }

        binding.emailContent.setOnEditorActionListener { v, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEND
                || actionId == EditorInfo.IME_ACTION_DONE
                || actionId == EditorInfo.IME_ACTION_NEXT
                || (event != null && KeyEvent.KEYCODE_ENTER == event.keyCode && KeyEvent.ACTION_DOWN == event.action)
            ) {
                binding.descContent.requestFocus()
            }
            true
        }

        helpModel.showEdmCheckBox.observe(viewLifecycleOwner) {
            if (it == true) {
                binding.attachLog.isSelected = true
                binding.attachLog.setImageResource(R.drawable.save_to_my_work_checked)
                binding.edmContainer.visible()
            } else {
                binding.edmContainer.gone()
            }
        }
    }

    private fun updatePromptText() {
        binding.emailTitle.text = getColorSpan(binding.emailTitle.text.toString().plus("*"))
        binding.issueCatTitle.text = getColorSpan(binding.issueCatTitle.text.toString().plus("*"))
        binding.issueTitle.text = getColorSpan(binding.issueTitle.text.toString().plus("*"))
        binding.reasonTitle.text = getColorSpan(binding.reasonTitle.text.toString().plus("*"))
        binding.descTitle.text = getColorSpan(binding.descTitle.text.toString().plus("*"))
    }

    private fun getColorSpan(text: String): SpannableString {
        val spannableString = SpannableString(text)
        setColorSpan(
            spannableString,
            text.length - 1,
            text.length,
            R.color.color_fa64b0.resColor()
        )
        return spannableString
    }

    private fun setColorSpan(
        spannableString: SpannableString,
        start: Int,
        end: Int,
        @ColorInt color: Int
    ) {
        spannableString.setSpan(
            object : UnderlineSpan() {
                override fun updateDrawState(ds: TextPaint) {
                    ds.color = color
                }
            },
            start, end, Spanned.SPAN_INCLUSIVE_EXCLUSIVE
        )
    }

    private fun updateReasonGroupVisible() {
        val isVisible = helpModel.selectIssue?.value == "request_a_refund"
                && helpModel.reasonItems.isNotEmpty()
        if (isVisible) {
            binding.groupReason.visible()
        } else {
            binding.groupReason.gone()
        }
    }

    private fun refreshSubmitBtnStatus() {
        val isEnable = binding.issueCatContent.text.isNotEmpty()
                && binding.issueContent.text.isNotEmpty()
                && binding.emailContent.text.isNotEmpty()
                && binding.descContent.text.isNotEmpty()
                && (!binding.groupReason.isVisible || binding.reasonContent.text.isNotEmpty())
        binding.submit.alpha = if (isEnable) 1.0f else 0.5f
        binding.submit.isEnabled = isEnable
    }

    // 显示下拉弹窗
    private fun showDropDownDialog(
        title: String,
        list: List<String>,
        listener: ((str: String) -> Unit)? = null
    ) {
        XDialog {
            BottomSheet {
                sheetTitle = title
                items = list
                onItemClick = listener
            }
        }.show(ownerActivity)
    }


    private fun checkNetworkAndRetry(onNetworkAvailable: () -> Unit) {
        if (!NetUtils.canNetworking(AppContext.context)) {
            ErrorNotifier.showNetworkRetryDialog({
                checkNetworkAndRetry(onNetworkAvailable)
            })
        } else {
            onNetworkAvailable.invoke()
        }
    }
}