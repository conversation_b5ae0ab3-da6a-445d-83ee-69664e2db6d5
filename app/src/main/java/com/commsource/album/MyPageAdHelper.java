package com.commsource.album;

import android.app.Activity;
import android.view.View;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;

import com.commsource.ad.AdConstantsKt;
import com.commsource.ad.AdPreloader;
import com.commsource.ad.AdSlotIds;
import com.commsource.beautyplus.R;
import com.commsource.beautyplus.util.DFPAdUtils;
import com.commsource.config.SubscribeConfig;
import com.commsource.statistics.MTFirebaseAnalyticsAgent;
import com.commsource.statistics.constant.MTFirebaseConstant;
import com.meitu.hwbusinesskit.core.HWBusinessSDK;
import com.meitu.hwbusinesskit.core.ad.NativeAd;
import com.meitu.hwbusinesskit.core.bean.AdData;
import com.meitu.hwbusinesskit.core.bean.AdSlot;
import com.meitu.hwbusinesskit.core.listener.OnAdListener;
import com.meitu.hwbusinesskit.core.widget.NativeAdView;

/**
 * Created by luohaoyang on 2018/6/11.
 */
public class MyPageAdHelper {
    private Activity mActivity;
    private NativeAd mNativeAd;
    private boolean mHasGotoAdUrl;
    private NativeAdView mNativeAdView;
    private OnAdListener mOnAdListener;
    private View mAdContainer;

    public MyPageAdHelper(@NonNull Activity activity, @NonNull View adContainer, @NonNull NativeAdView adView,
        OnAdListener listener) {
        mActivity = activity;
        mNativeAdView = adView;
        mOnAdListener = listener;
        mAdContainer = adContainer;
    }

    public void onAdClick(AdData adData) {
        if (mHasGotoAdUrl) {
            return;
        }
        mHasGotoAdUrl = true;
        DFPAdUtils.onAdClick(mActivity, adData);
    }

    public void onAddThirdPartyNativeAdView(AdData adData, View thirdPartyNativeAdView) {
        if (adData.getAdType().equals(AdSlot.TYPE_BANNER)) {
            RelativeLayout.LayoutParams bannerParameters = new RelativeLayout.LayoutParams(
                RelativeLayout.LayoutParams.WRAP_CONTENT, RelativeLayout.LayoutParams.WRAP_CONTENT);
            bannerParameters.addRule(RelativeLayout.CENTER_IN_PARENT);
            mNativeAdView.removeAllViews();
            mNativeAdView.addThirdPartyNativeAdView(thirdPartyNativeAdView, bannerParameters);
        }
    }

    /**
     * 展示广告
     */
    public void doShowAd(boolean update) {
        if (AdPreloader.INSTANCE.isNeedDisableAd()) {
            mAdContainer.setVisibility(View.GONE);
            return;
        }
        if (mNativeAd == null || update) {
            if (mNativeAd != null) {
                mNativeAd.setOnAdListener(null);
                mNativeAd.destroy();
            }
            mNativeAd = HWBusinessSDK.getNativeAd(AdSlotIds.ad_album_down);
            mAdContainer.setVisibility(mNativeAd.hasCacheAd() ? View.VISIBLE : View.GONE);
            mNativeAd.setOnAdListener(mOnAdListener);
            mNativeAd.show(mActivity,mNativeAdView);
            mHasGotoAdUrl = false;
            MTFirebaseAnalyticsAgent.logEvent(mActivity, MTFirebaseConstant.EVENT_AD_ALBUM_PAGE_SHOW, null);
        }
    }

    public boolean hasCacheAd() {
        return mNativeAd != null && mNativeAd.hasCacheAd();
    }

    /**
     * 销毁广告
     */
    public void destroyAd() {
        if (mNativeAd != null) {
            mNativeAd.setOnAdListener(null);
            mNativeAd.destroy();
            mNativeAd = null;
        }
        mAdContainer.setVisibility(View.GONE);
    }

}
