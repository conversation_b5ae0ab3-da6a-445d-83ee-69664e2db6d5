package com.commsource.airepair.imagequality.view

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.graphics.RectF
import androidx.core.graphics.drawable.toBitmap
import com.commsource.beautyplus.R
import com.commsource.util.dpf
import com.commsource.util.resColor
import com.commsource.airepair.easytimeline.BaseRender
import com.meitu.library.util.app.ResourcesUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 简化的裁剪器渲染器
 */
class SimpleCropperRender(val height: Int, private val verticalDrawOffset: Float) : BaseRender() {

    private var leftHandle: Bitmap? = null
    private var rightHandle: Bitmap? = null

    private val borderWidth = 2.dpf
    private val timeTextMargin = 4.dpf

    private var timeMSTextWidth = 0f // 分秒宽度
    private var timeHMSTextWidth = 0f // 时分秒宽度
    private var drawTextBaseLine = 0f

    /**
     * 裁剪开始点/结束点
     * 范围限定在了屏幕范围内。
     */
    var cropStartPos: Float = 0f
    var cropEndPos: Float = 0f

    var maxCropWidth: Float = 0f
    var maxCropTime: Long = 0

    /**
     * 触摸区域
     */
    private var touchAreaRect = RectF()

    private val cropperPaint = Paint().apply {
        color = Color.WHITE
        strokeWidth = borderWidth
        style = Paint.Style.STROKE
    }

    private val maskPaint = Paint().apply {
        color = R.color.black50.resColor()
        style = Paint.Style.FILL
    }

    private val bgPaint = Paint().apply {
        color = R.color.color_80000000.resColor()
        style = Paint.Style.FILL
    }

    private val maskRect = RectF()

    /**
     * 裁剪时间的背景区域
     */
    private val timeBgRect = RectF()
    private val timeBgPath = Path()

    /**
     * 裁剪时间显示
     */
    private val cropperTimeTextPaint = Paint().apply {
        textSize = 10.dpf
        color = Color.WHITE
        isAntiAlias = true
    }

    suspend fun initialCropperRender(maxCropWidth: Float, maxCropTime: Long) {
        this.maxCropWidth = maxCropWidth
        this.maxCropTime = maxCropTime
        withContext(Dispatchers.IO) {
            leftHandle = ResourcesUtils.getDrawable(R.drawable.crop_left_handle).toBitmap()
            rightHandle = ResourcesUtils.getDrawable(R.drawable.crop_right_handle).toBitmap()

            timeMSTextWidth = cropperTimeTextPaint.measureText("00:00")
            timeHMSTextWidth = cropperTimeTextPaint.measureText("00:00:00")

            val fontMetrics: Paint.FontMetrics = cropperTimeTextPaint.fontMetrics
            val distance = (fontMetrics.bottom - fontMetrics.top) / 2 - fontMetrics.bottom
            drawTextBaseLine = 4.dpf + distance
        }
    }

    private fun drawTime(canvas: Canvas) {
        val curCropTime = ((cropEndPos - cropStartPos) / maxCropWidth * maxCropTime).toLong()
        timeBgRect.apply {
            right = cropEndPos - 1.dpf
            left = right - (if (curCropTime >= 3600000) timeHMSTextWidth else timeMSTextWidth) - timeTextMargin
            top = verticalDrawOffset + 2.dpf
            bottom = top + 15.dpf
        }
        // 超出就不绘制了
        if (timeBgRect.width() >= cropEndPos - cropStartPos) return

        timeBgPath.reset()
        timeBgPath.moveTo(timeBgRect.left, timeBgRect.top)
        timeBgPath.lineTo(timeBgRect.right, timeBgRect.top)
        timeBgPath.lineTo(timeBgRect.right, timeBgRect.bottom)
        timeBgPath.lineTo(timeBgRect.left + borderWidth, timeBgRect.bottom)
        timeBgPath.arcTo(
            timeBgRect.left, timeBgRect.bottom - borderWidth * 4,
            timeBgRect.left + borderWidth * 4, timeBgRect.bottom,
            90f, 90f,
            false
        )
        timeBgPath.lineTo(timeBgRect.left, timeBgRect.top + 2.dpf)
        timeBgPath.close()
        canvas.drawPath(timeBgPath, bgPaint)
        canvas.drawText(
            getTimeText(curCropTime),
            cropEndPos - timeTextMargin / 2f - if (curCropTime >= 3600000) timeHMSTextWidth else timeMSTextWidth,
            drawTextBaseLine + verticalDrawOffset + timeTextMargin, 
            cropperTimeTextPaint
        )
    }

    /**
     * 绘制裁剪工具
     */
    fun drawCropper(canvas: Canvas, horizontalOffset: Float, maxHorizontalOffset: Float) {
        val tempLeftHandle = leftHandle
        val tempRightHandle = rightHandle
        if (tempLeftHandle == null || tempRightHandle == null) return
        
        // 绘制裁剪的边框
        canvas.drawRoundRect(
            cropStartPos, verticalDrawOffset + borderWidth / 2f, cropEndPos,
            verticalDrawOffset + height - borderWidth / 2f, 2.dpf, 2.dpf, cropperPaint
        )
        
        // 绘制黑色遮罩
        var isNeedCorner = horizontalOffset < 0f
        maskRect.apply {
            left = if (horizontalOffset < 0f) -horizontalOffset else 0f
            top = verticalDrawOffset
            right = cropStartPos
            bottom = top + tempLeftHandle.height
        }
        if (isNeedCorner) {
            canvas.drawRoundRect(maskRect, 6.dpf, 6.dpf, maskPaint)
        } else {
            canvas.drawRect(maskRect, maskPaint)
        }

        isNeedCorner = (maxHorizontalOffset - horizontalOffset) < (canvas.width - cropEndPos)
        maskRect.apply {
            left = cropEndPos
            top = verticalDrawOffset
            bottom = top + tempLeftHandle.height
            right = cropEndPos + ((maxHorizontalOffset - horizontalOffset).coerceAtMost(canvas.width - cropEndPos))
        }
        if (isNeedCorner) {
            canvas.drawRoundRect(maskRect, 6.dpf, 6.dpf, maskPaint)
        } else {
            canvas.drawRect(maskRect, maskPaint)
        }
        
        // 绘制左右Handle
        canvas.drawBitmap(tempLeftHandle, cropStartPos - tempLeftHandle.width, verticalDrawOffset, null)
        canvas.drawBitmap(tempRightHandle, cropEndPos, verticalDrawOffset, null)
        canvas.drawLine(cropStartPos, verticalDrawOffset, cropStartPos, verticalDrawOffset + tempLeftHandle.height, cropperPaint)
        canvas.drawLine(cropEndPos, verticalDrawOffset, cropEndPos, verticalDrawOffset + tempLeftHandle.height, cropperPaint)
        
        // 绘制裁剪时间（无背景）
        drawTime(canvas)
    }

    /**
     * 是否触摸到裁剪区域
     */
    fun isTouchInLeftCropHandle(touchX: Float, touchY: Float): Boolean {
        touchAreaRect.set(
            cropStartPos - (leftHandle?.width?.toFloat() ?: 0f), verticalDrawOffset,
            cropStartPos, verticalDrawOffset + (leftHandle?.height?.toFloat() ?: 0f)
        )
        return touchAreaRect.contains(touchX, touchY)
    }

    fun isTouchInRightCropHandle(touchX: Float, touchY: Float): Boolean {
        touchAreaRect.set(
            cropEndPos, verticalDrawOffset,
            cropEndPos + (rightHandle?.width?.toFloat() ?: 0f),
            verticalDrawOffset + (rightHandle?.height?.toFloat() ?: 0f)
        )
        return touchAreaRect.contains(touchX, touchY)
    }
} 