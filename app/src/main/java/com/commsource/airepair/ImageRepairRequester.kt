package com.commsource.airepair

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Base64
import com.commsource.config.ApplicationConfig
import com.commsource.util.print
import com.meitu.http.AbsRequest
import com.meitu.http.XHttp
import com.meitu.http.kotex.Get
import com.meitu.http.kotex.Parameter
import com.meitu.http.kotex.Post
import com.meitu.http.kotex.XSign
import com.meitu.http.kotex.response
import com.meitu.library.util.io.StreamUtils
import kotlinx.coroutines.suspendCancellableCoroutine
import java.net.URL
import java.util.Timer
import java.util.TimerTask

class ImageRepairRequester {

    private val timer = Timer()
    private var queryRequest: AbsRequest? = null
    private var commitRequest: AbsRequest? = null

    @Volatile
    private var isCanceled: Boolean = false

    @Volatile
    private var isTimerStart: Boolean = false

    /**
     * 取消处理。
     */
    private fun doCancel() {
        isCanceled = true
        timer.cancel()
        queryRequest?.cancel()
        commitRequest?.cancel()
    }

    /**
     * 照片修复
     */
    suspend fun imageAIRepair(
        imageUrl: String,
        oriWidth: Int,
        oriHeight: Int,
        type: Int = ImageQuality.Quality_HIGH, // 0 高清,
        progressCallback: ((progress: Int) -> Unit)? = null
    ) = suspendCancellableCoroutine { continuation ->
        // 协程被取消的时候，自动取消请求和计算时间
        try {
            continuation.invokeOnCancellation {
                doCancel()
            }
            val url = if (ApplicationConfig.isTestMaterialEnvironment()) {
                "${XHttp.OPEN_API_DEBUG_HOST}/v3/bp/image_restoration_ai"
            } else {
                "https://v2.lab.pixocial.com/v3/bp/image_restoration_ai"
            }
            // 发起请求。
            Post<MsgResponse>(requestMapping = url) {
                val entity = when (type) {
                    ImageQuality.Quality_HIGH -> ImageAiParameter(
                        ImageHDParameter(
                            ImageHDConfig(
                                2,
                                oriWidth,
                                oriHeight
                            )
                        ), null
                    )

                    ImageQuality.Quality_ULTRA -> ImageAiParameter(
                        null, ImageHDPlusParameter(
                            ImageHDConfig(3, oriWidth, oriHeight)
                        )
                    )

                    else -> ImageAiParameter(
                        null,
                        ImageHDPlusParameter(ImageHDConfig(4, oriWidth, oriHeight))
                    )
                }
                removeDefaultRequestAdapter()
                XSign()
                Parameter {
                    this["parameter"] = entity
                    this["media_info_list"] = listOf(MediaInfo(imageUrl, MediaProfiles("url")))
                }
            }.apply {
                commitRequest = absRequest
            }.responseOnBackground().synRequest().response {
                onNext = {
                    // 构建轮询
                    var result: Bitmap? = null
                    if (!isCanceled) {
                        // 开始计时。
                        if (!isTimerStart) {
                            isTimerStart = true
                            timer.schedule(object : TimerTask() {
                                var count = 0
                                override fun run() {
                                    count++
                                    val value = when {
                                        count < 5 -> count * 4
                                        count < 15 -> 25 + (count - 5) * 2
                                        else -> (45 + (count - 15)).coerceAtMost(80)
                                    }
                                    progressCallback?.invoke(value)
                                }
                            }, 0, 300)
                        }
                        result = requestImageProcessResult(it.msgId)
                    }
                    timer.cancel()
                    progressCallback?.invoke(100)
                    continuation.resumeWith(Result.success(result))
                }
                onError = {
                    it.printStackTrace()
                    timer.cancel()
                    progressCallback?.invoke(100)
                    continuation.resumeWith(Result.success(null))
                }
            }
        } catch (e: Exception) {
            timer.cancel()
            progressCallback?.invoke(100)
            continuation.resumeWith(Result.success(null))
            e.printStackTrace()
        }
    }


    private fun requestImageProcessResult(msgId: String): Bitmap? {
        var bmp: Bitmap? = null
        Get<LabImageResponse>("https://v2.lab.pixocial.com/v1/query?msg_id=${msgId}") {
            log()
            removeDefaultRequestAdapter()
            XSign()
        }.apply { queryRequest = absRequest }.responseOnBackground().synRequest().response {
            onNext = {
                when (it.error_code) {
                    0 -> {
                        if (!isCanceled) {
                            bmp = parseImageResponse(it)
                        }
                    }

                    29901 -> {
                        Thread.sleep(2000)
                        if (!isCanceled) {
                            bmp = requestImageProcessResult(msgId)
                        }
                    }
                }
            }
        }
        return bmp
    }


    /**
     * 解析图片返回数据。
     */
    private fun parseImageResponse(response: LabImageResponse): Bitmap? {
        return try {
            response.media_info_list.elementAtOrNull(0)?.let {
                if (it.media_profiles.media_data_type == "url") {
                    val labMaskStream = URL(it.media_data).openStream()
                    BitmapFactory.decodeStream(labMaskStream).also {
                        StreamUtils.close(labMaskStream)
                    }
                } else {
                    val byteData =
                        Base64.decode(it.media_data.toByteArray(), Base64.DEFAULT)
                    BitmapFactory.decodeByteArray(byteData, 0, byteData.size)
                }
            }
        } catch (e: Exception) {
            null
        }
    }

}