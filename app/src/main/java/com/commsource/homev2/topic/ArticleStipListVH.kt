package com.commsource.homev2.topic

import android.content.Context
import android.view.ViewGroup
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemArticleListVhBinding
import com.commsource.homev2.entity.BannerType
import com.commsource.homev2.entity.MixMaterials
import com.commsource.homev2.entity.RecommendMaterial
import com.commsource.util.ViewShowState
import com.commsource.util.common.BaseCallback2
import com.commsource.util.dpf
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.commsource.widget.recyclerview.BaseViewHolder
import com.pixocial.androidx.core.extension.dp

class ArticleStipListVH(val context: Context, parent: ViewGroup) :
    TopicBaseVH<MixMaterials>(context, parent, R.layout.item_article_list_vh) {

    companion object {
        val Item_Height = 120.dp
    }

    private val viewModel by lazy {
        ViewModelProvider(context as FragmentActivity)[TopicViewModel::class.java]
    }

    private val binding = ItemArticleListVhBinding.bind(itemView)
    private val baseAdapter by lazy { BaseRecyclerViewAdapter(context) }
    private val visibleTracker = VisibleTracker()
    private val visibleCallback =
        BaseCallback2<Int, BaseViewHolder<RecommendMaterial>> { t, holder ->
            if (t == ViewShowState.SHOW_COMPLETE) {
                (holder)?.item?.entity?.let {
                    viewModel.logEvent(item.entity.materialType, it.materialId)
                }
            }
        }

    private val scrollListener = object : RecyclerView.OnScrollListener() {
        var scrollX = 0
        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
            scrollX += dx
            viewModel.recordScrollMap.put(bindingAdapterPosition, scrollX)
            if (dx > 0) {
                visibleTracker.checkVisibleState(
                    binding.materialList,
                    VisibleTracker.CheckDir.BOTH,
                    useGlobalLimit = true,
                    callback2 = visibleCallback
                )
            }
        }
    }

    init {
        binding.executePendingBindings()
    }

    override fun onViewHolderCreated() {
        val layoutParams = binding.materialList.layoutParams
        layoutParams.height = Item_Height
        binding.materialList.layoutParams = layoutParams
    }

    override fun onBindViewHolder(position: Int, item: BaseItem<MixMaterials>, payloads: List<Any>?) {
        super.onBindViewHolder(position, item, payloads)
        binding.materialList.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = baseAdapter.apply {
                baseAdapter.addTag(SIZE, fetchItemSize())
                addTag(SpecifyColor, getSpecifyBgColor())
                addTag(CornerRadius, 12.dpf)
                item.entity.materials?.forEach { it.parentBannerType = item.entity.materialType }
                setSingleItemEntities(item.entity.materials, CommonTopicMaterialVH::class.java)
                setOnEntityClickListener(RecommendMaterial::class.java) { pos, entity ->
                    viewModel.onClickEntity(context as FragmentActivity, entity)
                    true
                }
            }
            if (itemDecorationCount <= 0) {
                addItemDecoration(ArticleTopicItemDecoration())
            }
            val lastScrollX = viewModel.recordScrollMap[bindingAdapterPosition]
            post {
                scrollListener.scrollX = 0
                scrollBy(lastScrollX, 0)
            }
            removeOnScrollListener(scrollListener)
            addOnScrollListener(scrollListener.also { it.scrollX = 0 })
        }
    }

    private fun fetchItemSize(): Pair<Int, Int> {
        return Pair(Item_Height, Item_Height)
    }

}