package com.commsource.widget;

import android.content.Context;
import android.telephony.TelephonyManager;
import android.text.TextUtils;

import com.commsource.config.ApplicationConfig;
import com.commsource.util.BPLocationUtils;
import com.meitu.common.AppContext;
import com.meitu.library.util.Debug.Debug;

/**
 * Created by luohaoyang on 2018/7/9.
 */

public class GDPRUtils {
    private static volatile Boolean mIsEuroArea;

    public static void init(Context context) {
        if (mIsEuroArea != null) {
            return;
        }
        synchronized (GDPRUtils.class) {
            if (mIsEuroArea != null) {
                return;
            }
            try {
                mIsEuroArea = getIsEuroArea(context);
            } catch (Exception e) {
                Debug.e(e.getMessage());
                mIsEuroArea = false;
            }
        }

    }

    public static boolean getIsEuroArea(Context context) {
        // 判断当前是否有定位信息。
        if (hasCertainLocation(context)) {
            // 如果有，直接通过定位判断。
            return BPLocationUtils.isEuropeanUnion(context);
        } else {
            // 否则如果用户有选择非欧洲地区，则判断为非欧。
            return !ApplicationConfig.isBool(context, ApplicationConfig.USER_SELECT_NO_EURO_AREA);
        }
    }

    public static void setIsEuroArea(boolean isEuroArea) {
        mIsEuroArea = isEuroArea;
    }

    /**
     * 判断用户是否是GDPR国家,默认是欧洲。
     * 如果用户选择了非欧洲地区且没有明确定位，判断为非欧洲。
     *
     * @return
     */
    public static boolean isEuroArea(Context context) {
        if (mIsEuroArea != null) {
            return mIsEuroArea;
        } else {
            init(context);
        }
        return mIsEuroArea;
    }

    /**
     * 是否又准确的定位。
     *
     * @param context
     * @return
     */
    public static boolean hasCertainLocation(Context context) {
        return !TextUtils.isEmpty(BPLocationUtils.getSIMCountryCode(context));
    }

    /**
     * 是否需要展示GDPR弹窗。
     *
     * @return
     */
    public static boolean needShowGDPRDialog(Context context) {
        // 是否同意过GDPR协议。
        boolean hasAgreeGDPR = ApplicationConfig.isAgreeGDPRProtocol(context);
        // 是否在欧洲地区。
        boolean isEuroArea = isEuroArea(context);
        // 没有同意GDPR协议并且定位是欧洲。
        return !hasAgreeGDPR && isEuroArea;
    }

    /**
     * 是否检查过GDPR的询问弹窗
     *
     * @return true 检查过/false 没有检查过
     */
    public static boolean hasCheckGDPR() {
        return !needShowGDPRDialog(AppContext.getContext());
    }
}
