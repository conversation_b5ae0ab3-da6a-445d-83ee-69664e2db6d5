package com.commsource.widget

import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View
import com.commsource.studio.function.BaseSubFragment
import com.commsource.util.common.MathUtil
import com.commsource.util.gone
import com.commsource.util.invisible
import com.commsource.util.visible
import com.meitu.library.util.device.DeviceUtils
import java.util.jar.Attributes

/**
 * 图片位置过渡空间。
 */
class ImageTranslateView : View {
    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    /**
     * 过渡使用的图片。
     */
    var targetBitmap: Bitmap? = null

    /**
     * 首次Draw的回调。
     */
    var firstDrawCallback: (() -> Unit)? = null

    var invisibleWhenEnd = false

    /**
     * 过渡开始时的位置。
     */
    var originRectF = RectF()
        set(value) {
            field.set(value)
            animateRectF.set(value)
        }

    /**
     * 过渡的目标位置。
     */
    var targetRectF = RectF()
        set(value) {
            field.set(value)
        }

    /**
     * 动画中心的矩形。
     */
    var animateRectF = RectF()

    private var animator: ValueAnimator? = null

    /**
     * 绘制图片使用的画笔。
     */
    var paint = Paint(Paint.ANTI_ALIAS_FLAG)

    /**
     * 开始过渡动画。
     */
    fun start(invisibleWhenEnd: Boolean, action: (() -> Unit)? = null) {
        this.invisibleWhenEnd = invisibleWhenEnd
        animator?.cancel()
        visible()
        animator = ValueAnimator.ofFloat(0f, 1f).apply {
            addUpdateListener {
                val progress = animatedValue as Float
                makeValidRectF(originRectF)
                makeValidRectF(targetRectF)
                animateRectF.left = MathUtil.getRatioValue(originRectF.left, targetRectF.left, progress)
                animateRectF.right = MathUtil.getRatioValue(originRectF.right, targetRectF.right, progress)
                animateRectF.top = MathUtil.getRatioValue(originRectF.top, targetRectF.top, progress)
                animateRectF.bottom = MathUtil.getRatioValue(originRectF.bottom, targetRectF.bottom, progress)
                invalidate()
                if (progress == 1f) {
                    action?.invoke()
                    // 不设置gone防止宽高丢失。
                    if (<EMAIL>) {
                        invisible()
                    }
                }
            }
            duration = 200
            start()
        }
    }

    fun show() {
        visible()
        makeValidRectF(targetRectF)
        animateRectF.set(targetRectF)
        invalidate()
    }

    fun gone() {
        if (animator?.isRunning == true) {
            <EMAIL> = true
        } else {
            invisible()
        }
    }

    /**
     * 保证RectF中的信息有效。
     */
    private fun makeValidRectF(rectF: RectF) {
        if (rectF.isEmpty && targetBitmap != null) {
            rectF.set(
                MathUtil.generateInscribeRect(
                    RectF(0f, 0f, width.toFloat(), height.toFloat()),
                    targetBitmap!!.width,
                    targetBitmap!!.height
                )
            )
        }
    }

    override fun onDraw(canvas: Canvas) {
        targetBitmap.let {
            makeValidRectF(animateRectF)
            it?.let { it1 -> canvas.drawBitmap(it1, null, animateRectF, paint) }
            firstDrawCallback?.let { callback ->
                callback.invoke()
                firstDrawCallback = null
            }
        }
    }
}