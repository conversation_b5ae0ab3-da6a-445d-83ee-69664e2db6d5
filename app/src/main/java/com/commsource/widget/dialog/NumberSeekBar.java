package com.commsource.widget.dialog;

import com.commsource.beautyplus.R;
import com.commsource.util.RTLTool;
import com.meitu.library.util.device.DeviceUtils;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.MotionEvent;

/**
 * <AUTHOR> 显示数值的seekBar
 * Created by meitu on 2018/10/10.
 */

public class NumberSeekBar extends androidx.appcompat.widget.AppCompatSeekBar {

    private Paint mTextPaint;
    /**文字大小*/
    private int mTextSize;

    /**
     * 文字颜色
     */
    private int mTextColor = Color.BLACK;
    private int dp_2;


    public NumberSeekBar(Context context) {
        this(context, null);
    }

    public NumberSeekBar(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public NumberSeekBar(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mTextSize = DeviceUtils.dip2px(25);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.NumberSeekBar, defStyleAttr, 0);
        int count = typedArray.getIndexCount();
        for (int i = 0; i < count; i++) {
            switch (typedArray.getIndex(i)) {
                case R.styleable.NumberSeekBar_numTextSize:
                    mTextSize = (int) typedArray.getDimension(i, DeviceUtils.dip2fpx(25));
                    break;
                case R.styleable.NumberSeekBar_numTextColor:
                    mTextColor = typedArray.getColor(i, Color.BLACK);
                    break;
                default:
                    break;
            }
        }
        typedArray.recycle();
        init();

    }

    /**
     * 初始化
     */
    private void init() {
        mTextPaint = new Paint();
        mTextPaint.setColor(mTextColor);
        mTextPaint.setTextSize(mTextSize);
        mTextPaint.setAntiAlias(true);
        mTextPaint.setTextAlign(Paint.Align.CENTER);
        int paddingRight = getPaddingRight();
        int mLeft = paddingRight > mTextSize / 2 ? paddingRight : mTextSize / 2;
        int mRight = paddingRight > mTextSize / 2 ? paddingRight : mTextSize / 2;
        if (RTLTool.isLayoutRtl()) {
            setPadding(mRight, mTextSize, mLeft, 0);
        } else {
            setPadding(mLeft, mTextSize, mRight, 0);
        }
        dp_2 = DeviceUtils.dip2px(2);
    }


    @Override
    protected synchronized void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        // 获取seekBarThumb的位置
        Rect progressDrawableBounds = getProgressDrawable().getBounds();
        int drawbleWidth = getThumb().getIntrinsicWidth();
        // 计算当前的位置
        float bkx = getProgress() * progressDrawableBounds.width() * 1F / getMax();
        float textX = getPaddingLeft()+progressDrawableBounds.left+bkx-dp_2;
        mTextPaint.setFakeBoldText(true);
        canvas.drawText(String.valueOf(getNumber() + 1), textX, mTextSize, mTextPaint);

    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        // 点击的时候通知绘画
        invalidate();
        return super.onTouchEvent(event);
    }
    
    public int getNumber() {
        int progress = getProgress();
        if (progress >= 20) {
            return progress / 10 - 1;
        }
        return 0;
    }
}
