package com.meitu.room.database

import android.database.Cursor
import android.os.CancellationSignal
import androidx.room.Database
import androidx.room.DeleteColumn
import androidx.room.DeleteTable
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.AutoMigrationSpec
import androidx.sqlite.db.SupportSQLiteQuery
import com.commsource.airepair.VideoAiRepairRecord
import com.commsource.airepair.VideoRepairRecordDao
import com.commsource.airepair.imagequality.repo.ImageRepairRecord
import com.commsource.airepair.imagequality.repo.ImageRepairRecordDao
import com.commsource.album.component.ImagePortraitDao
import com.commsource.album.component.ImagePortraitEntity
import com.commsource.album.component.ImagePortraitProvider
import com.commsource.beautyfilter.FilterCategoryInfo
import com.commsource.billing.bean.subsconfig.Config
import com.commsource.billing.credit.Good
import com.commsource.billing.credit.bean.CampaignModel
import com.commsource.billing.credit.bean.CampaignModelDao
import com.commsource.billing.credit.bean.CreditSkuModel
import com.commsource.billing.credit.bean.CreditSkuModelDao
import com.commsource.camera.montage.MontageGroupEntity
import com.commsource.camera.montage.MontageMaterialEntity
import com.commsource.camera.xcamera.cover.bottomFunction.effect.look.LookCategory
import com.commsource.duffle.airetouch.AiRetouchDao
import com.commsource.duffle.airetouch.AiRetouchMaterial
import com.commsource.duffle.airetouch.AiRetouchTag
import com.commsource.duffle.airetouch.AiRetouchTagDao
import com.commsource.duffle.sticker.DuffleStiCategoryDao
import com.commsource.duffle.sticker.DuffleStickerDao
import com.commsource.duffle.sticker.StiCategory
import com.commsource.duffle.sticker.Sticker
import com.commsource.home.entity.DialogDataEntity
import com.commsource.home.work.aigenerate.xyz.XYZRecordDao
import com.commsource.home.work.aigenerate.xyz.XYZRecordEntity
import com.commsource.repository.FileTypeConverter
import com.commsource.repository.child.filter.NewFilter
import com.commsource.repository.child.filter.NewFilterCategory
import com.commsource.repository.child.makeup.MakeupMaterial
import com.commsource.search_common.entity.KeyWordInfo
import com.commsource.search_common.entity.SearchTagEntity
import com.commsource.search_common.repo.KeyWordDao
import com.commsource.search_common.repo.SearchTagDao
import com.commsource.studio.doodle.DoodleMaterial
import com.commsource.studio.doodle.NewDoodleCategory
import com.commsource.studio.formula.FormulaCategory
import com.commsource.studio.formula.JsFormula
import com.commsource.studio.function.background.BgTextureCategory
import com.commsource.studio.function.background.GradientMaterial
import com.commsource.studio.function.background.TextureMaterial
import com.commsource.studio.function.beauty_pen.duffler.BeautyPenDao
import com.commsource.studio.function.beauty_pen.duffler.BeautyPenMaterial
import com.commsource.studio.function.beauty_pen.duffler.BeautyPenTag
import com.commsource.studio.function.beauty_pen.duffler.BeautyPenTagDao
import com.commsource.studio.function.mosaic.MosaicMaterial
import com.commsource.studio.looks.LooksDuffleCategory
import com.commsource.studio.looks.LooksDuffleMaterial
import com.commsource.studio.looks.MTLooksV1CategoryDao
import com.commsource.studio.looks.MTLooksV1MaterialDao
import com.commsource.studio.text.TextFontMaterial
import com.commsource.studio.text.TextTemplateCategory
import com.commsource.studio.text.TextTemplateMaterial
import com.commsource.studio.work.Work
import com.commsource.videostudio.func.sticker.VSticker
import com.commsource.videostudio.func.sticker.VStickerCategory
import com.commsource.videostudio.func.sticker.VStickerCategoryDao
import com.commsource.videostudio.func.sticker.VStickerDao
import com.commsource.videostudio.func.subfunc.anim.MaterialAnim
import com.commsource.videostudio.func.subfunc.anim.MaterialAnimDao
import com.commsource.videostudio.func.subfunc.anim.TextMaterialAnim
import com.commsource.videostudio.func.subfunc.anim.TextMaterialAnimDao
import com.meitu.room.dao.BgTextureCategoryDao
import com.meitu.room.dao.CouponDao
import com.meitu.room.dao.GoodDao
import com.meitu.room.dao.MTArMaterialDao
import com.meitu.room.dao.MTChatDao
import com.meitu.room.dao.MTChatFiledDao
import com.meitu.room.dao.MTDoodleCategoryDao
import com.meitu.room.dao.MTDoodleDao
import com.meitu.room.dao.MTFilterCategoryInfoDao
import com.meitu.room.dao.MTFilterDao
import com.meitu.room.dao.MTFilterGroupDao
import com.meitu.room.dao.MTFormulaCategoryDao
import com.meitu.room.dao.MTFormulaDao
import com.meitu.room.dao.MTGradientDao
import com.meitu.room.dao.MTLookCategoryDao
import com.meitu.room.dao.MTLookMaterialDao
import com.meitu.room.dao.MTMakeupMaterialDao
import com.meitu.room.dao.MTMontageGroupDao
import com.meitu.room.dao.MTMontageMaterialDao
import com.meitu.room.dao.MTNewDoodleCategoryDao
import com.meitu.room.dao.MTNewDoodleDao
import com.meitu.room.dao.MTOnlineDialogDao
import com.meitu.room.dao.MTTextFontDao
import com.meitu.room.dao.MTTextTemplateCategoryDao
import com.meitu.room.dao.MTTextTemplateDao
import com.meitu.room.dao.MTTextureDao
import com.meitu.room.dao.MTWorkDao
import com.meitu.room.dao.MosaicDao
import com.meitu.room.dao.MusicDao
import com.meitu.room.dao.MusicTagDao
import com.meitu.room.dao.NewFilterCategoryDao
import com.meitu.room.dao.NewFilterDao
import com.meitu.room.dao.SoundEffectDao
import com.meitu.room.dao.SoundEffectTagDao
import com.meitu.room.dao.SpecialEffectDao
import com.meitu.room.dao.SpecialEffectTagDao
import com.meitu.room.dao.StyleDao
import com.meitu.room.dao.StyleTagDao
import com.meitu.room.dao.TransitionDao
import com.meitu.room.dao.TransitionTagDao
import com.meitu.template.bean.ArMaterial
import com.meitu.template.bean.Chat
import com.meitu.template.bean.ChatFiled
import com.meitu.template.bean.Doodle
import com.meitu.template.bean.DoodleCategory
import com.meitu.template.bean.Filter
import com.meitu.template.bean.FilterGroup
import com.meitu.template.bean.LookMaterial
import com.meitu.template.bean.MusicMaterial
import com.meitu.template.bean.MusicTag
import com.meitu.template.bean.SoundEffectMaterial
import com.meitu.template.bean.SoundEffectTag
import com.meitu.template.bean.SpecialEffectMaterial
import com.meitu.template.bean.SpecialEffectTag
import com.meitu.template.bean.StyleMaterial
import com.meitu.template.bean.StyleTag
import com.meitu.template.bean.TransitionMaterial
import com.meitu.template.bean.TransitionTag
import com.pixocial.business.ar.popup.ARPopupDao
import com.pixocial.business.ar.popup.PopupModel
import com.pixocial.business.duffle.repo.ar.ARCategory
import com.pixocial.business.duffle.repo.ar.ARCategoryDao
import com.pixocial.business.duffle.repo.ar.ARDao
import com.pixocial.business.duffle.repo.ar.ARMaterial
import com.pixocial.business.duffle.repo.film.FilmCategory
import com.pixocial.business.duffle.repo.film.FilmCategoryDao
import com.pixocial.business.duffle.repo.film.FilmDao
import com.pixocial.business.duffle.repo.film.FilmMaterial
import com.pixocial.business.duffle.repo.glow.GlowCateDao
import com.pixocial.business.duffle.repo.glow.GlowCategory
import com.pixocial.business.duffle.repo.glow.GlowDao
import com.pixocial.business.duffle.repo.glow.GlowMaterial
import com.pixocial.business.duffle.repo.mockup.MockupCateDao
import com.pixocial.business.duffle.repo.mockup.MockupCategory
import com.pixocial.business.duffle.repo.mockup.MockupDao
import com.pixocial.business.duffle.repo.mockup.MockupMaterial
import com.pixocial.bussiness.limit.dao.FreeLimitStrategy
import com.pixocial.bussiness.limit.dao.FreeLimitStrategyDao
import com.pixocial.bussiness.limit.dao.FreeLimitUsage
import com.pixocial.bussiness.limit.dao.FreeLimitUsageDao
import com.pixocial.framework.aws.data.AwsDownwardRecord
import com.pixocial.framework.aws.data.AwsDownwardRecordDao
import com.pixocial.framework.aws.data.AwsUpwardRecord
import com.pixocial.framework.aws.data.AwsUpwardRecordDao

/**
 * @Desc : 数据库结构
 * <AUTHOR> Bear - 6/28/21
 */
@Database(
    entities = [
        ArMaterial::class,
        ChatFiled::class,
        Chat::class,
        MontageGroupEntity::class,
        MontageMaterialEntity::class,
        Filter::class,
        FilterCategoryInfo::class,
        FilterGroup::class,
        LookMaterial::class,
        LooksDuffleMaterial::class,
        MakeupMaterial::class,
        Doodle::class,
        DoodleCategory::class,
        TextureMaterial::class,
        TextTemplateMaterial::class,
        GradientMaterial::class,
        TextFontMaterial::class,
        DoodleMaterial::class,
        JsFormula::class,
        DialogDataEntity::class,
        FormulaCategory::class,
        LookCategory::class,
        LooksDuffleCategory::class,
        NewDoodleCategory::class,
        TextTemplateCategory::class,
        BgTextureCategory::class,
        VSticker::class,
        VStickerCategory::class,
        Work::class,
        MosaicMaterial::class,
        NewFilter::class,
        NewFilterCategory::class,
        MusicMaterial::class,
        MusicTag::class,
        MaterialAnim::class,
        TextMaterialAnim::class,
        SoundEffectMaterial::class,
        SoundEffectTag::class,
        SpecialEffectMaterial::class,
        SpecialEffectTag::class,
        TransitionMaterial::class,
        TransitionTag::class,
        KeyWordInfo::class,
        StyleMaterial::class,
        StyleTag::class,
        ImagePortraitEntity::class,
        Config::class,
        StiCategory::class,
        Sticker::class,
        AwsUpwardRecord::class,
        AwsDownwardRecord::class,
        BeautyPenMaterial::class,
        VideoAiRepairRecord::class,
        XYZRecordEntity::class,
        BeautyPenTag::class,
        SearchTagEntity::class,
        Good::class,
        CreditSkuModel::class,
        CampaignModel::class,
        AiRetouchTag::class,
        AiRetouchMaterial::class,
        ImageRepairRecord::class,
        FilmCategory::class,
        FilmMaterial::class,
        MockupMaterial::class,
        MockupCategory::class,
        FreeLimitStrategy::class,
        FreeLimitUsage::class,
        GlowMaterial::class,
        GlowCategory::class,
        ARMaterial::class,
        ARCategory::class,
        PopupModel::class,
    ],
    exportSchema = true,
    version = 119,
    autoMigrations = [
        androidx.room.AutoMigration(from = 87, to = 88),
        androidx.room.AutoMigration(
            from = 88,
            to = 89,
            spec = BeautyPlusDatabase.MigrationSpec88to89::class
        ),
        androidx.room.AutoMigration(from = 89, to = 90),
        androidx.room.AutoMigration(from = 90, to = 91),
        androidx.room.AutoMigration(from = 91, to = 92),
        // 92 -> 93 手动migration
        androidx.room.AutoMigration(from = 93, to = 94),
        androidx.room.AutoMigration(from = 94, to = 95),
        // 95 -> 96 手动升级！！
        androidx.room.AutoMigration(from = 96, to = 97),
        androidx.room.AutoMigration(from = 97, to = 98),
        androidx.room.AutoMigration(from = 98, to = 99),
        androidx.room.AutoMigration(from = 99, to = 100),
        androidx.room.AutoMigration(from = 100, to = 101),
        androidx.room.AutoMigration(from = 101, to = 102),
        androidx.room.AutoMigration(from = 102, to = 103),
        androidx.room.AutoMigration(from = 103, to = 104),
        androidx.room.AutoMigration(from = 104, to = 105),
        androidx.room.AutoMigration(
            from = 105, to = 106, spec = BeautyPlusDatabase.MigrationSpec105to106::class
        ),
        androidx.room.AutoMigration(from = 106, to = 107),
        androidx.room.AutoMigration(from = 107, to = 108),
        androidx.room.AutoMigration(from = 108, to = 109),
        androidx.room.AutoMigration(from = 109, to = 110),
        androidx.room.AutoMigration(from = 110, to = 111),
        androidx.room.AutoMigration(from = 111, to = 112),
        androidx.room.AutoMigration(from = 112, to = 113),
        androidx.room.AutoMigration(from = 113, to = 114),
        androidx.room.AutoMigration(from = 114, to = 115),
        androidx.room.AutoMigration(from = 115, to = 116),
        androidx.room.AutoMigration(from = 116, to = 117),
        androidx.room.AutoMigration(from = 117, to = 118),
        androidx.room.AutoMigration(
            from = 118, to = 119, spec = BeautyPlusDatabase.MigrationSpec118to119::class
        ),
    ]
)
@TypeConverters(value = [MultiDataConvert::class, FileTypeConverter::class])
abstract class BeautyPlusDatabase : RoomDatabase(), ImagePortraitProvider {
    abstract val arMaterialDao: MTArMaterialDao
    abstract val chatDao: MTChatDao
    abstract val chatFiledDao: MTChatFiledDao
    abstract val mTMontageGroupDao: MTMontageGroupDao
    abstract val mTMontageMaterialDao: MTMontageMaterialDao
    abstract val newFilterDao: MTFilterDao
    abstract val filterCategoryInfoDao: MTFilterCategoryInfoDao
    abstract val filterGroupDao: MTFilterGroupDao
    abstract val mTLookMaterialDao: MTLookMaterialDao
    abstract val mTLookCategoryDao: MTLookCategoryDao
    abstract val mtLooksV1CategoryDao: MTLooksV1CategoryDao
    abstract val mtLooksV1MaterialDao: MTLooksV1MaterialDao
    abstract val mTMakeupMaterialDao: MTMakeupMaterialDao
    abstract val doodleDao: MTDoodleDao
    abstract val doodleCategoryDao: MTDoodleCategoryDao
    abstract val textFrontDao: MTTextFontDao
    abstract val textureDao: MTTextureDao
    abstract val gradientDao: MTGradientDao
    abstract val textTemplateDao: MTTextTemplateDao
    abstract val textTemplateCategoryDao: MTTextTemplateCategoryDao
    abstract val newDoodleDao: MTNewDoodleDao
    abstract val newDoodleCategoryDao: MTNewDoodleCategoryDao
    abstract val formulaCategoryDao: MTFormulaCategoryDao
    abstract val formulaDao: MTFormulaDao
    abstract val onlineDialogDao: MTOnlineDialogDao
    abstract val workDao: MTWorkDao
    abstract val bgTextureCatDao: BgTextureCategoryDao
    abstract val mosaicDao: MosaicDao
    abstract val filterDao: NewFilterDao
    abstract val newFilterCategoryDao: NewFilterCategoryDao
    abstract val musicDao: MusicDao
    abstract val musicTagDao: MusicTagDao
    abstract val vStickerDao: VStickerDao
    abstract val vStickerCategoryDao: VStickerCategoryDao
    abstract val soundEffectTagDao: SoundEffectTagDao
    abstract val soundEffectDao: SoundEffectDao
    abstract val specialEffectDao: SpecialEffectDao
    abstract val specialEffectTagDao: SpecialEffectTagDao
    abstract val transitionDao: TransitionDao
    abstract val transitionTagDao: TransitionTagDao
    abstract val materialAnimDao: MaterialAnimDao
    abstract val textMaterialAnimDao: TextMaterialAnimDao
    abstract val styleDao: StyleDao
    abstract val styleTagDao: StyleTagDao
    abstract val keyWordDao: KeyWordDao
    abstract val imagePortraitDao: ImagePortraitDao
    abstract val goodDao: GoodDao
    abstract val awsUpwardRecordDao: AwsUpwardRecordDao
    abstract val awsDownwardRecordDao: AwsDownwardRecordDao
    abstract val beautyPenTagDao: BeautyPenTagDao
    abstract val beautyPenDao: BeautyPenDao
    abstract val searchTagDao: SearchTagDao
    abstract val aiRepairRecordDao: VideoRepairRecordDao
    abstract val xyzRecordDao: XYZRecordDao
    abstract val creditSkuModelDao: CreditSkuModelDao
    abstract val campaignModel: CampaignModelDao
    abstract val aiRetouchTagDao: AiRetouchTagDao
    abstract val aiRetouchDao: AiRetouchDao
    abstract val imageRepairRecordDao: ImageRepairRecordDao
    abstract val duffleFilmDao: FilmDao
    abstract val duffleFilmCategoryDao: FilmCategoryDao
    abstract val duffleMockupDao: MockupDao
    abstract val duffleMockupCateDao: MockupCateDao
    abstract val duffleGlowDao: GlowDao
    abstract val duffleGlowCateDao: GlowCateDao
    abstract val duffleArDao: ARDao
    abstract val duffleArCategoryDao: ARCategoryDao
    abstract val duffleArPopupDao: ARPopupDao

    abstract val freeLimitUsageDao: FreeLimitUsageDao
    abstract val freeLimitStrategyDao: FreeLimitStrategyDao

    override fun getPortraitDao(): ImagePortraitDao {
        return imagePortraitDao
    }

    abstract val duffleStiCategoryDao: DuffleStiCategoryDao
    abstract val duffleStiDao: DuffleStickerDao
    abstract val couponDao: CouponDao

//    fun awsDownWardRecordDao(): AwsDownwardRecordDao {
//        return awsDownwardRecordDao
//    }
//
//    fun awsUpWardRecordDao(): AwsUpwardRecordDao {
//        return awsUpwardRecordDao
//    }

    override fun query(query: String, args: Array<out Any?>?): Cursor {
        return try {
            // 概率性出现查表时抛数据库已关闭的异常，这里捕获住，返回空数据
            super.query(query, args)
        } catch (e: IllegalStateException) {
            EmptyCursor()
        }
    }

    override fun query(query: SupportSQLiteQuery, signal: CancellationSignal?): Cursor {
        return try {
            // 概率性出现查表时抛数据库已关闭的异常，这里捕获住，返回空数据
            super.query(query, signal)
        } catch (e: IllegalStateException) {
            EmptyCursor()
        }
    }

    @DeleteColumn(tableName = "TEXT_TEMPLATE_MATERIAL", columnName = "picture_editor")
    @DeleteColumn(tableName = "TEXT_TEMPLATE_MATERIAL", columnName = "video_editor")
    @DeleteColumn(tableName = "NEW_FILTER_MATERIAL", columnName = "picture_editor")
    @DeleteColumn(tableName = "NEW_FILTER_MATERIAL", columnName = "video_editor")
    @DeleteColumn(tableName = "NEW_FILTER_CATEGORY", columnName = "picture_editor")
    @DeleteColumn(tableName = "NEW_FILTER_CATEGORY", columnName = "video_editor")
    class MigrationSpec88to89 : AutoMigrationSpec

    @DeleteTable(tableName = "Duffle_Avatar_Tag")
    @DeleteTable(tableName = "Duffle_Avatar")
    @DeleteTable(tableName = "AR_MATERIAL")
    @DeleteTable(tableName = "AR_MATERIAL_GROUP")
    @DeleteTable(tableName = "AR_MATERIAL_PAID_INFO")
    @DeleteTable(tableName = "MONTAGE_MATERIAL_ENTITY")
    @DeleteTable(tableName = "MONTAGE_GROUP_ENTITY")
    class MigrationSpec105to106 : AutoMigrationSpec

    // AR_MATERIAL 暂时不删，观测下蒙太奇数据是否有反馈
    @DeleteTable(tableName = "AR_MATERIAL_GROUP")
    @DeleteTable(tableName = "AR_MATERIAL_PAID_INFO")
    class MigrationSpec118to119 : AutoMigrationSpec
}
