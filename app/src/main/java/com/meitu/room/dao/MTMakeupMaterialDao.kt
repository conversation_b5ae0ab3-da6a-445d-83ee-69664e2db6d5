package com.meitu.room.dao

import androidx.room.*
import com.commsource.camera.param.MakeupType
import com.commsource.repository.child.makeup.MakeupMaterial

/**
 * @Desc : 美妆单品素材
 * <AUTHOR> Bear - 2020/7/3
 */
@Dao
interface MTMakeupMaterialDao : IDataResource<MakeupMaterial, Int> {

    @Query("select * from makeup_material where id = :key")
    override fun loadEntity(key: Int?): MakeupMaterial

    @Query("select * from makeup_material where onlineId = :onlineId")
    fun loadEntityByOnlineId(onlineId: String?):MakeupMaterial

    @Query("select id from makeup_material")
    override fun loadKeys(): MutableList<Int>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    override fun insert(entity: MakeupMaterial)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    override fun insertAll(list: Array<MakeupMaterial>)

    @Update
    override fun updateAll(list: Array<MakeupMaterial>)

    @Update
    override fun update(entity: MakeupMaterial)

    @Delete
    override fun delete(entity: MakeupMaterial)

    @Delete
    override fun deleteAll(list: Array<MakeupMaterial>)

    @Query("select * from makeup_material")
    fun loadAllEnableEntity(): MutableList<MakeupMaterial>?

    @Query("select * from makeup_material")
    fun loadAll(): MutableList<MakeupMaterial>

    /**
     * 加载所有gl3限制的实体数据 如果isSupportGl3 == 1 那么需要gl3才能使用的素材
     */
    @Query("select * from makeup_material where isSupportGl3 == 0")
    fun loadAllGl3LimitEnableEntity(): MutableList<MakeupMaterial>?

    /**
     * 加载美妆数据
     */
    @Query("select * from makeup_material where makeupType = :makeupType and configType == 2")
    fun loadMakeupTypeMaterial(@MakeupType makeupType: Int): MutableList<MakeupMaterial>

    /**
     * 加载美妆睡觉
     */
    @Query("select * from makeup_material where makeupType = :makeupType and configType == 1")
    fun loadMakeupTypeColorMaterial(@MakeupType makeupType: Int): MutableList<MakeupMaterial>

    /**
     * 加载本地内置素材
     */
    @Query("select * from makeup_material where isInside == 1")
    fun loadInternalMaterials(): MutableList<MakeupMaterial>?
}