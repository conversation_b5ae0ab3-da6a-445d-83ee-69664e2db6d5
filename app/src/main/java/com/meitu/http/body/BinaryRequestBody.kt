package com.meitu.http.body

import okhttp3.MediaType
import okhttp3.RequestBody
import okhttp3.internal.closeQuietly
import okio.BufferedSink
import okio.Source
import okio.source
import java.io.IOException
import java.io.InputStream

class BinaryRequestBody(val mediaType: MediaType?, val inputStream: InputStream) : RequestBody() {
    override fun contentType(): MediaType? {
        return mediaType
    }

    override fun contentLength(): Long {
        return try {
            inputStream.available().toLong()
        } catch (e: IOException) {
            0
        }
    }

    override fun writeTo(sink: BufferedSink) {
        var source: Source? = null
        try {
            source = inputStream.source()
            sink.writeAll(source)
        } finally {
            source?.closeQuietly()
        }
    }
}