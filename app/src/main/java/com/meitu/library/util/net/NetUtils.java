package com.meitu.library.util.net;

import com.meitu.common.AppContext;
import com.meitu.library.util.Debug.Debug;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.provider.Settings;


/**
 * 与网络相关的工具类
 *
 * <AUTHOR>
 * @since 2013-04-10
 */
public class NetUtils {

    private static ConnectivityManager mConnManager;
    private static NetworkInfo mNetworkInfo;
    /**
     * 移动网络
     */
    public static final int WAP = -5;
    /**
     * 成功
     */
    public static final int OK = 1;
    /**
     * 关闭
     */
    public static final int DISABLE = -1;
    /**
     * 失败
     */
    public static final int FAIL = -2;
    /**
     * 未开启移动网络或WLAN
     */
    public static final int NOINFO = -3;
    /**
     * 异常
     */
    public static final int EXCEPTION = -4;

    /**
     * 判断是否可联网
     *
     * @param context 上下文
     * @return true可联网，false不可联网
     * @see #checkNetConnection(Context)
     */
    public static boolean canNetworking(Context context) {
        int netState = checkNetConnection(context);
        return netState == OK || netState == WAP;
    }

    public static boolean canNetworking() {
        int netState = checkNetConnection(AppContext.getContext());
        return netState == OK || netState == WAP;
    }


    /**
     * 检测当前是否有可用网络
     *
     * @param context 上下文
     * @return 1：可用； -1：不可用；0：移动网络； -2：连接测试网站失败； -3：无网络信息； -4：异常
     */
    public static int checkNetConnection(Context context) {
        return NetStatusMonitor.INSTANCE.getNetState();
    }



    /**
     * 根据网络类型选择下载缓冲池大小
     *
     * @param context 上下文
     * @return 缓冲池大小：wifi:100K,3g:32K,其他：8k，无网络：0
     */
    public static int chooseBufferSize(Context context) {
        String netWorkType = getNetWorkType(context);
        int size = 8;
        if ("wifi".equals(netWorkType)) {
            size = 100;
        } else if ("3g".equals(netWorkType)) {
            size = 32;
        } else if ("net".equals(netWorkType)) {
            size = 8;
        } else if ("wap".equals("netWorkType")) {
            size = 8;
        } else if ("".equals(netWorkType)) {
            // 无网络
            size = 0;
        }
        return size;
    }

    /**
     * 检测网络类型
     *
     * @param context 上下文
     * @return 网络类型：""、"wifi"、"3g"、"net"、"wap"、"other"
     */
    public static String getNetWorkType(Context context) {
        try {
            mConnManager = (ConnectivityManager) AppContext.getContext().getSystemService(Context.CONNECTIVITY_SERVICE);
            mNetworkInfo = mConnManager.getActiveNetworkInfo();
            if (mNetworkInfo == null || !mNetworkInfo.isConnected()) {// 检测是否有可用网络信息//
                // 网络没有连接
                return "";
            }
            if (mNetworkInfo.getTypeName().toLowerCase().contains("wifi")) {
                return "wifi";
            } else {
                if (mNetworkInfo.getExtraInfo() != null) {
                    String apn = mNetworkInfo.getExtraInfo().toLowerCase();
                    if (apn.contains("3g")) {
                        return "3g";
                    } else if (apn.contains("net")) {
                        return "net";
                    } else if (apn.contains("wap")) {
                        return "wap";
                    } else {
                        return apn;
                    }
                } else {
                    return "other";
                }
            }
        } catch (Exception e) {
            Debug.PrintError(e);
        }
        return "";
    }

    /**
     * 获取网络类型是否是wifi
     *
     * @param context 上下文
     * @return true 是，false 其他
     */
    public static boolean isWIFI(Context context) {
        String netWorkType = getNetWorkType(context);
        return "wifi".equals(netWorkType);
    }

    /**
     * 检查移动数据网络是否可用。
     *
     * @return 如果可用就返回true。
     */
    public static boolean isMobileEnable(Context context) {
        try {
            if (context == null) {
                return false;
            }
            ConnectivityManager connectivityManager =
                    (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            if (connectivityManager == null) {
                return false;
            }
            NetworkInfo networkInfo = connectivityManager.getNetworkInfo(ConnectivityManager.TYPE_MOBILE);
            return networkInfo != null && networkInfo.isConnected();
        } catch (Throwable ignore) {
            ignore.printStackTrace();
            return false;
        }
    }

    /**
     * 无网络状态下，提供用户选择是否进行网络设置，弹出的对话框不响应back键
     *
     * @param activity Activity实例
     * @see #turnIntoNetSetting(Activity, int, boolean)
     */
    public static void turnIntoNetSetting(final Activity activity) {
        turnIntoNetSetting(activity, checkNetConnection(activity), false);
    }

    /**
     * 无网络状态下，提供用户选择是否进行网络设置，弹出的对话框不响应back键
     *
     * @param activity Activity实例
     * @param netState 网络状态
     * @see #turnIntoNetSetting(Activity, int, boolean)
     */
    public static void turnIntoNetSetting(final Activity activity, final int netState) {
        turnIntoNetSetting(activity, netState, false);
    }

    /**
     * 无网络状态下，提供用户选择是否进行网络设置，弹出的对话框不响应back键
     *
     * @param activity   Activity实例
     * @param netState   网络状态
     * @param willFinish 点击按钮后是否关闭当前activity
     */
    public static void turnIntoNetSetting(final Activity activity, final int netState, final boolean willFinish) {
        try {
            String showMeString = "无可用网络";
            if (netState == WAP) {
                showMeString = "不支持wap网络接入方式,请设置接入点(APN)为net方式";
            } else if (netState == FAIL) {
                showMeString = "网络连接失败,请检测网络";
            } else if (netState == NOINFO) {
                showMeString = "未开启移动网络或WLAN";
            } else if (netState == EXCEPTION) {
                showMeString = "检测网络出现异常";
            }
            AlertDialog.Builder b =
                    new AlertDialog.Builder(activity).setTitle("提示").setMessage(showMeString + ",是否进行网络设置");
            b.setCancelable(false);
            b.setPositiveButton("设置", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int whichButton) {
                    if (willFinish) {
                        activity.finish();
                    }
                    if (netState == WAP) {
                        activity.startActivityForResult(new Intent(Settings.ACTION_APN_SETTINGS), 0);
                    } else {
                        if (android.os.Build.VERSION.SDK_INT <= 10) {
                            activity.startActivityForResult(new Intent(Settings.ACTION_WIRELESS_SETTINGS), 0);
                        } else {
                            activity.startActivityForResult(new Intent(Settings.ACTION_SETTINGS), 0);
                        }
                    }
                }
            });
            b.setNegativeButton("取消", new DialogInterface.OnClickListener() {

                @Override
                public void onClick(DialogInterface dialog, int which) {
                    if (willFinish) {
                        activity.finish();
                    }
                }
            }).show();
        } catch (Exception e) {
            Debug.PrintError(e);
        }
    }

}
