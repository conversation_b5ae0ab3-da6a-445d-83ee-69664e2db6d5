package com.meitu.library.util.Debug;

import android.util.Log;

/**
 * Debug 信息输出类，提供打印log信息到Logcat里面的工具。<br>
 * <AUTHOR>
 */
public class Debug {
    /**
     * Debug等级,默认的Debug等级为{@link #ALL}
     */
    public enum DebugLevel implements Comparable<DebugLevel> {
        NONE, ERROR, WARNING, INFO, DEBUG, VERBOSE;

        private static DebugLevel ALL = DebugLevel.VERBOSE;

        public boolean isSameOrLessThan(final DebugLevel pDebugLevel) {
            return this.compareTo(pDebugLevel) >= 0;
        }
    }

    private static DebugLevel sDebugLevel = DebugLevel.ERROR;
    public static String sTag = "Meitu";

    private static String sDebugUser = "Javan";

    /**
     * 打印{@link Log#DEBUG}级别日志
     * @see #d(String, String, Throwable)
     * @param pMessage 需要显示的日志
     */
    public static void d(final String pMessage) {
        Debug.d(Debug.sTag, pMessage, null);
    }

    /**
     * 打印{@link Log#DEBUG}级别日志
     * @see #d(String, String, Throwable)
     * @param pTag 需要显示的tag
     * @param pMessage 需要显示的日志
     */
    public static void d(final String pTag, final String pMessage) {
        Debug.d(pTag, pMessage, null);
    }

    /**
     * 打印{@link Log#DEBUG}级别日志
     * @see #d(String, String, Throwable)
     * @param pTag 需要显示的tag
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     */
    public static void d(final String pTag, String pMessage, final Throwable pThrowable) {
        if (Debug.sDebugLevel.isSameOrLessThan(DebugLevel.DEBUG)) {
            if (pMessage == null) {
                pMessage = "noMsg";
            }
            if (pThrowable == null) {
                Log.d(pTag, pMessage);
            } else {
                Log.d(pTag, pMessage, pThrowable);
            }
        }
    }

    /**
     * 打印{@link Log#DEBUG}级别日志
     * @see #d(String, String, Throwable)
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     */
    public static void d(final String pMessage, final Throwable pThrowable) {
        Debug.d(Debug.sTag, pMessage, pThrowable);
    }

    /**
     * 打印{@link Log#DEBUG}级别日志
     * @see #d(String, String, Throwable)
     * @param pThrowable 抛出的异常信息
     */
    public static void d(final Throwable pThrowable) {
        Debug.d(Debug.sTag, pThrowable);
    }

    /**
     * 打印{@link Log#DEBUG}级别日志
     * @param pMessage 需要显示的日志
     * @param pDebugUser 用户名称
     */
    public static void dUser(final String pMessage, final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.d(pMessage);
        }
    }

    /**
     * 打印{@link Log#DEBUG}级别日志
     * @param pTag 日志标签
     * @param pMessage 需要显示的日志
     * @param pDebugUser 用户名称
     */
    public static void dUser(final String pTag, final String pMessage, final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.d(pTag, pMessage);
        }
    }

    /**
     * 打印{@link Log#DEBUG}级别日志
     * @param pTag 日志标签
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     * @param pDebugUser 用户名称
     */
    public static void dUser(final String pTag, final String pMessage, final Throwable pThrowable,
                             final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.d(pTag, pMessage, pThrowable);
        }
    }

    /**
     * 打印{@link Log#DEBUG}级别日志
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     * @param pDebugUser 用户名称
     */
    public static void dUser(final String pMessage, final Throwable pThrowable, final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.d(pMessage, pThrowable);
        }
    }

    /**
     * 打印{@link Log#ERROR}级别日志
     * @see #e(String, String, Throwable)
     * @param pMessage 需要显示的日志
     */
    public static void e(final String pMessage) {
        Debug.e(Debug.sTag, pMessage, null);
    }

    /**
     * 打印{@link Log#ERROR}级别日志
     * @see #e(String, String, Throwable)
     * @param pTag 需要显示的tag
     * @param pMessage 需要显示的日志
     */
    public static void e(final String pTag, final String pMessage) {
        Debug.e(pTag, pMessage, null);
    }

    /**
     * 打印{@link Log#ERROR}级别日志
     * @see #e(String, String, Throwable)
     * @param pTag 需要显示的tag
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     */
    public static void e(final String pTag, String pMessage, final Throwable pThrowable) {
        if (Debug.sDebugLevel.isSameOrLessThan(DebugLevel.ERROR)) {
            if (pMessage == null) {
                pMessage = "noMsg";
            }
            if (pThrowable == null) {
                Log.e(pTag, pMessage);
            } else {
                Log.e(pTag, pMessage, pThrowable);
            }
        }
    }

    /**
     * 打印{@link Log#ERROR}级别日志
     * @see #e(String, String, Throwable)
     * @param pThrowable 抛出的异常信息
     */
    public static void e(final String tag, final Throwable pThrowable) {
        Debug.e(tag, null, pThrowable);
    }

    /**
     * 打印{@link Log#ERROR}级别日志
     * @see #e(String, String, Throwable)
     * @param pThrowable 抛出的异常信息
     */
    public static void e(final Throwable pThrowable) {
        Debug.e(Debug.sTag, pThrowable);
    }

    /**
     * 打印{@link Log#ERROR}级别日志
     * @param pMessage 需要显示的日志
     * @param pDebugUser 用户名称
     */
    public static void eUser(final String pMessage, final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.e(pMessage);
        }
    }

    /**
     * 打印{@link Log#ERROR}级别日志
     * @param pTag 日志标签
     * @param pMessage 需要显示的日志
     * @param pDebugUser 用户名称
     */
    public static void eUser(final String pTag, final String pMessage, final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.e(pTag, pMessage);
        }
    }

    /**
     * 打印{@link Log#ERROR}级别日志
     * @param pTag 日志标签
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     * @param pDebugUser 用户名称
     */
    public static void eUser(final String pTag, final String pMessage, final Throwable pThrowable,
                             final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.e(pTag, pMessage, pThrowable);
        }
    }

    /**
     * 打印{@link Log#ERROR}级别日志
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     * @param pDebugUser 用户名称
     */
    public static void eUser(final String pMessage, final Throwable pThrowable, final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.e(pMessage, pThrowable);
        }
    }

    /**
     * 打印{@link Log#ERROR}级别日志
     * @param pThrowable 抛出的异常信息
     * @param pDebugUser 用户名称
     */
    public static void eUser(final Throwable pThrowable, final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.e(pThrowable);
        }
    }

    /**
     * 获取Debug等级,默认为{@link #sDebugLevel}
     * @return 返回当前的Debug等级
     */
    public static DebugLevel getDebugLevel() {
        return Debug.sDebugLevel;
    }

    /**
     * 获取打印标签TAG,默认为 {@link #sTag}
     */
    public static String getTag() {
        return Debug.sTag;
    }

    /**
     * 打印{@link Log#INFO}级别日志
     * @see #i(String, String, Throwable)
     * @param pMessage 需要显示的日志
     */
    public static void i(final String pMessage) {
        Debug.i(Debug.sTag, pMessage, null);
    }

    /**
     * 打印{@link Log#INFO}级别日志
     * @see #i(String, String, Throwable)
     * @param pTag 需要显示的tag
     * @param pMessage 需要显示的日志
     */
    public static void i(final String pTag, final String pMessage) {
        Debug.i(pTag, pMessage, null);
    }

    /**
     * 打印{@link Log#INFO}级别日志
     * @see #i(String, String, Throwable)
     * @param pTag 需要显示的tag
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     */
    public static void i(final String pTag, String pMessage, final Throwable pThrowable) {
        if (Debug.sDebugLevel.isSameOrLessThan(DebugLevel.INFO)) {
            if (pMessage == null) {
                pMessage = "noMsg";
            }
            if (pThrowable == null) {
                Log.i(pTag, pMessage);
            } else {
                Log.i(pTag, pMessage, pThrowable);
            }
        }
    }

    /**
     * 打印{@link Log#INFO}级别日志
     * @see #i(String, String, Throwable)
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     */
    public static void i(final String pMessage, final Throwable pThrowable) {
        Debug.i(Debug.sTag, pMessage, pThrowable);
    }

    /**
     * 打印{@link Log#INFO}级别日志
     * @param pMessage 需要显示的日志
     * @param pDebugUser 用户名称
     */
    public static void iUser(final String pMessage, final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.i(pMessage);
        }
    }

    /**
     * 打印{@link Log#INFO}级别日志
     * @param pTag 日志标签
     * @param pMessage 需要显示的日志
     * @param pDebugUser 用户名称
     */
    public static void iUser(final String pTag, final String pMessage, final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.i(pTag, pMessage);
        }
    }

    /**
     * 打印{@link Log#INFO}级别日志
     * @param pTag 日志标签
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     * @param pDebugUser 用户名称
     */
    public static void iUser(final String pTag, final String pMessage, final Throwable pThrowable,
                             final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.i(pTag, pMessage, pThrowable);
        }
    }

    /**
     * 打印{@link Log#INFO}级别日志
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     * @param pDebugUser 用户名称
     */
    public static void iUser(final String pMessage, final Throwable pThrowable, final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.i(pMessage, pThrowable);
        }
    }

    /**
     * 根据调试等级打调试日志
     * @param pDebugLevel 需要显示的日志级别
     * @param pMessage 需要显示的日志
     */
    public static void log(final DebugLevel pDebugLevel, final String pMessage) {
        switch (pDebugLevel) {
            case NONE:
                return;
            case VERBOSE:
                Debug.v(pMessage);
                return;
            case INFO:
                Debug.i(pMessage);
                return;
            case DEBUG:
                Debug.d(pMessage);
                return;
            case WARNING:
                Debug.w(pMessage);
                return;
            case ERROR:
                Debug.e(pMessage);
                return;
        }
    }

    /**
     * 根据调试等级打调试日志
     * @param pDebugLevel 需要显示的日志级别
     * @param pTag 需要显示的tag
     * @param pMessage 需要显示的日志
     */
    public static void log(final DebugLevel pDebugLevel, final String pTag, final String pMessage) {
        switch (pDebugLevel) {
            case NONE:
                return;
            case VERBOSE:
                Debug.v(pTag, pMessage);
                return;
            case INFO:
                Debug.i(pTag, pMessage);
                return;
            case DEBUG:
                Debug.d(pTag, pMessage);
                return;
            case WARNING:
                Debug.w(pTag, pMessage);
                return;
            case ERROR:
                Debug.e(pTag, pMessage);
                return;
        }
    }

    /**
     * 根据调试等级打调试日志
     * @param pDebugLevel 需要显示的日志级别
     * @param pTag 需要显示的tag
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     */
    public static void log(final DebugLevel pDebugLevel, final String pTag, final String pMessage,
                           final Throwable pThrowable) {
        switch (pDebugLevel) {
            case NONE:
                return;
            case VERBOSE:
                Debug.v(pTag, pMessage, pThrowable);
                return;
            case INFO:
                Debug.i(pTag, pMessage, pThrowable);
                return;
            case DEBUG:
                Debug.d(pTag, pMessage, pThrowable);
                return;
            case WARNING:
                Debug.w(pTag, pMessage, pThrowable);
                return;
            case ERROR:
                Debug.e(pTag, pMessage, pThrowable);
                return;
        }
    }

    /**
     * 根据调试等级打调试日志
     * @param pDebugLevel 需要显示的日志级别
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     */
    public static void log(final DebugLevel pDebugLevel, final String pMessage, final Throwable pThrowable) {
        switch (pDebugLevel) {
            case NONE:
                return;
            case VERBOSE:
                Debug.v(pMessage, pThrowable);
                return;
            case INFO:
                Debug.i(pMessage, pThrowable);
                return;
            case DEBUG:
                Debug.d(pMessage, pThrowable);
                return;
            case WARNING:
                Debug.w(pMessage, pThrowable);
                return;
            case ERROR:
                Debug.e(pMessage, pThrowable);
                return;
        }
    }

    /**
     * 打印{@link Log#DEBUG}级别日志
     * @see #d(String, String, Throwable)
     * @param pMessage 需要显示的日志
     */
    public static void Print(final String pMessage) {
        Debug.d(pMessage);
    }

    /**
     * 打印{@link Log#DEBUG}级别日志
     * @see #d(String, String, Throwable)
     * @param pTag 需要显示的tag
     * @param pMessage 需要显示的日志
     */
    public static void Print(final String pTag, final String pMessage) {
        Debug.d(pTag, pMessage);
    }

    /**
     * 打印异常的错误日志
     * @param e 异常
     */
    public static void PrintError(Exception e) {
        Debug.w(e);
    }

    /**
     * 设置Debug输出日志等级,默认为{@link #sDebugLevel}
     */
    public static void setDebugLevel(final DebugLevel pDebugLevel) {
        if (pDebugLevel == null) {
            throw new IllegalArgumentException("pDebugLevel must not be null!");
        }
        Debug.sDebugLevel = pDebugLevel;
    }

    /**
     * 设置打印标签TAG,默认为 {@link #sTag}
     * @param tag 设置标签tag
     */
    public static void setDebugTag(String tag) {
        Debug.sTag = tag;
    }

    /**
     * 设置用户
     * @param pDebugUser
     */
    public static void setDebugUser(final String pDebugUser) {
        if (pDebugUser == null) {
            throw new IllegalArgumentException("pDebugUser must not be null!");
        }
        Debug.sDebugUser = pDebugUser;
    }

    /**
     * 设置打印标签TAG,默认为 {@link #sTag}
     * @param tag 设置标签tag
     */
    public static void setTag(final String tag) {
        setDebugTag(tag);
    }

    /**
     * 打印{@link Log#VERBOSE}级别日志
     * @see #v(String, String, Throwable)
     * @param pMessage 需要显示的日志
     */
    public static void v(final String pMessage) {
        Debug.v(Debug.sTag, pMessage, null);
    }

    /**
     * 打印{@link Log#VERBOSE}级别日志
     * @see #v(String, String, Throwable)
     * @param pTag 需要显示的tag
     * @param pMessage 需要显示的日志
     */
    public static void v(final String pTag, final String pMessage) {
        Debug.v(pTag, pMessage, null);
    }

    /**
     * 打印{@link Log#VERBOSE}级别日志
     * @see #v(String, String, Throwable)
     * @param pTag 需要显示的tag
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     */
    public static void v(final String pTag, String pMessage, final Throwable pThrowable) {
        if (Debug.sDebugLevel.isSameOrLessThan(DebugLevel.VERBOSE)) {
            if (pMessage == null) {
                pMessage = "noMsg";
            }
            if (pThrowable == null) {
                Log.v(pTag, pMessage);
            } else {
                Log.v(pTag, pMessage, pThrowable);
            }
        }
    }

    /**
     * 打印{@link Log#VERBOSE}级别日志
     * @see #v(String, String, Throwable)
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     */
    public static void v(final String pMessage, final Throwable pThrowable) {
        Debug.v(Debug.sTag, pMessage, pThrowable);
    }

    /**
     * 打印{@link Log#VERBOSE}级别日志
     * @param pMessage 需要显示的日志
     * @param pDebugUser 用户名称
     */
    public static void vUser(final String pMessage, final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.v(pMessage);
        }
    }

    /**
     * 打印{@link Log#VERBOSE}级别日志
     * @param pTag 日志标签
     * @param pMessage 需要显示的日志
     * @param pDebugUser 用户名称
     */
    public static void vUser(final String pTag, final String pMessage, final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.v(pTag, pMessage);
        }
    }

    /**
     * 打印{@link Log#VERBOSE}级别日志
     * @param pTag 日志标签
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     * @param pDebugUser 用户名称
     */
    public static void vUser(final String pTag, final String pMessage, final Throwable pThrowable,
                             final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.v(pTag, pMessage, pThrowable);
        }
    }

    /**
     * 打印{@link Log#VERBOSE}级别日志
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     * @param pDebugUser 用户名称
     */
    public static void vUser(final String pMessage, final Throwable pThrowable, final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.v(pMessage, pThrowable);
        }
    }

    /**
     * 打印{@link Log#WARN}级别日志
     * @see #w(String, String, Throwable)
     * @param pMessage 需要显示的日志
     */
    public static void w(final String pMessage) {
        Debug.w(Debug.sTag, pMessage, null);
    }

    /**
     * 打印{@link Log#WARN}级别日志
     * @see #w(String, String, Throwable)
     * @param pTag 需要显示的tag
     * @param pMessage 需要显示的日志
     */
    public static void w(final String pTag, final String pMessage) {
        Debug.w(pTag, pMessage, null);
    }

    /**
     * 打印{@link Log#WARN}级别日志
     * @see #w(String, String, Throwable)
     * @param pTag 需要显示的tag
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     */
    public static void w(final String pTag, String pMessage, final Throwable pThrowable) {
        if (Debug.sDebugLevel.isSameOrLessThan(DebugLevel.WARNING)) {
            if (pMessage == null) {
                pMessage = "noMsg";
            }
            if (pThrowable == null) {
                Log.w(pTag, pMessage);
            } else {
                Log.w(pTag, pMessage, pThrowable);
            }
        }
    }

    /**
     * 打印{@link Log#WARN}级别日志
     * @see #w(String, String, Throwable)
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     */
    public static void w(final String pMessage, final Throwable pThrowable) {
        Debug.w(Debug.sTag, pMessage, pThrowable);
    }

    /**
     * 打印{@link Log#WARN}级别日志
     * @see #w(String, String, Throwable)
     * @param pThrowable 抛出的异常信息
     */
    public static void w(final Throwable pThrowable) {
        Debug.w("", pThrowable);
    }

    /**
     * 打印{@link Log#WARN}级别日志
     * @param pMessage 需要显示的日志
     * @param pDebugUser 用户名称
     */
    public static void wUser(final String pMessage, final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.w(pMessage);
        }
    }

    /**
     * 打印{@link Log#WARN}级别日志
     * @param pTag 日志标签
     * @param pMessage 需要显示的日志
     * @param pDebugUser 用户名称
     */
    public static void wUser(final String pTag, final String pMessage, final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.w(pTag, pMessage);
        }
    }

    /**
     * 打印{@link Log#WARN}级别日志
     * @param pTag 日志标签
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     * @param pDebugUser 用户名称
     */
    public static void wUser(final String pTag, final String pMessage, final Throwable pThrowable,
                             final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.w(pTag, pMessage, pThrowable);
        }
    }

    /**
     * 打印{@link Log#WARN}级别日志
     * @param pMessage 需要显示的日志
     * @param pThrowable 抛出的异常信息
     * @param pDebugUser 用户名称
     */
    public static void wUser(final String pMessage, final Throwable pThrowable, final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.w(pMessage, pThrowable);
        }
    }

    /**
     * 打印{@link Log#WARN}级别日志
     * @param pThrowable 抛出的异常信息
     * @param pDebugUser 用户名称
     */
    public static void wUser(final Throwable pThrowable, final String pDebugUser) {
        if (Debug.sDebugUser.equals(pDebugUser)) {
            Debug.w(pThrowable);
        }
    }
}
