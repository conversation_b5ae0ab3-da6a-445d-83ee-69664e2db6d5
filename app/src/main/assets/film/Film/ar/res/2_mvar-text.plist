<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist>
	<array>
		<dict>
			<key>TextCommonStruct</key>
			<dict>
				<key>TextInTimestamp</key>
				<string>0.00</string>
				<key>DefaultSize</key>
				<string>512, 512</string>
				<key>RGBA</key>
				<string>0,0,0,255</string>
				<key>EnableGlobalColor</key>
				<false />
				<key>MaskBgColor</key>
				<string>0,0,0,0</string>
				<key>EnableTextAsMask</key>
				<false />
				<key>WaterMark</key>
				<dict>
					<key>SpacingX</key>
					<string>0.00</string>
					<key>SpacingY</key>
					<string>0.00</string>
					<key>Staggered</key>
					<string>0.00</string>
					<key>Rotate</key>
					<string>0.00</string>
					<key>Scale</key>
					<string>1.00</string>
					<key>Type</key>
					<integer>0</integer>
					<key>AllRotate</key>
					<string>0.00</string>
				</dict>
				<key>Lines</key>
				<array>
					<dict>
						<key>RenderMode</key>
						<integer>0</integer>
						<key>TextString</key>
						<string>04 01 ’25</string>
						<key>InputFlag</key>
						<string>&lt;TIME_BASE_71&gt;</string>
						<key>FontLibrary</key>
						<string>LCDMN___.ttf</string>
						<key>Size</key>
						<string>35.00</string>
						<key>RefFontSize</key>
						<string>0.00</string>
						<key>ORGBA</key>
						<string>100,255,74,2,255</string>
						<key>Bold</key>
						<integer>0</integer>
						<key>Italic</key>
						<integer>0</integer>
						<key>Skew</key>
						<string>0.00,0.00</string>
						<key>Underline</key>
						<integer>0</integer>
						<key>StrikeThrough</key>
						<integer>0</integer>
						<key>Spacing</key>
						<integer>120</integer>
						<key>LineSpacing</key>
						<integer>0</integer>
						<key>HideNonHighlightUnderline</key>
						<integer>0</integer>
						<key>Justify</key>
						<integer>34</integer>
						<key>Rectangle</key>
						<string>0,0,512,256</string>
						<key>Horizontal</key>
						<integer>1</integer>
						<key>LeftToRight</key>
						<integer>1</integer>
						<key>Wrap</key>
						<integer>1</integer>
						<key>Shrink</key>
						<integer>0</integer>
						<key>RepeatToBound</key>
						<integer>0</integer>
						<key>EnableGlyphCenterAdvance</key>
						<integer>0</integer>
						<key>GlyphUnifomAdvance</key>
						<integer>-1</integer>
						<key>RemoveLineBreak</key>
						<integer>0</integer>
						<key>Pinyin</key>
						<integer>0</integer>
						<key>EditingType</key>
						<integer>-1</integer>
						<key>TextEditableProperties</key>
						<dict>
							<key>Editable</key>
							<integer>0</integer>
							<key>SpacingEditable</key>
							<integer>1</integer>
							<key>LineSpacingEditable</key>
							<integer>1</integer>
							<key>HorizontalEditable</key>
							<integer>1</integer>
							<key>VerticalEditable</key>
							<integer>1</integer>
							<key>PinyinEditable</key>
							<integer>1</integer>
						</dict>
						<key>BeginTimestamp</key>
						<integer>0</integer>
						<key>EndTimestamp</key>
						<integer>0</integer>
						<key>RecognizeWords</key>
						<array />
						<key>LayerStyleConfigs</key>
						<array>
							<dict>
								<key>LayerTag</key>
								<string>Glow</string>
								<key>LayerStyle</key>
								<integer>2</integer>
								<key>GlowConfig</key>
								<dict>
									<key>Enable</key>
									<integer>1</integer>
									<key>Editable</key>
									<integer>1</integer>
									<key>Offset</key>
									<string>0.00,0.00</string>
									<key>Scale</key>
									<string>1.00,1.00</string>
									<key>UseTextureOverlayAlpha</key>
									<integer>0</integer>
									<key>GlowVersion</key>
									<integer>2</integer>
									<key>ORGBA</key>
									<string>100,255,74,2,255</string>
									<key>Blur</key>
									<string>4.08</string>
									<key>StrokeWidth</key>
									<string>1.35</string>
									<key>GlowInAlpha</key>
									<string>0.00</string>
								</dict>
							</dict>
							<dict>
								<key>LayerTag</key>
								<string>Stroke</string>
								<key>LayerStyle</key>
								<integer>4</integer>
								<key>StrokeConfig</key>
								<dict>
									<key>Enable</key>
									<integer>1</integer>
									<key>Editable</key>
									<integer>1</integer>
									<key>Offset</key>
									<string>0.00,0.00</string>
									<key>Scale</key>
									<string>1.00,1.00</string>
									<key>UseTextureOverlayAlpha</key>
									<integer>0</integer>
									<key>ORGBA</key>
									<string>100,255,74,2,255</string>
									<key>Size</key>
									<string>0.26</string>
								</dict>
							</dict>
							<dict>
								<key>LayerTag</key>
								<string>text</string>
								<key>LayerStyle</key>
								<integer>3</integer>
								<key>TextLayerConfig</key>
								<dict>
									<key>Enable</key>
									<integer>1</integer>
									<key>Editable</key>
									<integer>1</integer>
									<key>Offset</key>
									<string>0.00,0.00</string>
									<key>Scale</key>
									<string>1.00,1.00</string>
									<key>UseTextureOverlayAlpha</key>
									<integer>0</integer>
									<key>ColorCollection</key>
									<array>
										<string>255,255,255,255</string>
									</array>
									<key>OverlayGradient</key>
									<dict />
								</dict>
							</dict>
						</array>
					</dict>
				</array>
			</dict>
		</dict>
	</array>
</plist>
