<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android">

    <style name="text_universal">
        <item name="android:textColor">@android:color/white</item>
    </style>

    <style name="text_universal_center" parent="text_universal">
        <item name="android:textColor">@android:color/white</item>
        <item name="android:gravity">center</item>
        <item name="android:singleLine">true</item>
    </style>

    <style name="text_normal" parent="text_universal">
        <item name="android:textSize">15dip</item>
        <item name="android:textColor">@color/black</item>
    </style>

    <style name="setting_pop_anim">
        <item name="android:windowEnterAnimation">@anim/setting_pop_show</item>
        <item name="android:windowExitAnimation">@anim/setting_pop_dismiss</item>
    </style>

    <style name="homeBannerAdFeedbackDialog" parent="@style/DialogTheme">
        <item name="android:windowFrame">@null</item><!-- 边框 -->
        <item name="android:windowIsFloating">true</item> <!-- 是否浮现在activity之上 -->
        <item name="android:windowNoTitle">true</item><!-- 无标题 -->
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item> <!-- 模糊 -->
    </style>

    <style name="updateDialog" parent="@style/DialogTheme">
        <item name="android:backgroundDimAmount">0.4</item>
        <item name="android:windowNoTitle">true</item><!-- 无标题 -->
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item> <!-- 模糊 -->
    </style>

    <style name="arDialog" parent="@style/DialogTheme">
        <item name="android:windowFrame">@null</item><!-- 边框 -->
        <item name="android:windowIsFloating">true</item> <!-- 是否浮现在activity之上 -->
        <item name="android:windowNoTitle">true</item><!-- 无标题 -->
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item> <!-- 模糊 -->
        <item name="android:windowAnimationStyle">@style/animation_translate</item>
    </style>

    <style name="animation_translate" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/dialog_enter_anim</item>
        <item name="android:windowExitAnimation">@anim/dialog_exit_anim</item>
    </style>

    <style name="fullScreenDialog" parent="@android:style/Animation.Dialog">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimAmount">0.4</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item> <!-- 模糊 -->
    </style>

    <style name="shareDialog" parent="@android:style/Animation.Dialog">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimAmount">0.4</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item> <!-- 模糊 -->
    </style>

    <style name="up_down_animation" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/dialog_down_up_enter</item>
        <item name="android:windowExitAnimation">@anim/dialog_up_down_exit</item>
    </style>

    <style name="center_zoom_in_animation" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/common_dialog_a_b_in</item>
        <item name="android:windowExitAnimation">@anim/common_dialog_a_b_out</item>
    </style>

    <style name="alpha_in_animation" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/dialog_alpha_in</item>
        <item name="android:windowExitAnimation">@anim/dialog_alpha_out</item>
    </style>

    <style name="guideDialogAnimations" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/dialog_alpha_in</item>
        <item name="android:windowExitAnimation">@anim/dialog_alpha_out</item>
    </style>

    <style name="waitingDialog" parent="updateDialog">
        <item name="android:backgroundDimAmount">0.0</item>
    </style>


    <style name="advertDialog" parent="waitingDialog">
        <item name="android:windowAnimationStyle">@style/advertDialogAnimation</item>
    </style>

    <style name="advertPopWindow" parent="updateDialog">
        <item name="android:windowBackground">@color/color_99000000</item>
    </style>

    <style name="advertDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/beauty_filter_effects_adjust_panel_up</item>
        <item name="android:windowExitAnimation">@anim/setting_pop_dismiss</item>
    </style>

    <style name="LeftRightAnimationActivity" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/slide_right_in</item>
        <item name="android:activityOpenExitAnimation">@anim/slide_left_out</item>
        <item name="android:activityCloseEnterAnimation">@anim/slide_left_in</item>
        <item name="android:activityCloseExitAnimation">@anim/slide_right_out</item>
    </style>

    <style name="AlphaInActivity" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/dialog_alpha_in</item>
        <item name="android:activityOpenExitAnimation">@anim/dialog_alpha_out</item>
        <item name="android:activityCloseEnterAnimation">@anim/dialog_alpha_in</item>
        <item name="android:activityCloseExitAnimation">@anim/dialog_alpha_out</item>
    </style>

    <style name="NoAnimationActivity" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
    </style>

    <style name="CameraAnimationActivity" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/push_up_in</item>
        <item name="android:activityOpenExitAnimation">@anim/push_down_in</item>
        <item name="android:activityCloseEnterAnimation">@anim/slide_left_in</item>
        <item name="android:activityCloseExitAnimation">@anim/slide_right_out</item>
    </style>

    <style name="top_bar">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/top_bar_height</item>
        <item name="android:background">@color/white</item>
    </style>


    <style name="PopwindowAnimStyle">
        <item name="android:windowEnterAnimation">@anim/push_up_in</item>
        <item name="android:windowExitAnimation">@anim/push_down_in</item>
    </style>

    <!-- mask控件 -->
    <declare-styleable name="DrawMaskView">
        <attr name="xpaintColor" format="color" /><!-- 用户涂抹时的画笔颜色 -->
        <attr name="xpaintAlpha" format="integer" /><!-- 用户涂抹时的画笔透明度 -->
        <attr name="xpaintRadius" format="dimension" /><!-- 画笔默认尺寸 -->
        <attr name="eraserColor" format="color" /><!-- 橡皮擦颜色 -->
        <attr name="eraserAlpha" format="integer" /><!-- 橡皮擦透明度 -->
        <attr name="eraserRadius" format="dimension" /><!-- 橡皮檫默认尺寸 -->
        <attr name="xmaskColor" format="color" /><!-- Mask通道颜色 -->
        <attr name="xmaskAlpha" format="integer" /><!-- Mask通道颜色透明度 -->
        <attr name="zoomWindowSizeRate" format="fraction" /><!-- 放大镜宽度和视图的比例 -->
        <attr name="zoomWindowBorderSize" format="dimension" /><!-- 放大镜边框宽度 -->
        <attr name="allowDrag" format="boolean" /><!-- 是否允许拖拽操作 -->
        <attr name="allowScale" format="boolean" /><!-- 是否允许缩放操作 -->
        <attr name="allowDraw" format="boolean" /><!-- 是否允许涂抹和擦除操作 -->
        <attr name="xminScale" format="fraction" /><!-- 图片最小缩放比例 -->
        <attr name="xmaxScale" format="fraction" /><!-- 图片最大缩放比例 -->
        <attr name="adjustAnimDuration" format="integer" /><!-- 图片回弹的持续时间 -->
        <attr name="adjustZoomInInterpolator" format="reference" /><!-- 图片放大动画的插值器 -->
        <attr name="adjustZoomOutInterpolator" format="reference" /><!-- 图片缩小动画的插值器 -->
        <attr name="allowAdjust" format="boolean" /><!-- 是否允许图片自适应视图 -->
        <attr name="showZoomWindow" format="boolean" /><!-- 是否显示放大镜 -->
        <attr name="xshowMask" format="boolean" /><!-- 是否显示Mask图层 -->
    </declare-styleable>

    <!-- tokenProgress dialog的风格-->
    <style name="progress_dialog" parent="@style/DialogTheme">
        <item name="android:windowFrame">@null</item> <!-- 边框 -->
        <item name="android:windowIsFloating">true</item> <!-- 是否浮现在activity之上 -->
        <item name="android:windowNoTitle">true</item> <!-- 无标题 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">false</item> <!-- 模糊 -->
    </style>

    <!-- 该主题用于意见反馈页，由于全屏显示会跟软键盘冲突，所以特别单独出来 -->
    <style name="theme_no_full_screen" parent="MD_Theme">
        <item name="android:windowAnimationStyle">@style/LeftRightAnimationActivity</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!--运营弹窗和图片链接弹窗-->
    <style name="OperateAdDialog" parent="@style/DialogTheme">
        <!-- No backgrounds, titles or window float -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowFullscreen">true</item>
    </style>


    <declare-styleable name="MagicPenGLSurfaceView">

        <!-- The first screen the workspace should display. -->
        <attr name="defaultpen" format="integer" />
    </declare-styleable>

    <style name="Bg_Btn_MainColor_Ripple">
        <item name="android:background">@drawable/selector_bg_btn_main_color</item>
    </style>

    <style name="ActionBar_Circle">
        <item name="android:background">@drawable/selector_bg_actionbar_circle</item>
    </style>

    <style name="ActionBar_Left">
        <item name="android:background">@drawable/selector_bg_actionbar_left</item>
    </style>

    <style name="ActionBar_Right">
        <item name="android:background">@drawable/selector_bg_actionbar_right</item>
    </style>

    <style name="selectable_item">
        <item name="android:background">@drawable/selector_item_bg</item>
    </style>

    <style name="settings_button_style" parent="text_universal_center">
        <item name="android:textSize">15dp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/color_ff3d3d3d</item>
        <item name="android:background">?android:attr/selectableItemBackground</item>
    </style>

    <style name="filter_seekbar">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:max">100</item>
        <item name="android:maxHeight">2dp</item>
        <item name="android:minHeight">2dp</item>
        <item name="android:paddingStart">@dimen/thumb_radus</item>
        <item name="android:paddingEnd">@dimen/thumb_radus</item>
        <item name="android:progress">0</item>
        <item name="android:progressDrawable">@drawable/filter_seekbar_progressdrawable</item>
        <item name="android:thumb">@drawable/filter_seekbar_thumb</item>
        <item name="android:theme">@style/Theme_BeautyActivity</item>
    </style>

    <style name="protocol_theme" parent="@android:style/Animation.Dialog">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimAmount">0.4</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item> <!-- 模糊 -->
    </style>

    <style name="ar_text_style" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">false</item><!--activity不变暗-->
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="dscore_interest_style">
        <item name="android:textColor">@color/color_333333</item>
        <item name="android:textSize">12sp</item>
        <item name="android:layout_centerInParent">true</item>
        <item name="android:enabled">false</item>
        <item name="android:gravity">center</item>
        <item name="android:maxWidth">110dp</item>
    </style>


    <style name="translucent_no_full_screen_orientation" parent="@style/theme_no_full_screen">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:screenOrientation">portrait</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Translucent</item>
    </style>

    <style name="googleAdActivity" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="TextEditPageDialog">
        <item name="android:windowFrame">@null</item><!--边框-->
        <item name="android:windowIsFloating">true</item><!--是否浮现在activity之上-->
        <item name="android:windowNoTitle">true</item><!--无标题-->
        <item name="android:windowAnimationStyle">@style/AlphaInAnimate</item>
    </style>

    <style name="AlphaInAnimate" parent="android:Animation">
        <!--//进入时的动画-->
        <item name="android:windowEnterAnimation">@anim/alpha_300_in</item>
        <!--//退出时的动画-->
        <item name="android:windowExitAnimation">@anim/alpha_300_out</item>
    </style>

    <style name="Transparent" parent="theme">
        <item name="android:windowAnimationStyle">@style/AlphaInActivity</item>
    </style>


    <style name="noAnimation" parent="theme">
        <item name="android:windowAnimationStyle">@style/NoAnimationActivity</item>
    </style>


    <style name="roundCornerImageStyle_16">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">16dp</item>
    </style>


    <style name="roundCornerImageStyle_32">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">32dp</item>
    </style>

    <style name="BottomSheetDialog" parent="Theme.MaterialComponents.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetDialogStyle</item>
    </style>

    <style name="CustomBottomSheetDialog" parent="Theme.MaterialComponents.Light.BottomSheetDialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="bottomSheetStyle">@style/CustomBottomSheetDialogStyle</item>
    </style>

    <style name="CustomBottomSheetDialogStyle" parent="Widget.MaterialComponents.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:backgroundTint">@android:color/transparent</item>
    </style>

    <style name="BottomSheetDialogStyle" parent="Widget.MaterialComponents.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:backgroundTint">@android:color/transparent</item>
    </style>
</resources>