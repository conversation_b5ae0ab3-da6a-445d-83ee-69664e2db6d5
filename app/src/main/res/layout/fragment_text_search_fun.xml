<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.commsource.studio.text.search.TextSearchFunViewModel" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.commsource.search_common.view.SearchLoadMoreLayout
            android:id="@+id/searchLoadMore"
            radiusLeftTop="@{16}"
            radiusRightTop="@{16}"
            solid="@{@color/white}"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="true"
            android:clipChildren="false"
            android:focusable="true"
            android:orientation="vertical"
            app:currentView="@{viewModel.getLoadMoreLayout}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:onClickCommand="@{viewModel.blankClick}">

            <!--搜索栏-->
            <include
                layout="@layout/layout_search_edit"
                android:layout_width="match_parent"
                android:layout_height="52dp"
                android:layout_marginStart="16dp"
                app:viewModel="@{viewModel.searchEditViewModel}" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="52dp">

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <!--最近搜索-->
                    <include
                        layout="@layout/layout_search_history"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        app:viewModel="@{viewModel.searchHistoryViewModel}" />

                    <!--热词-->
                    <include
                        layout="@layout/layout_search_hot"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        app:viewModel="@{viewModel.searchHotViewModel}" />

                </androidx.appcompat.widget.LinearLayoutCompat>

                <!--            列表和加载更多-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    tools:visibility="gone">

                    <FrameLayout
                        android:id="@+id/content"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:clipChildren="false">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@color/white"
                            android:layout_marginEnd="10dp"
                            android:overScrollMode="never"
                            android:visibility="@{viewModel.rvVisibility}"
                            app:currentView="@{viewModel.getRecyclerView}" />

                        <com.commsource.widget.mask.MaskContainer
                            android:id="@+id/mask"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:visibility="@{viewModel.netStatusVisibility}"
                            app:searchStatus="@{viewModel.maskStatus}"
                            app:searchWord="@{viewModel.searchWord}">

                        </com.commsource.widget.mask.MaskContainer>

                    </FrameLayout>


                    <com.commsource.search_common.view.SearchLoadMoreFooter
                        android:id="@+id/footer"
                        android:layout_width="match_parent"
                        android:layout_height="72dp" />
                </LinearLayout>

                <include
                    layout="@layout/layout_search_tags"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:viewModel="@{viewModel.searchTagsViewModel}" />

            </FrameLayout>
        </com.commsource.search_common.view.SearchLoadMoreLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>