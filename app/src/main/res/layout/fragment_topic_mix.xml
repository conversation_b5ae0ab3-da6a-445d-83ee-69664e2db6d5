<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/coordinatorLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            app:elevation="0dp">

            <View
                android:id="@+id/topSpace"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                app:layout_scrollFlags="scroll" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/tabBar"
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:layout_marginBottom="5dp"
                app:layout_constraintTop_toTopOf="parent" />

        </com.google.android.material.appbar.AppBarLayout>

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vp2"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="11dp"
            android:overScrollMode="never"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>