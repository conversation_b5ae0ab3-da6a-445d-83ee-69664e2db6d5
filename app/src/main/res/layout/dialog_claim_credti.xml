<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.cardview.widget.CardView
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="30dp"
            android:layout_gravity="center"
            app:cardBackgroundColor="@color/Gray_Background_2"
            app:cardCornerRadius="20dp">


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/iv_bg"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:layout_gravity="center_horizontal"
                    android:src="@drawable/pop_bg" />

                <ImageView
                    android:id="@+id/iv_credit"
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="24dp"
                    android:src="@drawable/img_coin_3d" />

                <TextView
                    android:id="@+id/tv_credits"
                    android:layout_width="wrap_content"
                    android:layout_height="28dp"
                    android:layout_alignBottom="@id/iv_credit"
                    android:layout_alignEnd="@id/iv_credit"
                    android:layout_marginEnd="4dp"
                    android:layout_marginBottom="10dp"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    android:gravity="center_vertical"
                    android:textColor="@color/white"
                    android:textStyle="bold|italic"
                    android:textSize="20dp"/>


                <com.commsource.widget.BoldTextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="168dp"
                    android:layout_marginEnd="24dp"
                    android:text="@string/t_bp_vip_benefits"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:textSize="18dp" />

                <com.commsource.widget.AutoFitTextView
                    android:id="@+id/tv_content"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/tv_title"
                    android:layout_centerHorizontal="true"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="7dp"
                    android:layout_marginEnd="24dp"
                    android:textColor="@color/color_999999"
                    android:textSize="16dp" />


                <com.commsource.widget.BoldTextView
                    android:id="@+id/tv_got_it"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_below="@id/tv_content"
                    android:layout_centerHorizontal="true"
                    android:layout_margin="24dp"
                    android:gravity="center"
                    android:text="@string/t_got_it"
                    android:textColor="@color/white"
                    android:textSize="16dp"
                    app:boldTextWidth="1.2" />

            </RelativeLayout>
        </androidx.cardview.widget.CardView>

    </FrameLayout>
</layout>