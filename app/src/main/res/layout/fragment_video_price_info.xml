<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        radiusLeftTop="@{16}"
        radiusRightTop="@{16}"
        solid="@{@color/color_1d1d1f}"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="18dp"
            android:textFontWeight="500"
            android:textColor="@color/white"
            android:text="@string/v7110_c_10"
            android:layout_marginStart="20dp"
            android:layout_marginTop="16dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:ignore="UnusedAttribute" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivClose"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/common_close_icon_white_with_bg"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            />

        <androidx.recyclerview.widget.RecyclerView
            radius="@{12}"
            solid="@{@color/Gray_Background_3}"
            android:id="@+id/rvPrice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="16dp"
            android:paddingTop="20dp"
            android:paddingHorizontal="20dp"
            android:overScrollMode="never"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle"
            />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvHint"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginHorizontal="20dp"
            android:textSize="13dp"
            android:textColor="@color/color_999999"
            android:text="@string/v7110_c_11"
            app:layout_constraintTop_toBottomOf="@+id/rvPrice"
            />

        <com.commsource.widget.PressTextView
            android:id="@+id/tvGetIt"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginTop="16dp"
            android:layout_marginHorizontal="20dp"
            android:layout_marginBottom="28dp"
            android:gravity="center"
            android:text="@string/ok"
            android:textColor="@color/white"
            android:textSize="16dp"
            android:textStyle="bold"
            app:layout_constraintTop_toBottomOf="@+id/tvHint"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>