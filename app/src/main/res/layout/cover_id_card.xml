<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 证件照照mask -->
        <com.commsource.widget.CircleImageView
            android:id="@+id/iv_mask"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:scaleType="fitXY"
            android:src="@drawable/selfie_id_ratio_11_icon"
            app:round_radius="20dp" />

        <!-- 倒计时  -->
        <ImageView
            android:id="@+id/iv_time"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_gravity="center"
            android:visibility="gone" />

        <!-- 顶部Bar -->
        <RelativeLayout
            android:id="@+id/rl_top_bar"
            android:layout_width="match_parent"
            android:layout_height="54dp">

            <!-- 返回 -->
            <com.commsource.widget.IconFrontView
                android:id="@+id/mIvBack"
                android:layout_width="35dp"
                android:layout_height="match_parent"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="20dp"
                android:gravity="center"
                android:text="@string/selfie_top_icon_back"
                android:textColor="@color/black"
                android:textSize="35dp"
                app:auto_mirror="true"
                app:stroke_color="@color/black30"
                app:stroke_width="0.5dp"
                app:usePressState="true" />

            <!-- 切换比例 -->
            <com.commsource.widget.IconFrontView
                android:id="@+id/mIvSwitchRatio"
                android:layout_width="35dp"
                android:layout_height="match_parent"
                android:layout_toStartOf="@+id/ifv_flash"
                android:layout_toEndOf="@+id/mIvBack"
                android:gravity="center"
                android:text="@string/selfie_top_icon_3_4"
                android:textColor="@color/black"
                android:textSize="35dp"
                app:stroke_color="@color/black30"
                app:stroke_width="0.5dp"
                app:usePressState="true" />

            <!--切换闪光灯-->
            <com.commsource.widget.IconFrontView
                android:id="@+id/ifv_flash"
                android:layout_width="35dp"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:text="@string/selfie_top_icon_flashalwayson"
                android:textColor="@color/black"
                android:textSize="35dp" />

            <!--定时拍照-->
            <com.commsource.widget.IconFrontView
                android:id="@+id/ifv_count_down"
                android:layout_width="35dp"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_toStartOf="@+id/mIvSwitchCamera"
                android:layout_toEndOf="@+id/ifv_flash"
                android:button="@null"
                android:gravity="center"
                android:text="@string/selfie_setting_icon_time_off"
                android:textColor="@color/black"
                android:textSize="35dp" />

            <!-- 切换相机-->
            <com.commsource.widget.IconFrontView
                android:id="@+id/mIvSwitchCamera"
                android:layout_width="35dp"
                android:layout_height="match_parent"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="20dp"
                android:gravity="center"
                android:text="@string/selfie_top_icon_cameraswitch"
                android:textColor="@color/black"
                android:textSize="35dp"
                app:stroke_color="@color/black30"
                app:stroke_width="0.5dp"
                app:usePressState="true" />

        </RelativeLayout>

        <!-- 拍照按钮 -->
        <FrameLayout
            android:id="@+id/fl_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom">
            <!--拍照按钮-->
            <com.commsource.camera.xcamera.widget.CameraCaptureView
                android:id="@+id/iv_capture"
                android:layout_width="85dp"
                android:layout_height="85dp"
                android:layout_gravity="center" />

        </FrameLayout>

        <!-- 屏蔽点击-->
        <View
            android:id="@+id/vClickBarrier"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="true"
            android:focusable="true"
            android:visibility="gone" />

    </FrameLayout>
</layout>