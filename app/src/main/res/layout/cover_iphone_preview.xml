<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clRoot"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/flTop"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/black"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/splashView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/black"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/flBottom"
            app:layout_constraintTop_toBottomOf="@id/flTop" />

        <View
            android:id="@+id/cameraMask"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/Gray_Background"
            app:layout_constraintBottom_toTopOf="@id/flBottom"
            app:layout_constraintTop_toBottomOf="@id/flTop" />

        <FrameLayout
            android:id="@+id/flBottom"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="bottom"
            android:background="@color/black"
            app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>