<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="225dp"
    android:layout_marginStart="10dp"
    android:layout_marginEnd="10dp"
    android:background="@color/white"
    android:minHeight="225dp"
    android:padding="16dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="25dp"
        android:layout_marginStart="9dp"
        android:layout_marginTop="11dp"
        android:textColor="@color/black"
        android:textSize="16dp"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_title"
        android:orientation="horizontal">

        <com.commsource.widget.wheelview.WheelView
            android:id="@+id/wv_year"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <com.commsource.widget.wheelview.WheelView
            android:id="@+id/wv_month"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <com.commsource.widget.wheelview.WheelView
            android:id="@+id/wv_day"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

    </LinearLayout>

    <TextView
        android:id="@+id/tv_ok"
        android:layout_width="88dp"
        android:layout_height="36dp"
        android:layout_alignParentBottom="true"
        android:layout_alignParentEnd="true"
        android:gravity="center"
        android:text="@string/ok"
        android:textColor="@color/color_ff5986"
        android:textSize="16dp" />

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="88dp"
        android:layout_height="36dp"
        android:layout_alignParentBottom="true"
        android:layout_toStartOf="@id/tv_ok"
        android:gravity="center"
        android:text="@string/cancel"
        android:textColor="@color/color_6e6e6e"
        android:textSize="16dp" />
</RelativeLayout>


