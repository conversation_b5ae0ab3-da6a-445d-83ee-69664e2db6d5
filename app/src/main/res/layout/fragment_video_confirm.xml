<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <RelativeLayout
        android:id="@+id/rl_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:clickable="true"
        android:focusable="true"
        android:orientation="vertical">

        <com.pixocial.videokit.view.XVideoContainer
            android:id="@+id/videoView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true" />

        <FrameLayout
            android:id="@+id/rl_camera_beauty_bottom_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_content"
                android:layout_width="match_parent"
                android:layout_height="85dp"
                android:layout_gravity="center_vertical"
                android:layout_marginBottom="@dimen/camera_button_margin_bottom"
                android:gravity="center">

                <!--返回-->
                <RelativeLayout
                    android:id="@+id/btn_back"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="12dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/rl_gif"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.commsource.widget.IconFrontView
                        android:id="@+id/back_btn"
                        android:layout_width="35dp"
                        android:layout_height="35dp"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="15dp"
                        android:duplicateParentState="true"
                        android:gravity="center"
                        android:text="@string/selfie_save_icon_back"
                        android:textColor="@color/black"
                        android:textSize="35dp"
                        app:auto_mirror="true"
                        app:show_stroke="true"
                        app:stroke_color="@color/color_4d000000"
                        app:usePressState="true" />

                    <com.commsource.widget.PressStrokeTextView
                        android:id="@+id/back_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/back_btn"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="2dp"
                        android:duplicateParentState="true"
                        android:text="@string/back"
                        android:textColor="@color/black"
                        android:textSize="@dimen/selfie_func_text_size" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_gif"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="8dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/btn_save_and_back"
                    app:layout_constraintStart_toEndOf="@+id/btn_back"
                    app:layout_constraintTop_toTopOf="parent">

                    <ProgressBar
                        android:id="@+id/pb_loading"
                        android:layout_width="35dp"
                        android:layout_height="35dp"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="15dp"
                        android:indeterminateDrawable="@drawable/progress_arvideo_loading"
                        android:visibility="gone" />

                    <com.commsource.widget.IconFrontView
                        android:id="@+id/gif_btn"
                        android:layout_width="35dp"
                        android:layout_height="35dp"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="15dp"
                        android:gravity="center"
                        android:text="@string/selfie_save_icon_gif"
                        android:textSize="35dp"
                        app:show_stroke="true"
                        app:stroke_color="@color/color_4d000000"
                        app:usePressState="true" />

                    <com.commsource.widget.PressStrokeTextView
                        android:id="@+id/gif_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="50dp"
                        android:duplicateParentState="true"
                        android:text="@string/gif"
                        android:textSize="@dimen/selfie_func_text_size" />

                </RelativeLayout>

                <com.commsource.camera.xcamera.widget.CameraSaveLoadingView
                    android:id="@+id/btn_save_and_back"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <!--分享按钮-->
                <RelativeLayout
                    android:id="@+id/btn_video_edit"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="8dp"
                    android:clipChildren="false"
                    app:layout_constraintEnd_toStartOf="@+id/btn_save_and_share"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toEndOf="@+id/btn_save_and_back"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.airbnb.lottie.LottieAnimationView
                        android:id="@+id/video_btn"
                        android:layout_width="35dp"
                        android:layout_height="35dp"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="15dp"
                        android:duplicateParentState="true"
                        android:gravity="center" />

                    <com.commsource.widget.PressStrokeTextView
                        android:id="@+id/video_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/video_btn"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="2dp"
                        android:duplicateParentState="true"
                        android:gravity="center"
                        android:text="@string/t_video_edit"
                        android:textColor="@color/black"
                        android:textSize="@dimen/selfie_func_text_size" />

                </RelativeLayout>

                <!--分享按钮-->
                <RelativeLayout
                    android:id="@+id/btn_save_and_share"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="12dp"
                    android:clipChildren="false"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toEndOf="@+id/btn_video_edit"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.commsource.widget.IconFrontView
                        android:id="@+id/share_btn"
                        android:layout_width="35dp"
                        android:layout_height="35dp"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="15dp"
                        android:duplicateParentState="true"
                        android:gravity="center"
                        android:text="@string/selfie_save_icon_share"
                        android:textColor="@color/black"
                        android:textSize="35dp"
                        app:show_stroke="true"
                        app:stroke_color="@color/color_4d000000"
                        app:usePressState="true" />

                    <com.commsource.widget.PressStrokeTextView
                        android:id="@+id/share_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/share_btn"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="2dp"
                        android:duplicateParentState="true"
                        android:text="@string/share"
                        android:textColor="@color/black"
                        android:textSize="@dimen/selfie_func_text_size" />

                </RelativeLayout>

                <ImageView
                    android:id="@+id/iv_video_new"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="40dp"
                    android:layout_marginTop="10dp"
                    android:src="@drawable/selfie_new_tag"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/btn_video_edit"
                    app:layout_constraintTop_toTopOf="@id/btn_video_edit" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </FrameLayout>

        <com.commsource.ad.ADContainer
            android:id="@+id/bannerAdContainer"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@color/white"
            android:visibility="gone" />

        <RelativeLayout
            android:id="@+id/btn_sound"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="12dp"
            android:clipChildren="false">

            <com.commsource.widget.IconFrontView
                android:id="@+id/sound_btn"
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:layout_centerHorizontal="true"
                android:duplicateParentState="true"
                android:gravity="center"
                android:text="@string/selfie_save_icon_music_on"
                android:textColor="@color/black"
                android:textSize="35dp"
                app:show_stroke="true"
                app:stroke_color="@color/color_4d000000"
                app:usePressState="true" />

        </RelativeLayout>

        <ViewStub
            android:id="@+id/vs_story_share_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout="@layout/share_fb_story_tip_layout"
            android:visibility="gone" />

        <!--付费滤镜提示-->
        <LinearLayout
            android:id="@+id/ll_paid_filter"
            android:layout_width="wrap_content"
            android:layout_height="34dp"
            android:layout_above="@+id/rl_camera_beauty_bottom_container"
            android:layout_marginBottom="70dp"
            android:background="@drawable/filter_paid_btn_bg_shape_normal"
            android:minWidth="100dp"
            android:orientation="horizontal"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_paid_filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:gravity="center"
                android:text="@string/paid_filter"
                android:textColor="@color/white"
                android:textSize="12dp" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="5dp"
                android:src="@drawable/arrow_right_icon" />

        </LinearLayout>

        <View
            android:id="@+id/share_mask"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/color_4d000000"
            android:visibility="gone" />

        <FrameLayout
            android:id="@+id/fragment_save_share"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentBottom="true" />

        <!--拍后视频页提示-->
        <com.commsource.comic.widget.StrokeTextView
            android:id="@+id/tv_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:visibility="gone" />

        <!-- 屏蔽屏幕点击事件-->
        <View
            android:id="@+id/v_click_mask"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="true"
            android:visibility="gone" />

    </RelativeLayout>

</layout>