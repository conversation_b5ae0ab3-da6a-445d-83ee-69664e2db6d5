<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/rlOptionBar"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_gravity="top|center_horizontal"
            android:clickable="true"
            android:orientation="horizontal"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:visibility="invisible">

            <com.commsource.widget.IconFrontView
                android:id="@+id/ifvZoom"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:gravity="center"
                android:text="@string/filmcam_tool_framemode"
                android:textColor="@color/Gray_E"
                android:textSize="40dp" />

            <Space
                android:id="@+id/space1"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />

            <com.commsource.widget.IconFrontView
                android:id="@+id/ifvWatermark"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:gravity="center"
                android:text="@string/filmcam_tool_timestamp24"
                android:textColor="@color/Gray_E"
                android:textSize="40dp" />

            <Space
                android:id="@+id/space2"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />

            <com.commsource.widget.IconFrontView
                android:id="@+id/ifvBeautify"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:gravity="center"
                android:text="@string/filmcam_tool_beauty"
                android:textColor="@color/Gray_E"
                android:textSize="40dp" />

            <Space
                android:id="@+id/space3"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />

            <com.commsource.widget.IconFrontView
                android:id="@+id/ifvFunction"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:gravity="center"
                android:text="@string/filmcam_tool_double_exposure"
                android:textColor="@color/Gray_E"
                android:textSize="40dp" />

            <Space
                android:id="@+id/space4"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />

            <com.commsource.widget.IconFrontView
                android:id="@+id/ifvFlash"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:gravity="center"
                android:text="@string/filmcam_tool_flash_close"
                android:textColor="@color/Gray_E"
                android:textSize="40dp" />

        </LinearLayout>

    </FrameLayout>

</layout>