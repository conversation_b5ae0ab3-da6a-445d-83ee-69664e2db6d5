<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/notification1"
            radius="@{12f}"
            solid="@{@color/Gray_Background}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="12dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:text="@string/ai_notifications_guide"
                android:textColor="@color/Gray_B"
                android:textSize="12dp" />

            <com.commsource.widget.BoldTextView
                android:id="@+id/trunOn"
                radius="@{20f}"
                solid="@{@color/white}"
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:layout_gravity="center"
                android:layout_marginStart="16dp"
                android:gravity="center_vertical"
                android:paddingHorizontal="14dp"
                android:text="@string/ai_turn_on"
                android:textColor="@color/Gray_A"
                android:textSize="12dp" />
        </LinearLayout>

        <TextView
            android:id="@+id/prompt1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawablePadding="12dp"
            android:gravity="center_vertical"
            android:text="@string/ai_wait_processing_tips"
            android:textColor="@color/Gray_B"
            android:textSize="12dp"
            app:drawableStartCompat="@drawable/repair_prompt_time" />

        <TextView
            android:id="@+id/prompt2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:drawablePadding="12dp"
            android:gravity="center_vertical"
            android:text="@string/ai_saved_days_tips"
            android:textColor="@color/Gray_B"
            android:textSize="12dp"
            app:drawableStartCompat="@drawable/repair_prompt_download" />


    </LinearLayout>
</layout>