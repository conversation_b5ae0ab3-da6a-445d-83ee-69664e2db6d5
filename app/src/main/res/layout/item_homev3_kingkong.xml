<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:id="@+id/feature_container"
        radius="@{16f}"
        solid="@{@color/white}"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:orientation="vertical">

        <FrameLayout
            android:id="@+id/rvContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_feature"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <View
                android:id="@+id/rv_barrier"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/expand_icon_fl"
            android:layout_width="30dp"
            android:layout_height="28dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="2dp">

            <com.commsource.widget.IconFrontView
                android:id="@+id/expand_icon"
                radius="@{15f}"
                solid="@{@color/Gray_O}"
                android:layout_width="30dp"
                android:layout_height="16dp"
                android:gravity="center"
                android:src="@drawable/arrow_down_icon_black"
                android:text="@string/home_ic_function_arrow"
                android:textColor="@color/Gray_A"
                android:textSize="16dp" />

            <View
                android:id="@+id/expand_icon_area"
                android:layout_width="30dp"
                android:layout_height="28dp" />
        </FrameLayout>


    </LinearLayout>
</layout>