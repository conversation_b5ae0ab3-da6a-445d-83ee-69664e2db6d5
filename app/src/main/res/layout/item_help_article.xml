<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <FrameLayout
        android:id="@+id/flArticle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.commsource.widget.AutoFitTextView
            android:id="@+id/tv_article"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="28dp"
            android:layout_marginBottom="12dp"
            android:gravity="center_vertical"
            android:maxLines="2"
            android:textColor="@color/Gray_D"
            android:textSize="14dp" />

        <View
            android:id="@+id/divideLine"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_gravity="bottom"
            android:background="@color/Gray_Dashline" />

    </FrameLayout>

</layout>