<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <import type="com.commsource.studio.StudioLayoutConstants" />
    </data>

    <FrameLayout
        android:id="@+id/fl_bottom"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/fl_insert"
            radiusLeftTop="@{16}"
            radiusRightTop="@{16}"
            solid="@{@color/color_f2f2f2}"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_gravity="bottom">

            <FrameLayout
                android:id="@+id/fl_insert_click"
                android:layout_width="match_parent"
                android:layout_height="44dp">

                <com.commsource.widget.PressAutoFitTextView
                    android:id="@+id/tvInsert"
                    android:layout_width="wrap_content"
                    android:layout_height="44dp"
                    android:layout_gravity="center_horizontal"
                    android:drawableStart="@drawable/edit_add_object_icon"
                    android:drawablePadding="8dp"
                    android:gravity="center"
                    android:text="@string/t_material_library"
                    android:textColor="@color/Gray_A"
                    android:textSize="14dp" />

            </FrameLayout>

        </FrameLayout>

        <LinearLayout
            android:id="@+id/ll_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_gravity="bottom|center_horizontal"
            android:translationY="124dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_tips"
                radius="@{16}"
                solid="@{@color/color_fb5986}"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:gravity="center"
                android:paddingStart="13dp"
                android:paddingEnd="13dp"
                android:text="@string/t_explore_unlimited"
                android:textColor="@color/white"
                android:textSize="13dp" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="-3dp"
                android:src="@drawable/common_triangle_icon_down_pink" />

        </LinearLayout>

        <!-- 插入面板 -->
        <FrameLayout
            android:id="@+id/fl_sub_insert"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <!--Tab层级Fragment的容器-->
        <FrameLayout
            android:id="@+id/fl_tab_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <!-- 一级界面可收起工具栏 和fl_sub_insert 作用一样 只是insert那个界面和这个不属于一个UI层级 驱动动画和结构是一致的 -->
        <FrameLayout
            android:id="@+id/fl_sub_tab"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <!-- 图片选择 -->
        <com.commsource.studio.PictureSelectView
            android:id="@+id/pictureSelect"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <!-- 二级功能栏  -->
        <FrameLayout
            android:id="@+id/fl_sub_bottom"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}" />


        <ImageView
            android:id="@+id/iv_blur"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}"
            android:visibility="gone" />

        <!-- 编组气泡提示  -->
        <LinearLayout
            android:id="@+id/tipsBubbleView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="77dp"
            android:layout_marginStart="14dp"
            android:layout_gravity="start|bottom"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:id="@+id/tip_content"
                radius="@{20f}"
                solid="@{@color/color_fa64b0}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:gravity="center"
                android:paddingStart="12dp"
                android:paddingTop="8dp"
                android:paddingEnd="12dp"
                android:paddingBottom="8dp"
                android:text="@string/t_guide_layer_group"
                android:textColor="@color/white"
                android:textSize="13dp" />

            <ImageView
                android:id="@+id/iv_bubble_indictor"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:rotation="180"
                android:src="@drawable/editor_bubble_indictor_up" />

        </LinearLayout>

    </FrameLayout>

</layout>