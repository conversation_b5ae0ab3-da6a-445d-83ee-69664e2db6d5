<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        radiusLeftTop="@{20}"
        radiusRightTop="@{20}"
        solid="@{@color/gray_f1f1f5}"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginStart="16dp"
            android:textSize="24dp"
            android:textStyle="bold"
            android:textFontWeight="700"
            android:textColor="@color/Gray_A"
            android:text="@string/t_tools"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            />

        <ImageView
            android:id="@+id/ic_close"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/premium_icon_close_gray"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_func"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="16dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"
            app:layout_constraintBottom_toTopOf="@+id/fl_bottom"
            />

        <FrameLayout
            android:id="@+id/fl_bottom"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/bg_button_start_editing"
            app:layout_constraintBottom_toBottomOf="parent"
            >

            <com.commsource.widget.PressTextView
                android:id="@+id/tv_start_editing"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/t_start_editing"
                android:textColor="@color/Gray_A"
                android:textSize="16dp"
                android:textStyle="italic"
                android:textFontWeight="700"
                android:drawableStart="@drawable/ic_start_edit"
                android:drawablePadding="8dp"
                />

        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>