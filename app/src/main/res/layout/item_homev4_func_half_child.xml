<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clItem"
        android:layout_width="match_parent"
        android:layout_height="72dp">

        <View
            android:id="@+id/bg"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_marginTop="12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <ImageView
            android:id="@+id/iv"
            android:layout_width="28dp"
            android:layout_height="28dp"
            app:layout_constraintStart_toStartOf="@id/bg"
            app:layout_constraintTop_toTopOf="@id/bg" />

        <com.commsource.widget.AutoFitTextView
            android:id="@+id/tvName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="5dp"
            android:layout_marginTop="12dp"
            android:gravity="center_horizontal"
            android:maxLines="2"
            android:textColor="@color/Gray_A"
            android:textSize="11dp"
            android:textFontWeight="500"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/bg" />

        <View
            android:id="@+id/ivRedPoint"
            shape="@{1}"
            solid="@{@color/color_FF3725}"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/bg"
            app:layout_constraintTop_toTopOf="@id/bg"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvCustom"
            radius="@{6f}"
            solid="@{@color/color_ff78bc}"
            android:layout_width="wrap_content"
            android:layout_height="11dp"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="-5dp"
            android:gravity="center_horizontal|end"
            android:paddingStart="3dp"
            android:paddingEnd="3dp"
            android:textColor="@color/white"
            android:textSize="7dp"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="@id/bg"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/bg"
            tools:text="Custom"
            tools:visibility="gone" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>