<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/rlBottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom">

            <com.commsource.home.work.SimpleMediaController
                android:id="@+id/mediaController"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_above="@id/content_container"
                android:layout_marginBottom="6dp"
                android:visibility="gone" />

            <RelativeLayout
                android:id="@+id/content_container"
                radiusLeftTop="@{20}"
                radiusRightTop="@{20}"
                solid="@{@color/Gray_Background}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_gravity="bottom"
                android:layout_marginTop="50dp"
                android:orientation="vertical"
                android:padding="16dp">

                <com.commsource.widget.BoldTextView
                    android:id="@+id/tv_time"
                    android:layout_width="wrap_content"
                    android:layout_height="25dp"
                    android:gravity="center"
                    android:textColor="@color/Gray_A"
                    android:textSize="18dp" />

                <TextView
                    android:id="@+id/tv_frame"
                    android:layout_width="wrap_content"
                    android:layout_height="20dp"
                    android:layout_below="@+id/tv_time"
                    android:layout_marginTop="4dp"
                    android:gravity="center"
                    android:textColor="@color/Gray_B"
                    android:textSize="14dp" />

                <ImageView
                    android:id="@+id/ifv_close"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignParentEnd="true"
                    android:gravity="center"
                    android:src="@drawable/all_image_close_icon" />

                <LinearLayout
                    radius="@{12}"
                    solid="@{@color/white}"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_frame"
                    android:layout_marginTop="16dp"
                    android:orientation="vertical">

                    <FrameLayout
                        android:id="@+id/fl_edit"
                        android:layout_width="match_parent"
                        android:layout_height="50dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="start|center_vertical"
                            android:layout_marginStart="16dp"
                            android:text="@string/t_edit"
                            android:textColor="@color/Gray_A"
                            android:textSize="16dp"
                            android:textStyle="bold" />

                        <com.commsource.widget.IconFrontView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_gravity="center_vertical|end"
                            android:layout_marginEnd="16dp"
                            android:gravity="center"
                            android:text="@string/all_icon_indicator_forward_16"
                            android:textColor="@color/Gray_A"
                            android:textSize="16dp"
                            app:auto_mirror="true" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1px"
                            android:layout_gravity="bottom"
                            android:background="@color/Gray_Dashline" />

                    </FrameLayout>

                    <FrameLayout
                        android:id="@+id/fl_export"
                        android:layout_width="match_parent"
                        android:layout_height="50dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="start|center_vertical"
                            android:layout_marginStart="16dp"
                            android:text="@string/t_export"
                            android:textColor="@color/Gray_A"
                            android:textSize="16dp"
                            android:textStyle="bold" />

                        <com.commsource.widget.IconFrontView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_gravity="center_vertical|end"
                            android:layout_marginEnd="16dp"
                            android:gravity="center"
                            android:text="@string/all_icon_indicator_forward_16"
                            android:textColor="@color/Gray_A"
                            android:textSize="16dp"
                            app:auto_mirror="true" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1px"
                            android:layout_gravity="bottom"
                            android:background="@color/Gray_Dashline" />

                    </FrameLayout>

                    <FrameLayout
                        android:id="@+id/fl_duplicate"
                        android:layout_width="match_parent"
                        android:layout_height="50dp">

                        <TextView
                            android:id="@+id/tv_copy"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="start|center_vertical"
                            android:layout_marginStart="16dp"
                            android:text="@string/t_copy"
                            android:textColor="@color/Gray_A"
                            android:textSize="16dp"
                            android:textStyle="bold" />

                        <com.commsource.widget.IconFrontView
                            android:id="@+id/iv_indicator"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_gravity="center_vertical|end"
                            android:layout_marginEnd="16dp"
                            android:gravity="center"
                            android:text="@string/edit_icon_optbar_share"
                            android:textColor="@color/Gray_A"
                            android:textSize="16dp"
                            android:textStyle="bold"
                            app:show_stroke="true"
                            app:stroke_color="@color/Gray_A"
                            app:stroke_width="1px" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1px"
                            android:layout_gravity="bottom"
                            android:background="@color/Gray_Dashline" />

                    </FrameLayout>

                    <FrameLayout
                        android:id="@+id/fl_delete"
                        android:layout_width="match_parent"
                        android:layout_height="50dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="start|center_vertical"
                            android:layout_marginStart="16dp"
                            android:text="@string/album_preview_dialog_delete"
                            android:textColor="@color/Premium_Red"
                            android:textSize="16dp"
                            android:textStyle="bold" />

                        <com.commsource.widget.IconFrontView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center_vertical|end"
                            android:layout_marginEnd="14dp"
                            android:gravity="center"
                            android:text="@string/edit_icon_optbar_delete"
                            android:textColor="@color/Premium_Red"
                            android:textSize="20dp"
                            android:textStyle="bold"
                            app:show_stroke="true"
                            app:stroke_color="@color/Premium_Red"
                            app:stroke_width="1px" />

                    </FrameLayout>

                </LinearLayout>

            </RelativeLayout>

        </FrameLayout>

    </FrameLayout>

</layout>