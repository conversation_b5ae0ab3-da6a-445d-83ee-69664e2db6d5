<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Layer 1: 深色内阴影边框 (Bottom Layer) -->
    <!-- 模拟 #000000 at 8% 的效果 -->
    <!-- 我们创建一个比按钮大一点的形状，并用一个深色半透明的边框 -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="114dp" /> <!-- 一个很大的圆角值来确保是胶囊形状 -->
            <stroke
                android:width="1dp"
                android:color="#14000000" /> <!-- 8% alpha of #000000 is approx #14000000 -->
        </shape>
    </item>

    <!-- Layer 2: 浅色高光边框 (Middle Layer) -->
    <!-- 模拟 #FFFFFF at 20% 的效果 -->
    <!-- 我们创建一个略小的形状，用一个白色半透明的边框，并向下偏移一点点 -->
    <item android:bottom="1dp" android:left="1dp" android:right="1dp" android:top="1dp">
        <shape android:shape="rectangle">
            <corners android:radius="114dp" />
            <stroke
                android:width="1.5dp"
                android:color="#33FFFFFF" /> <!-- 20% alpha of #FFFFFF is #33FFFFFF -->
        </shape>
    </item>

    <!-- Layer 3: 主要的背景渐变 (Top Layer) -->
    <!-- 这是Figma中的 Background colors 部分 -->
    <!-- 它需要比边框层再小一点，这样边框才能露出来 -->
    <item android:bottom="2dp" android:left="2dp" android:right="2dp" android:top="2dp">
        <shape android:shape="rectangle">
            <corners android:radius="114dp" />
            <gradient
                android:angle="270"
                android:startColor="#F5F5FA"
                android:endColor="#FFFFFF"
                android:type="linear" />
        </shape>
    </item>

</layer-list>