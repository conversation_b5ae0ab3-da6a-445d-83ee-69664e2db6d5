package com.pixocial.plugin.router.bridge

import org.gradle.api.Project
import java.io.File

/**
 * @Description:
 *
 * @Author: vinvince, @Time: 2024/7/24 10:18
 */
object CompilerUtil {

    private const val ROUTER_BRIDGE = "router-bridge"

    const val ROUTER_API_SUFFIX = "-api"

    const val ROUTER_API_SOURCE = "api"

    const val ROUTER_API = "routerApi"
    const val ROUTER_IMPLEMENTATION = "routerImplementation"
    const val ROUTER_COMPILE_ONLY = "routerCompileOnly"

    fun getRouterApiProject(buildTreePath: String): String {
        return ":$ROUTER_BRIDGE${buildTreePath}$ROUTER_API_SUFFIX"
    }

    fun getRouterApiSourceDir(project: Project, sourceType: String): String {
        return project.projectDir.absolutePath + File.separator + "api" + File.separator + sourceType
    }
}