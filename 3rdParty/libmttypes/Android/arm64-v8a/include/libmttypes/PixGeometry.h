//
//  PixGeometry.h
//  libmttypes
//
//  Created by meitu on 2021/10/29.
//  Copyright © 2021 Ryan. All rights reserved.
//

#ifndef PixGeometry_h
#define PixGeometry_h

#define Pix_POINT_INITIALIZER {0,0}
#define Pix_SIZE_INITIALIZER {0,0}
#define Pix_RECT_INITIALIZER {0, 0, 0, 0}

#if defined(__cplusplus)
#  define CG_INLINE static inline
# elif defined(__GNUC__)
#  define CG_INLINE static __inline__
# else
#  define CG_INLINE static
# endif

#if !defined(CG_EXTERN)
#  if defined(__cplusplus)
#   define CG_EXTERN extern "C"
#  else
#   define CG_EXTERN extern
#  endif
#endif /* !defined(CG_EXTERN) */

namespace MTTypes {

struct PixPoint {
    float x;
    float y;
};
typedef struct PixPoint PixPoint;

/* Sizes. */

struct PixSize {
    float width;
    float height;
};
typedef struct PixSize PixSize;

struct PixRect {
    float x;
    float y;
    float width;
    float height;
};
typedef struct PixRect PixRect;

/* Make a point from `(x, y)'. */

CG_INLINE PixPoint PixPointMake(float x, float y);

/* Make a size from `(width, height)'. */

CG_INLINE PixSize PixSizeMake(float width, float height);

/* Make a rect from `(x, y; width, height)'. */

CG_INLINE PixRect PixRectMake(float x, float y, float width,
                            float height);

/* The "zero" rectangle -- equivalent to PixRectMake(0, 0, 0, 0). */
/*** Definitions of inline functions. ***/

CG_INLINE PixPoint
PixPointMake(float x, float y) {
    PixPoint p;
    p.x = x;
    p.y = y;
    return p;
}

CG_INLINE PixSize
PixSizeMake(float width, float height) {
    PixSize size;
    size.width = width;
    size.height = height;
    return size;
}

CG_INLINE PixRect
PixRectMake(float x, float y, float width, float height) {
    PixRect rect;
    rect.x = x;
    rect.y = y;
    rect.width = width;
    rect.height = height;
    return rect;
}

CG_INLINE bool
__PixPointEqualToPoint(PixPoint point1, PixPoint point2) {
    return point1.x == point2.x && point1.y == point2.y;
}

#define PixPointEqualToPoint __PixPointEqualToPoint

CG_INLINE bool
__PixSizeEqualToSize(PixSize size1, PixSize size2) {
    return size1.width == size2.width && size1.height == size2.height;
}

#define PixSizeEqualToSize __PixSizeEqualToSize

CG_INLINE PixPoint
PixPointMultiplyPixSize(PixPoint point, PixSize size)
{
    return PixPointMake(point.x * size.width, point.y * size.height);
}

CG_INLINE PixSize
PixSizeMultiplyPixSize(PixSize size, PixSize size1)
{
    return PixSizeMake(size.width * size1.width, size.height * size1.height);
}

CG_INLINE PixRect
PixRectMultiplyPixSize(PixRect rect, PixSize size)
{
    return PixRectMake(rect.x * size.width, rect.y * size.height, rect.width * size.width, rect.height * size.height);
}

}
#endif /* PixGeometry_h */
