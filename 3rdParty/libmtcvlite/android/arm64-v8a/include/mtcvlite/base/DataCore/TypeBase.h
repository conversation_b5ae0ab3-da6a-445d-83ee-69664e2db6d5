/*****************************************************************************
* TypeBase.h
***************************************************************************** 
 *
 * 模块: iCloudLibrary/DataCore
 * 作者: Qidong Li       
 * 描述: 定义基本数据类型
 *
 * (c) Copyright by iCloudLibrary Development Group 
 *                 
 * 
 *****************************************************************************
 *
 *参考文献: 
 *
 *
 *****************************************************************************
 *
 * $Revision:  $
 * $Date: 
 *
 */
#pragma once

#ifdef ICL_TEST

#define ICL_EXPORT /*_declspec(dllimport)*/
#define ICL_EXPORT_TEMPLATE

#else

#ifdef ICL_API
#define ICL_EXPORT /*_declspec(dllexport)*/
#define ICL_EXPORT_TEMPLATE /*_declspec(dllexport)*/
#else
#define ICL_EXPORT /*_declspec(dllimport)*/
#define ICL_EXPORT_TEMPLATE
#endif

#endif

// #ifdef ICL_TEST
// #define LOGI printf
// #define LOGE printf
// #else
// #define LOGI /*printf*/
// #define LOGE /*printf*/
// #endif

//#include <string>

//#define ICL_HAVE_TBB

//typedef double dtype_t;
typedef float dtype_t;

namespace mtcvlite
{

namespace ICL
{
typedef float dtype_t;
}
    
} // namespace mtcvlite

#ifdef _MSC_VER
#include <float.h>
#define ICL_TYPE_MAX_VAL FLT_MAX
#define ICL_DBL_MAX DBL_MAX
#else
#define ICL_TYPE_MAX_VAL 3.402823466e+38F
#define ICL_DBL_MAX 1.7976931348623158e+308
#endif


//相关基本数据
/***************************************************************** 
** 数据结构: 常值数据，计算精度、PI值等
** 使用说明: 根据具体要求使用
** 版本说明: V1.0	    
** 修改说明:	
******************************************************************/
#define ICLZeroRange 1.0e-7 //零值表示的范围大小
#define ICLCtrlEps 1.0e-6  //计算控制精度
#define ICLCtrlSquareEps 1.0e-12  //计算控制精度
#define ICLNegativeEps -1.0e6  //计算控制精度
#define ICLPositiveEps 1.0e6  //计算控制精度
#define ICLNegativeEps_F -1.0e6F  //计算控制精度
#define ICLPositiveEps_F 1.0e6F  //计算控制精度

#define ICL_PI 3.1415926535897932384626433832795
#define ICL_PI_F 3.1415926535897932384626433832795F

#define PRECISION_ZERO_RANGE_SQ 1.0e-8F

//#define NULL 0
