//
//  MTNeckSkinToneMatch.h : 面部与颈部亮度匹配实现文件
//
//  Created by 周凡 （<PERSON> Chou）on 2016/11/9.
//

#ifndef _MTCV_LITE_NECK_SKIN_MATCH_H_
#define _MTCV_LITE_NECK_SKIN_MATCH_H_

//#include "MeituBeautifyCommon.h"
#include "MTSkinMatchDefine.h"



#include <stdio.h>
#include <string.h>
#include <vector>

//Float point struct
typedef struct MtPointF
{
	float x;
	float y;
	//MtPointF():x(0),y(0){}
	//MtPointF(float _x,float _y) : x(_x) , y(_y){}
}MtPointF;

//Float rect struct
typedef struct MtRectF
{
	float left, top, width, height;
	//MtRectF(){}
	//MtRectF(float l,float t,float w,float h): left(l),top(t),width(w),height(h){}
}MtRectF;

//Face information
typedef struct MtFaceInformation
{
	//face detected rect
	MtRectF	 rect_face;
	//the left and right eye position
	MtPointF left_eye, right_eye;
	//the position about left and right corner of the mouth
	MtPointF left_mouth_corner, right_mouth_corner;
	//the nose position
	MtPointF nose;
	//if kFaceFacepp39
	MtPointF landmark39[39];
	//if kFaceFreeFace106
	MtPointF landmark106[106];
	float attribute; //人脸置信度-属性
	int is_female;	//是否是女性
}MtFaceInformation;

class CMTNeckSkinToneMatch
{
public:
	CMTNeckSkinToneMatch();
    ~CMTNeckSkinToneMatch();

    // =======================================================
    // Face Neck Match 
    // pSrcY      : NV12 format image  Y Channel
    // pSrcUV     : NV12 format image UV Channel
    // pMask      : 1-channel gray image
    // facePoints : 106 Points vector
    // =======================================================
    status RunNV12(uint8* pSrcY, uint8* pSrcUV, int32 imageWidth, int32 imageHight,
		           const uint8* pMaskImg, int32 maskWidth, int32 maskHight,
			       MtFaceInformation* facePoints, int32 faceCount);  // 106点人脸点

	// 提取颈部Mask 
	// 1. 平滑全图，抠掉脸部皮肤，同时统计被抠掉的脸部皮肤的YUV值。
	// 2. 连通性检测，提取下巴特征点附近的联通区域，并统计均值。
	status MakeNeckMask(uint8* pSrcY, uint8* pSrcUV, int32 imageWidth, int32 imageHight,
		uint8* pMask, int32 maskWidth, int32 maskHight,
		MtFaceInformation* facePoints, int32 faceCount);

	const uint8* GetNeckSkin() const { return m_pNeckSkin; }
	const FNM_Rect<int16>* GetNectRect() const{ return m_pNeckRect; }
	const int* GetEnableFlag() const { return m_pEnable; }

	const YUVPixel<float> *GetFaceYUVAve() const { return m_pFaceAve; }
	const YUVPixel<float> *GetNeckYUVAve() const { return m_pNeckAve; }

	void SetMaskThr(const float fMaskAveThr, const float fMaskVarThr);
   
#if defined(DEBUG_MODE)																	 // 干掉下面的
    // ===== Test Fun =================
    // 验证用，一般不需要调用
    // ================================
    void GetMask(uint8*pMaskImg , int32 maskWidth, int32 maskHight, int32 faceCount);
    YUVPixel<float> GetFaceAve();
    YUVPixel<float> GetNeckAve();
    YUVPixel<float> GetNeckResultAve(uint8* pSrcImg, uint8* pSrcUV, int32 imageWidth, int32 imageHight,
                                     uint8* pMaskImg,int32 maskWidth, int32 maskHight);
#endif

private:
    uint8* m_pNeckSkin;                        // 颈部Mask （多人脸）  
    FNM_Rect<int16> *m_pNeckRect;              // 颈部Mask的范围(多人脸)
    YUVPixel<float> *m_pFaceAve, *m_pNeckAve;  // 人脸的YUV均值（多人脸）
    int *m_pEnable;                           // 操作允许位（多人脸）

	float m_fMaskAveThr, m_fMaskVarThr;

    

    // 执行Mask提亮操作
    // pMask : skin Mask, Neck Mask 由 m_pNeckSkin 提供
    status BrightnessMatch(uint8* pSrcY, uint8* pSrcUV, int32 imageWidth, int32 imageHight,
		                   uint8* pMask, int32 maskWidth, int32 maskHight,
                           int32 faceCount);

    template <typename DType>
    inline bool InsidePolygon(const std::vector< FNM_Point<DType> > &polygon, FNM_Point<DType> p);
};

#endif // MT_NECK_SKIN_MATCH_H_