/*****************************************************************************
 * DataCore.h
 *****************************************************************************
 *
 * 模块: iCloudLibrary/DataCore
 * 作者: Qidong Li
 * 描述: 核心数据类型定义
 *
 * (c) Copyright by iCloudLibrary Development Group
 *
 *
 *****************************************************************************
 *
 *参考文献:
 *
 *
 *****************************************************************************
 *
 * $Revision:  $
 * $Date:
 *
 */
#pragma once

#include "GeometryCore2D.h"
//#include "GeometryCore3D.h"

#include <map>
#include <set>

namespace mtcvlite
{

namespace ICL
{

#ifndef ICL_MIN
#  define ICL_MIN(a,b)  ((a) > (b) ? (b) : (a))
#endif

#ifndef ICL_MAX
#  define ICL_MAX(a,b)  ((a) < (b) ? (b) : (a))
#endif

//template class ICL_EXPORT std::allocator<float>;
//template class ICL_EXPORT_TEMPLATE std::vector<float, std::allocator<float> >;

typedef std::vector<float> TVecFlt;
typedef TVecFlt::iterator TVecFltIt;
typedef TVecFlt::const_iterator TVecFltConIt;

//template  class ICL_EXPORT std::allocator<double>;
//template  class ICL_EXPORT std::vector<double>;
typedef std::vector<double> TVecDou;
typedef TVecDou::iterator TVecDouIt;
typedef TVecDou::const_iterator TDSVecDouConIt;

typedef std::vector<TVecDou> TVec2Dou;
typedef TVec2Dou::iterator TVec2DouIt;
typedef TVec2Dou::const_iterator TVec2DouConIt;

typedef std::vector<dtype_t> TVecDType;
typedef TVecDType::iterator TVecDTypeIt;
typedef TVecDType::const_iterator TVecDTypeConIt;

//template class ICL_EXPORT std::allocator<int>;
//template class ICL_EXPORT std::vector<int, std::allocator<int> >;

typedef std::vector<int>  TVecInt;
typedef TVecInt::iterator TVecIntIt;
typedef TVecInt::const_iterator TVecIntConIt;

typedef std::vector<TVecInt> TVec2Int;
typedef TVec2Int::iterator TVec2IntIt;
typedef TVec2Int::const_iterator TVec2IntConIt;

typedef std::map<int,TVecInt> TMapIntVecInt;
typedef TMapIntVecInt::iterator TMapIntVecIntIt;
typedef TMapIntVecInt::const_iterator TMapIntVecIntConIt;

//template struct ICL_EXPORT std::pair<double,int>;
typedef std::map<double,int> TMapDouInt;
typedef TMapDouInt::iterator TMapDouIntIt;
typedef TMapDouInt::const_iterator TMapDouIntConIt;

// template  class ICL_EXPORT std::allocator<int>;
//template  class ICL_EXPORT std::set<int>;
typedef std::set<int> TSetInt;
typedef TSetInt::iterator TSetIntIt;
typedef TSetInt::const_iterator TSetIntConIt;

typedef std::vector<TSetInt> TVecSetInt;
typedef TVecSetInt::iterator TVecSetIntIt;
typedef TVecSetInt::const_iterator TVecSetIntConIt;

/*****************************************************************
** 数据结构: typedef vector<Point3D> TVec3DPt
** 使用说明: 用标准容器vector存储的点集
** 版本说明: V1.0
** 修改说明:
******************************************************************/
//template  class ICL_EXPORT std::allocator<Point3D>;
//template  class ICL_EXPORT std::vector<Point3D>;
typedef std::vector<Point3D> TVec3DPt; //存储没有法矢的点集
typedef TVec3DPt::iterator TVec3DPtIt;
typedef TVec3DPt::const_iterator TVec3DPtConIt;

/*****************************************************************
** 数据结构: typedef vector<TVec3DPt> TVec23DPt
** 使用说明: 用双容器表示的三维点集
** 版本说明: V1.0
** 修改说明:
******************************************************************/
//template  class ICL_EXPORT std::allocator<TVec3DPt>;
//template  class ICL_EXPORT std::vector<TVec3DPt>;
typedef std::vector<TVec3DPt> TVec23DPt; //存储没有法矢的点集
typedef TVec23DPt::iterator TVec23DPtIt;
typedef TVec23DPt::const_iterator TVec23DPtConIt;

typedef TVec23DPt::reverse_iterator TVec23DPtReIt;

/*****************************************************************
** 数据结构: typedef vector<Vector3D> TVec3DVec
** 使用说明: vector表示的三维向量集
** 版本说明: V1.0
** 修改说明:
******************************************************************/
typedef std::vector<Vector3D> TVec3DVec;
typedef TVec3DVec::iterator TVec3DVecIt;
typedef TVec3DVec::const_iterator TVec3DVecConIt;

/*****************************************************************
** 数据结构: typedef vector<UnVec3D> TVec3DUnV
** 使用说明: vector表示的三维单位向量集
** 版本说明: V1.0
** 修改说明:
******************************************************************/
typedef std::vector<UnVec3D> TVec3DUnV;
typedef TVec3DUnV::iterator TVec3DUnVIt;
typedef TVec3DUnV::const_iterator TVec3DUnVConIt;

/*****************************************************************
** 数据结构: typedef vector<Point3D> TVec3DPtNor
** 使用说明: 用标准容器vector存储带法矢的点集
** 版本说明: V1.0
** 修改说明:
******************************************************************/
//template  class ICL_EXPORT std::allocator<Point3DNor>;
//template  class ICL_EXPORT std::vector<Point3DNor>;

typedef std::vector<Point3DNor> TVec3DPtNor; //带法矢的点集
typedef TVec3DPtNor::iterator TVec3DPtNorIt;
typedef TVec3DPtNor::const_iterator TVec3DPtNorConIt;

/*****************************************************************
** 数据结构: typedef vector<TVec3DPtNor> TVec23DPtNor
** 使用说明: 用双容器vector存储带法矢的点集
** 版本说明: V1.0
** 修改说明:
******************************************************************/
typedef std::vector<TVec3DPtNor> TVec23DPtNor; //带法矢的双容器点集
typedef TVec23DPtNor::iterator TVec23DPtNorIt;
typedef TVec23DPtNor::const_iterator TVec23DPtNorConIt;

//template  class ICL_EXPORT std::allocator<Point3DNorColor>;
//template  class ICL_EXPORT std::vector<Point3DNorColor>;
typedef std::vector<Point3DNorColor> TVec3DPtColor;
typedef TVec3DPtColor::iterator TVec3DPtColorIt;
typedef TVec3DPtColor::const_iterator TVec3DPtColorConIt;

/*****************************************************************
** 数据结构: typedef vector<Point2DNor> TVecPt2DNor
** 使用说明: 用标准容器vector存储带法矢的2维点集
** 版本说明: V1.0
** 修改说明:
******************************************************************/
//template  class ICL_EXPORT std::allocator<Point2DNor>;
//template  class ICL_EXPORT std::vector<Point2DNor>;

typedef std::vector<Point2DNor> TVecPt2DNor; //带法矢的点集
typedef TVecPt2DNor::iterator TVecPt2DNorIt;
typedef TVecPt2DNor::const_iterator TVecPt2DNorConIt;

typedef std::vector<Point2DDir> TVecPt2DDir; //带法矢的点集
typedef TVecPt2DDir::iterator TVecPt2DDirIt;
typedef TVecPt2DDir::const_iterator TVecPt2DDirConIt;

/*****************************************************************
** 数据结构: typedef vector<TVec2DPtNor> TVec2Pt2DNor
** 使用说明: 用双容器vector存储带法矢的2维点集
** 版本说明: V1.0
** 修改说明:
******************************************************************/
typedef std::vector<TVecPt2DNor> TVec2Pt2DNor; //带法矢的双容器点集
typedef TVec2Pt2DNor::iterator TVec2Pt2DNorIt;
typedef TVec2Pt2DNor::const_iterator TVec2Pt2DNorConIt;

typedef std::vector<Point2D>     TVecPt2D;
typedef TVecPt2D::iterator       TVecPt2DIt;
typedef TVecPt2D::const_iterator TVecPt2DConIt;

//template  class ICL_EXPORT std::allocator<Point2Df>;
//template  class ICL_EXPORT std::vector<Point2Df>;

typedef std::vector<Point2Df>     TVecPt2Df;
typedef TVecPt2Df::iterator       TVecPt2DfIt;
typedef TVecPt2Df::const_iterator TVecPt2DfConIt;

//template  class ICL_EXPORT std::allocator<Point2Di>;
//template  class ICL_EXPORT std::vector<Point2Di>;

typedef std::vector<Point2Di>     TVecPt2Di;
typedef TVecPt2Di::iterator       TVecPt2DiIt;
typedef TVecPt2Di::const_iterator TVecPt2DiConIt;

typedef std::vector<UnVec2D>       TVecUVec2D;
typedef TVecUVec2D::iterator       TVecUVec2DIt;
typedef TVecUVec2D::const_iterator TVecUVec2DConIt;

typedef std::vector<UnVec2Df>       TVecUVec2Df;
typedef TVecUVec2Df::iterator       TVecUVec2DfIt;
typedef TVecUVec2Df::const_iterator TVecUVec2DfConIt;

template<typename _Tp> class Size_;
typedef Size_<float> Size2f;

template<typename _Tp> class Size_
{
public:
	typedef _Tp value_type;

	//! various constructors
	Size_();
	Size_(_Tp _width, _Tp _height) : width(_width), height(_height) {}
	Size_(const Size_& sz) : width(sz.width), height(sz.height) {}
	//Size_(const CvSize& sz);
	//Size_(const CvSize2D32f& sz);
	Size_(const Point_nD<_Tp, 2>& pt) : width(pt.x()), height(pt.y()) {}

	Size_& operator = (const Size_& sz) { width = sz.width; height = sz.height; return (*this); }
	//! the area (width*height)
	_Tp area() const { return width * height; }

	//! conversion of another data type.
	//template<typename _Tp2> operator Size_<_Tp2>() const;

	//! conversion to the old-style OpenCV types
	//operator CvSize() const;
	//operator CvSize2D32f() const;

	_Tp width, height; // the width and the height
};

}

} // namespace mtcvlite
