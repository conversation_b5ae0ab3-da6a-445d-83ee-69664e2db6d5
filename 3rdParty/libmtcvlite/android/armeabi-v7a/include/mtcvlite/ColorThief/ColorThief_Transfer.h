
/*****************************************************************
* ColorThief_Transfer 颜色迁移类
* Copyright (c) 2015年 MEITU. All rights reserved.
*
* @version: 1.0
*
* @author:  pxdev(Sunshine)
*
* @date: 2015-11-13
*
* @note: 图像不要过大
*
* @change: 
*
******************************************************************/

//#include "stdafx.h"

//#include "TestModule.h"

namespace mtcvlite
{


	class ColorThief_Transfer{

		/*协方差矩阵对角化*/
		void SEE_3M(float *Matrix3, float *EofM, float *Evalue);

		/*利用矩阵进行颜色迁移*/
		void Transfer(unsigned char *pInImage, int Area, unsigned char *SrcRGBv, unsigned char *DestRGBv, float *FinalMatrix, unsigned char *pOutImage);

		/*矩阵相乘*/
		void MatrixMultiplication(float *m1, float *m2, float *m3, int m, int n, int k);

		/*矩阵转置相乘*/
		void MatrixCrossMultiplication(float *m1, float *m2, float *m3, int m, int n, int k);

		/*获取协方差矩阵*/
		void Get3CovM(unsigned char *Data, int dim, int n, int step, unsigned char *Avg, float *CovM);

		int D_weight_hist[4][256];
	public:

		/*
		@param Src：     4通道源图像
		@param nSrcWidth:   源图宽
		@param nSrcHeight:  源图高
		@param Tar:      4通道指导图
		@param nTarWidth:   指导图宽
		@param nTarHeight:  指导图高
		@param pOut:     迁移后的输出图像

		@return：void
		@brief：功能说明  将保留Src的内容，呈现Tar的颜色特征。
		*/

		void Run(unsigned char *Src, int nSrcWidth, int nSrcHeight,
			unsigned char *Tar, int nTarWidth, int nTarHeight,
			unsigned char *Mask, int nMaskWidth, int nMaskHeight,
			unsigned char *Org, int nOrgWidth, int nOrgHeight,
			unsigned char *pOut);

		void Run_Sample(unsigned char *Src, int nSrcWidth, int nSrcHeight,
			unsigned char *Tar, int nTarWidth, int nTarHeight,
			unsigned char *pOut);

		void Run_gamma(unsigned char *Src, int nSrcWidth, int nSrcHeight,
			unsigned char *Tar, int nTarWidth, int nTarHeight,
			unsigned char *Mask, int nMaskWidth, int nMaskHeight,
			unsigned char *Org, int nOrgWidth, int nOrgHeight,
			unsigned char *pOut);

		void Run_gamma_old(unsigned char *Src, int nSrcWidth, int nSrcHeight,
			unsigned char *Tar, int nTarWidth, int nTarHeight,
			unsigned char *Mask, int nMaskWidth, int nMaskHeight,
			unsigned char *pOut);

		bool Run_gamma_st(unsigned char *Src, int nSrcWidth, int nSrcHeight,
			unsigned char *Tar, int nTarWidth, int nTarHeight,
			unsigned char *Mask,
			unsigned char *tMask,
			unsigned char *pOut);
	};

}