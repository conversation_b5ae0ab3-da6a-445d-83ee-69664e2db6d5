/*****************************************************************
 * PsImageScale ,photo放缩算法，有一般的双线性放缩和锐化平滑放缩
 * Copyright (c) 2015年 MEITU. All rights reserved.
 *
 * @version: 1.0
 *
 * @author:  lym
 *
 * @date: 2015-5-11
 *
 * @note:
 *
 * @usage：
 *
 ******************************************************************/

#ifndef _MTLAB_PSIMAGE_SCALE__lymlymlymlll
#define _MTLAB_PSIMAGE_SCALE__lymlymlymlll

#include "mtlab/common/MeituDefine.h"

#if defined(PLATFORM_IOS)||defined(PLATFORM_ANDROID)||defined(PLATFORM_LINUX)||defined(PLATFORM_WINDOWS)
#define PSThreadOpen 1
#else
#define PSThreadOpen 0
#endif
namespace mtlab {
    
    
    //放缩方式
    enum eScaleMode
    {
        SCALE_BI_LINEAR =0,            //双线性插值
        SCALE_BI_CUBIC_EXPAND,   //平滑（用于放大）
        SCALE_BI_CUBIC_SHRINK,    //锐化（用于缩小）
        SCALE_BI_CUBIC_GRAD        //平滑渐变
    };
    
    class MTLAB_EXPORT PsImageScale
    {
    public:
        PsImageScale();
        ~PsImageScale();
        /*
         @param pSrc：输入图
         @param nSrcWidth: 输入图宽
         @param nSrcHeight: 输入图高
         @param pDst：输出图
         @param nDstWidth: 输出图宽
         @param nDstHeight: 输出图高
         @param nChannels: 输入图的通道数，只能是1或4
         @param stType : 放缩方式，默认为平滑渐变
         @return ：true为放缩正确，false的话情况可能输入输出图传入不正确。
         */
        bool Run(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstWidth, int nDstHeight, int nChannels, eScaleMode stType);
        
    protected:
        ////平滑锐化插值缩放
        void ImageScaleBiCubic(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstWidth, int nDstHeight, int nChannels, eScaleMode stType);
        //双线性差值缩放
        void ImageScaleBilinear(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstWidth, int nDstHeight, int nChannels);
        
    private:
        //立方高的放缩
        void HeightCube(BYTE *pSrc, int nSrcWidth, int nSrcHeight, int nChannles, BYTE *pDst, int nDstHeight, int *YIndex, BYTE *Ydot, short *pTable);
        void HeightGrayCube(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstHeight, int *YIndex, BYTE *Ydot, short *pTable);
        void HeightRGBCube(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstHeight, int *YIndex, BYTE *Ydot, short *pTable);
        void HeightGrayThreadCube(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstHeight, int *YIndex, BYTE *Ydot, short *pTable);
        void HeightRGBThreadCube(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstHeight, int *YIndex, BYTE *Ydot, short *pTable);
        //立方宽的放缩
        void WidthCube(BYTE *pSrc, int nSrcWidth, int nSrcHeight, int nChannles, BYTE *pDst, int nDstWidth, int *XIndex, BYTE *Xdot, short *pTable);
        void WidthGrayCube(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstWidth, int *XIndex, BYTE *Xdot, short *pTable);
        void WidthRGBCube(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstWidth, int *XIndex, BYTE *Xdot, short *pTable);
        void WidthGrayThreadCube(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstWidth, int *XIndex, BYTE *Xdot, short *pTable);
        void WidthRGBThreadCube(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstWidth, int *XIndex, BYTE *Xdot, short *pTable);
        
        //双线性高的放缩
        void HeightBilinear(BYTE *pSrc, int nSrcWidth, int nSrcHeight, int nChannles, BYTE *pDst, int nDstHeight, int *YIndex, BYTE *Ydot);
        void HeightGray(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstHeight, int *YIndex, BYTE *Ydot);
        void HeightRGB(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstHeight, int *YIndex, BYTE *Ydot);
        void HeightGrayThread(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstHeight, int *YIndex, BYTE *Ydot);
        void HeightRGBThread(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstHeight, int *YIndex, BYTE *Ydot);
        //双线性宽的放缩
        void WidthBilinear(BYTE *pSrc, int nSrcWidth, int nSrcHeight, int nChannles, BYTE *pDst, int nDstWidth, int *XIndex, BYTE *Xdot);
        void WidthGray(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstWidth, int *XIndex, BYTE *Xdot);
        void WidthRGB(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstWidth, int *XIndex, BYTE *Xdot);
        void WidthGrayThread(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstWidth, int *XIndex, BYTE *Xdot);
        void WidthRGBThread(BYTE *pSrc, int nSrcWidth, int nSrcHeight, BYTE *pDst, int nDstWidth, int *XIndex, BYTE *Xdot);
        
        //目标图再原图位置的索引
        void DstInSrcIndexCube(int Dst_len, int Src_len, int* a3_pIntValue, BYTE* a4_pByteValue);
        /* @param large_len: 目标宽/高
            @param little_len: 源宽/高
            @param a3_pIntValue: 目标索引值
            @param a4_pByteValue:
         */
        void DstInSrcIndex(int large_len, int little_len, int* a3_pIntValue, BYTE* a4_pByteValue);
        
        //计算一个128*n的表（n为2到20的偶数），n的大小和表的值由放缩比例和放缩方式决定
        void CountTable(short *pTable, int Dst_len, int Srclen, eScaleMode stType);
        void CountExpandTable(short *pTable, float *fparam);
        void CountShrinkTable(short *pTable, float *fparam);
        float ExpandWay(float f);
        float ShrinkWay(float f1, float f2);
        
        int ThreadNum;
    };
}
#endif

