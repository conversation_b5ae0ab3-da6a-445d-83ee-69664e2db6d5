/*****************************************************************
* 高斯双边滤波模糊类
*
*
* @version: 1.0.0
*
* @author:  Ted
*
* @date: 2018-03-13
*
* @change:
*
* @note: 基于高斯双边滤波脚本重写算法，包含普通版本以及多线程版本
*
******************************************************************/
#ifndef _MTLAB__MT_GAUSS_BILATERAL_FILTER_H_
#define _MTLAB__MT_GAUSS_BILATERAL_FILTER_H_

#include <math.h>
#include <pthread.h>
#include "mtlab/common/MeituDefine.h"


namespace mtlab {
    
    
    typedef struct MT_GaussBilateral_Para
    {
        unsigned char* pSrcPartData;
        unsigned char* pDstData;
	float* pWeightList;
        
        int start_row;
        int end_row;
        
        float threshold;
        int srcW;
        int dstW;
        int dstH;
        int step_val;
        
    }MT_GaussBilateral_Para;
    
    class MTLAB_EXPORT CMeituGaussBilateralFilter
    {
    public:
        CMeituGaussBilateralFilter();
        ~CMeituGaussBilateralFilter();
        
        /*
         @param pSrcData： 原图数据，4通道
         @param pDstData： 高斯双边滤波后的结果数据，4通道
         @param width：    图像的宽度
         @param height：   图像的高度
         @param width_step： 采样时水平方向步长
         @param height_step: 采样时垂直方向步长
         @param threshold_perc:   颜色距离阈值百分比，范围为[0.0, 1.0], 取值在[0, 255 * sqrt(3)]之间
         @param brief:   基于水平方向和垂直方向步长，以及颜色距离阈值计算得到高斯双边滤波结果图像
         */
        bool RunCPP(unsigned char* pSrcData, unsigned char* pDstData, int width, int height, int width_step, int height_step, float threshold_perc);
        
        /*
         @param pSrcData： 原图数据，4通道
         @param pDstData： 高斯双边滤波后的结果数据，4通道
         @param width：    图像的宽度
         @param height：   图像的高度
         @param width_step： 采样时水平方向步长
         @param height_step: 采样时垂直方向步长
         @param threshold_perc:   颜色距离阈值百分比，范围为[0.0, 1.0], 取值在[0, 255 * sqrt(3)]之间
         @param brief:   多线程：基于水平方向和垂直方向步长，以及颜色距离阈值计算得到高斯双边滤波结果图像
         */
        bool RunCPP_Mul(unsigned char* pSrcData, unsigned char* pDstData, int width, int height, int width_step, int height_step, float threshold_perc);
        
        /*
         @param pSrcYUY2Data： 原图YUY2数据，2通道
         @param pDstYUY2Data： 高斯双边滤波后的结果数据，2通道
         @param width：    图像的宽度
         @param height：   图像的高度
         @param width_step： 采样时水平方向步长
         @param height_step: 采样时垂直方向步长
         @param threshold_perc:   灰度距离阈值百分比，范围为[0.0, 1.0], 取值在[0, 255]之间
         @param brief:   仅对Y通道进行高斯双边滤波操作，基于水平方向和垂直方向步长，以及灰度距离阈值计算得到高斯双边滤波结果图像
         */
        bool RunYUY2(unsigned char* pSrcYUY2Data, unsigned char* pDstYUY2Data, int width, int height, int width_step, int height_step, float threshold_perc);
        
        /*
         @param pSrcYUY2Data： 原图YUY2数据，2通道
         @param pDstYUY2Data： 高斯双边滤波后的结果数据，2通道
         @param width：    图像的宽度
         @param height：   图像的高度
         @param width_step： 采样时水平方向步长
         @param height_step: 采样时垂直方向步长
         @param threshold_perc:   灰度距离阈值百分比，范围为[0.0, 1.0], 取值在[0, 255]之间
         @param brief:   仅对Y通道进行高斯双边滤波操作，基于水平方向和垂直方向步长，以及灰度距离阈值计算得到高斯双边滤波结果图像
         */
        bool RunYUY2_Mul(unsigned char* pSrcYUY2Data, unsigned char* pDstYUY2Data, int width, int height, int width_step, int height_step, float threshold_perc);
        
        /*
         @param pSrcYChannel： 原图灰度数据，单通道
         @param pDstYChannel： 高斯双边滤波后的结果数据，单通道
         @param width：    图像的宽度
         @param height：   图像的高度
         @param width_step： 采样时水平方向步长
         @param height_step: 采样时垂直方向步长
         @param threshold_perc:   灰度距离阈值百分比，范围为[0.0, 1.0], 取值在[0, 255]之间
         @param brief:   基于水平方向和垂直方向步长，以及灰度距离阈值计算得到高斯双边滤波结果图像
         */
        bool RunYChannel(unsigned char* pSrcYChannel, unsigned char* pDstYChannel, int width, int height, int width_step, int height_step, float threshold_perc);
        
        /*
         @param pSrcYChannel： 原图灰度数据，单通道
         @param pDstYChannel： 高斯双边滤波后的结果数据，单通道
         @param width：    图像的宽度
         @param height：   图像的高度
         @param width_step： 采样时水平方向步长
         @param height_step: 采样时垂直方向步长
         @param threshold_perc:   灰度距离阈值百分比，范围为[0.0, 1.0], 取值在[0, 255]之间
         @param brief:   多线程：基于水平方向和垂直方向步长，以及灰度距离阈值计算得到高斯双边滤波结果图像
         */
        bool RunYChannel_Mul(unsigned char* pSrcYChannel, unsigned char* pDstYChannel, int width, int height, int width_step, int height_step, float threshold_perc);

	/*
	@param pSrcYChannel： 原图灰度数据，单通道
	@param pDstYChannel： 高斯双边滤波后的结果数据，单通道
	@param width：    图像的宽度
	@param height：   图像的高度
	@param sample_interval： 采样时水平/垂直方向步长
	@param brief:   用于祛斑祛痘单通道灰度图高斯双边滤波，阈值比例固定为0.2，权重系数表设置为固定数组
	*/
	bool RunYChannel_for_FleckClean(unsigned char* pSrcYChannel, unsigned char* pDstYChannel, int width, int height, int sample_interval);

	/*
	@param pSrcYChannel： 原图灰度数据，单通道
	@param pDstYChannel： 高斯双边滤波后的结果数据，单通道
	@param width：    图像的宽度
	@param height：   图像的高度
	@param sample_interval： 采样时水平/垂直方向步长
	@param brief:   多线程：用于祛斑祛痘单通道灰度图高斯双边滤波，阈值比例固定为0.2，权重系数表设置为固定数组
	*/
	bool RunYChannel_for_FleckClean_Mul(unsigned char* pSrcYChannel, unsigned char* pDstYChannel, int width, int height, int sample_interval);
    private:
        
        /*
         @param pSrcData： 原图数据，4通道
         @param srcW：     原图的宽度
         @param srcH：     原图的高度
         @param pDstData： 水平方向上高斯双边滤波后的结果数据，4通道
         @param dstW：     结果图像的宽度
         @param dstH：     结果图像的高度
         @param step_val： 采样时水平方向步长
         @param threshold: 颜色距离阈值，取值在[0, 255 * sqrt(3)]之间
         @param brief:   基于水平方向步长，以及颜色距离阈值计算得到高斯双边滤波水平方向滤波结果图像，并且结果数据按列存储
         */
        bool BGRAConv1D(unsigned char* pSrcData, int srcW, int srcH, unsigned char* pDstData, int dstW, int dstH, int step_val, float threshold);
        
        /*
         @param pSrcData： 原图数据，4通道
         @param srcW：     原图的宽度
         @param srcH：     原图的高度
         @param pDstData： 水平方向上高斯双边滤波后的结果数据，4通道
         @param dstW：     结果图像的宽度
         @param dstH：     结果图像的高度
         @param step_val： 采样时水平方向步长
         @param threshold: 颜色距离阈值，取值在[0, 255 * sqrt(3)]之间
         @param brief:   多线程：基于水平方向步长，以及颜色距离阈值计算得到高斯双边滤波水平方向滤波结果图像，并且结果数据按列存储
         */
        bool BGRAConv1D_Mul(unsigned char* pSrcData, int srcW, int srcH, unsigned char* pDstData, int dstW, int dstH, int step_val, float threshold);
        
        /*
         @param pSrcGray： 原图数据，单通道
         @param srcW：     原图的宽度
         @param srcH：     原图的高度
         @param pDstGray： 水平方向上高斯双边滤波后的结果数据，单通道
         @param dstW：     结果图像的宽度
         @param dstH：     结果图像的高度
         @param step_val： 采样时水平方向步长
	@param pWeightList: 基于灰度距离阈值确定的权重系数表
         @param brief:   基于水平方向步长，以及灰度距离阈值计算得到高斯双边滤波水平方向滤波结果图像，并且结果数据按列存储
         */
	bool GrayConv1D(unsigned char* pSrcGray, int srcW, int srcH, unsigned char* pDstGray, int dstW, int dstH, int step_val, float* pWeightList);
        
        /*
         @param pSrcGray： 原图数据，单通道
         @param srcW：     原图的宽度
         @param srcH：     原图的高度
         @param pDstGray： 水平方向上高斯双边滤波后的结果数据，单通道
         @param dstW：     结果图像的宽度
         @param dstH：     结果图像的高度
         @param step_val： 采样时水平方向步长
	@param pWeightList: 基于灰度距离阈值确定的权重系数表
         @param brief:   多线程：基于水平方向步长，以及灰度距离阈值计算得到高斯双边滤波水平方向滤波结果图像，并且结果数据按列存储
         */
	bool GrayConv1D_Mul(unsigned char* pSrcGray, int srcW, int srcH, unsigned char* pDstGray, int dstW, int dstH, int step_val, float* pWeightList);
        
        /*
         @param pSrcData： 原图数据，4通道
         @param srcW：     原图的宽度
         @param srcH：     原图的高度
         @param pDstData： 左右各增加col_pad_val列的结果图像数据，4通道
         @param dstW：     结果图像的宽度
         @param dstH：     结果图像的高度
         @param col_pad_val: 左/右增加的列个数
         @param brief:   左右各扩充col_pad_val列数据，使用填充边缘数据的方式
         */
        void BGRAPadImage(unsigned char* pSrcData, int srcW, int srcH, unsigned char* pDstData, int dstW, int dstH, int col_pad_val);
        
        /*
         @param pSrcGray： 原图数据，单通道
         @param srcW：     原图的宽度
         @param srcH：     原图的高度
         @param pDstGray： 左右各增加col_pad_val列的结果图像数据，单通道
         @param dstW：     结果图像的宽度
         @param dstH：     结果图像的高度
         @param col_pad_val: 左/右增加的列个数
         @param brief:   左右各扩充col_pad_val列数据，使用填充边缘数据的方式
         */
        void GrayPadImage(unsigned char* pSrcGray, int srcW, int srcH, unsigned char* pDstGray, int dstW, int dstH, int col_pad_val);
        
    };
}

#endif // !_MT_GAUSS_BILATERAL_FILTER_H_


