#ifndef _MTLAB__H_FACE_POINT_AND_MASK_H_
#define _MTLAB__H_FACE_POINT_AND_MASK_H_

#include "mtlab/common/Vectors.h"
#include "mtlab/common/MeituDefine.h"

namespace mtlab {
    class MTLAB_EXPORT CFacePointAndMask
    {
    public:
        CFacePointAndMask();
        ~CFacePointAndMask();
        
        /* 获取眼瞳左眼的mask,圆心和半径
            @param eyeCenterLeft: 左眼中心点
            @param eyeLeft:左眼半径
            @param imageWidth: 图像宽度
            @param imageHeight：图像高度
            @param facePoints171: 人脸点
            @param left: 输出左眼睛左边缘x坐标
            @param top: 输出左眼睛上边缘y坐标
            @param right：输出左眼精右边缘x坐标
            @param bottom：输出左眼睛下边缘y坐标
            @param width: 输出左眼宽度
            @param height: 输出左眼高度
            @param r:输出左眼半径
            @param MPoint: 输出左眼中心点
         */
        static unsigned char* GetLeftEyepupilMask(Vector2 eyeCenterLeft,float eyeLeft, int imageWidth, int imageHeight,Vector2* facePoints171,int &left, int &top, int &right, int &bottom, int &width, int &height, float &r, Vector2 &MPoint);
         
        /**获取眼瞳右眼的mask,圆心和半径
         */
        static unsigned char* GetRightEyepupilMask(Vector2 eyeCenterRight,float eyeRight, int imageWidth, int imageHeight,Vector2* facePoints171,int &left, int &top, int &right, int &bottom, int &width, int &height, float &r, Vector2 &MPoint);
              
        /*获取左右眼瞳包含眼白的mask(用于美颜做亮眼的曲线)
             @param facePoints171: 人脸点
             @param sw: 图像宽度
             @param sh: 图像高度
             @param left: 输出眼睛左边缘x坐标
             @param top: 输出眼睛上边缘y坐标
             @param right：输出眼精右边缘x坐标
             @param bottom：输出眼睛下边缘y坐标
             @param width: 输出眼睛宽度
             @param height: 输出眼睛高度
             @param lefrRight:==0:左眼，!=0：右眼
         */
        static unsigned char* GetEyeMask(Vector2* facePoints171,int sw,int sh,int& left,int& top,int &right,int &bottom,int &width,int &height,int leftRight);
          
        //获取眉毛mask
        /*
         annotation added by zhangencai
         @param ResPoint:眉毛点
         @param nWidth:输出mask图像宽
         @param nHeight:输出mask图像高
         @param pMask:输出mask图像
         */
        static bool CalEyeBrowMask(Vector2* ResPoint, int nWidth, int nHeight, BYTE* pMask);
        /*
         @param ResPoint: 171个特征点
         @param nWidth: 图片的宽
         @param nHeight: 图片的高
         @param pMask: 结果mask，传入大小为nWidth*nHeight大小的指针。
         @brief: 获取眼睛，眉毛，眼睛，黑色代表眼睛眉毛嘴巴。
         @return: 是否成功。
         */
        static bool CalEyeMouthEyeBrowMask(Vector2* ResPoint, int nWidth, int nHeight, BYTE* pMask);
        
        
    private:
        float m_Eye_R_left[10];
        
        Vector2 m_Eye_center_left[10];
        
        float m_Eye_R_right[10];
        
        Vector2 m_Eye_center_right[10];
        
    };
    
}
#endif
