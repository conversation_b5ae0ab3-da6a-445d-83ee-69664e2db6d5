/*****************************************************************
 * 皮肤mask蒙版
 *
 *
 * @version: 1.0
 *
 * @date: 2015-04-16
 *
 * @note:
 *
 * @change:
 *
 ******************************************************************/
#ifndef _MTLAB__MT_FACE_SKIN_MASK_H_
#define _MTLAB__MT_FACE_SKIN_MASK_H_
#include "mtlab/common/MeituDefine.h"
#include "mtlab/common/MeituTypes.h"

namespace mtlab {
    
    class MTLAB_EXPORT CFaceSkinMask
    {
    public:
        CFaceSkinMask(void);
        ~CFaceSkinMask(void);
        
        /**
         *  皮肤mask 蒙板，单人脸
         *
         *  @param pSrcImage
         *  @param nSrcWidth
         *  @param nSrcHeight
         *  @param pFaceInfo
         *
         *  @return
         */
        bool Run(BYTE* pSrcImage,int nSrcWidth,int nSrcHeight, MT_FaceInfo* pFaceInfo);
        
        /**
         *  皮肤mask蒙板，多人脸
         *
         *  @param pSrcImage
         *  @param nSrcWidth
         *  @param nSrcHeight
         *  @param nFaceCount
         *  @param pFaceInfo
         *
         *  @return
         */
        bool Run(BYTE* pSrcImage,int nSrcWidth,int nSrcHeight, int nFaceCount, MT_FaceInfo* pFaceInfo);
        
        BYTE* GetBlurSkinMask(int &width,int &height);
        BYTE* GetSkinMask(int &width,int &height);
        void GetAvgFaceColor(BYTE& r,BYTE &g,BYTE &b);
        void CalcFaceAvgColor(BYTE* pData,int width,int height, int nFaceCount, MT_FaceInfo* pFaceInfo);
        BYTE* GetSrcData(int &width ,int &height );
        
        void CreateSkinMask(BYTE* pImage, int nWidth, int nHeight, BYTE* pSkinMask, int nFaceCount, MT_FaceInfo* pFaceInfo, BYTE* pAvgSkinR, BYTE* pAvgSkinG, BYTE* pAvgSkinB);
        
        void ShadowHighlightZcd( BYTE* pSrcData,int width,int height,BYTE* pMask,BYTE* pHightlightMask );
        bool ImageMix(BYTE* pSrcData,BYTE* pDstData,int width,int height,int alpha,int storage,BYTE* pMask);
    private:
        BYTE m_AvgFaceR,m_AvgFaceG,m_AvgFaceB;
        BYTE* m_SkinMask;
        BYTE* m_BlurSkinMask;
        BYTE* m_pSrcData;
        int m_nSrcWidth;
        int m_nSrcHeight;
        int m_SkinWidth;
        int m_SkinHeight;
    };
}

#endif // _MT_ALGO_FACE_BEAUTY_H_
