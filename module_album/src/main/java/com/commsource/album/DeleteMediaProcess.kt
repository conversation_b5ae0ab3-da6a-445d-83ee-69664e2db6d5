package com.commsource.album

import android.app.Activity
import android.content.Intent
import android.provider.MediaStore
import com.commsource.util.AndroidVersionCompat
import com.commsource.util.delegate.DelegateFragment
import com.commsource.util.delegate.DelegateProcess
import com.meitu.common.AppContext
import com.pixocial.library.albumkit.media.MediaInfo

/**
 * 删除Uri请求
 */
abstract class DeleteMediaProcess(val deletes: List<MediaInfo>) : DelegateProcess() {

    override fun onExecute(delegateFragment: DelegateFragment) {
        val list = deletes.filter { it.mediaUri != null }.map { it.mediaUri }
        if (AndroidVersionCompat.isScopedStorage() && list.isNotEmpty()) {
            val pendingIntent =
                MediaStore.createDeleteRequest(AppContext.context.contentResolver, list)
            delegateFragment.startIntentSenderForResult(
                pendingIntent.intentSender,
                101,
                null,
                0,
                0,
                0,
                null
            )
        } else {
            delegateFragment.removeProcess(this)
            onRequestPermissionEnable(true, deletes)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        if (requestCode == 101 && resultCode == Activity.RESULT_OK) {
            onRequestPermissionEnable(true, deletes)
        } else {
            onRequestPermissionEnable(false, emptyList())
        }
        return false
    }

    abstract fun onRequestPermissionEnable(isSuccess: Boolean, deletes: List<MediaInfo>)
}